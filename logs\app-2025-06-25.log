{"level":"INFO","time":"2025-06-25T14:31:12.135+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:12.276+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-06-25T14:31:12.446+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":169,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:12.526+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:12.655+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":128,"影响行数":1}
{"level":"INFO","time":"2025-06-25T14:31:13.076+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":420,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:13.310+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":233,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:13.445+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":134,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:13.705+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":260,"影响行数":1}
{"level":"INFO","time":"2025-06-25T14:31:13.946+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":240,"影响行数":3}
{"level":"INFO","time":"2025-06-25T14:31:14.045+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:14.309+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":263,"影响行数":1}
{"level":"INFO","time":"2025-06-25T14:31:14.505+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":195,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:14.568+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:14.714+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-06-25T14:31:14.829+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:14.939+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:15.463+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":524,"影响行数":1}
{"level":"INFO","time":"2025-06-25T14:31:15.899+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":435,"影响行数":3}
{"level":"INFO","time":"2025-06-25T14:31:15.985+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:16.133+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":148,"影响行数":1}
{"level":"INFO","time":"2025-06-25T14:31:16.305+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":170,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:16.396+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:16.535+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-06-25T14:31:16.739+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":203,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:17.383+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":644,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:18.022+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":637,"影响行数":1}
{"level":"INFO","time":"2025-06-25T14:31:18.171+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":148,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:18.334+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":162,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:18.492+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-25T14:31:18.635+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":143,"影响行数":-1}
{"level":"INFO","time":"2025-06-25T14:31:18.635+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-25T14:31:18.775+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-25T14:31:18.778+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-25T14:31:18.776+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-25T14:31:18.779+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-25T18:36:53.189+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-25T18:36:53.191+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-25T18:36:53.191+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
