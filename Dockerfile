FROM golang:1.19-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置Go环境变量以减小二进制大小
ENV CGO_ENABLED=0 
ENV GOOS=linux 
ENV GOARCH=amd64
ENV GO111MODULE=on

# 安装必要的构建工具
RUN apk add --no-cache tzdata git

# 复制go.mod和go.sum
COPY go.mod go.sum ./
# 下载依赖，利用Docker缓存层
RUN go mod download && go mod verify

# 复制源代码
COPY . .

# 构建应用
# -ldflags 减小二进制体积
RUN go build -ldflags="-s -w" -o /app/bin/server ./main.go

# 使用scratch作为基础镜像，最小化最终镜像大小
FROM scratch

# 复制时区数据
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
# 设置时区
ENV TZ=Asia/Shanghai

# 设置工作目录
WORKDIR /app

# 复制配置文件
COPY --from=builder /app/config ./config
COPY --from=builder /app/.env ./

# 复制二进制文件
COPY --from=builder /app/bin/server .

# 创建日志目录
WORKDIR /app/logs
RUN mkdir -p .
WORKDIR /app

# 暴露端口
EXPOSE 8888

# 运行应用
CMD ["./server"] 