package mqtt

import (
	"fmt"
	"sync"
	"time"

	"eyc3_meeting/config"
	"eyc3_meeting/internal/pkg/logger"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

var (
	client     mqtt.Client
	handlers   = make(map[string]mqtt.MessageHandler)
	handlersMu sync.RWMutex
)

// HandlerFunc MQTT消息处理函数
type HandlerFunc func(client mqtt.Client, msg mqtt.Message)

// Init 初始化MQTT客户端
func Init() {
	mqttConfig := config.AppConfig.MQTT

	// 创建客户端配置
	opts := mqtt.NewClientOptions()
	opts.AddBroker(mqttConfig.Broker)
	opts.SetClientID(mqttConfig.ClientID)

	if mqttConfig.Username != "" {
		opts.SetUsername(mqttConfig.Username)
		opts.SetPassword(mqttConfig.Password)
	}

	// 设置重连
	opts.SetAutoReconnect(true)
	opts.SetMaxReconnectInterval(5 * time.Second)
	opts.SetKeepAlive(60 * time.Second)
	opts.SetPingTimeout(3 * time.Second)
	opts.SetConnectTimeout(5 * time.Second)

	// 设置连接/断开回调
	opts.SetOnConnectHandler(func(c mqtt.Client) {
		logger.Info("MQTT连接成功")

		// 重新订阅所有主题
		handlersMu.RLock()
		for topic, handler := range handlers {
			if token := c.Subscribe(topic, 0, handler); token.Wait() && token.Error() != nil {
				logger.Error("MQTT重新订阅失败", logger.String("topic", topic), logger.Error2(token.Error()))
			}
		}
		handlersMu.RUnlock()
	})

	opts.SetConnectionLostHandler(func(c mqtt.Client, err error) {
		logger.Error("MQTT连接断开", logger.Error2(err))
	})

	// 创建客户端
	client = mqtt.NewClient(opts)

	// 连接服务器
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		logger.Error("MQTT连接失败", logger.Error2(token.Error()))
		panic(fmt.Sprintf("MQTT连接失败: %v", token.Error()))
	}

	logger.Info("MQTT初始化成功")
}

// Subscribe 订阅主题
func Subscribe(topic string, handler HandlerFunc) error {
	h := mqtt.MessageHandler(handler)

	handlersMu.Lock()
	handlers[topic] = h
	handlersMu.Unlock()

	token := client.Subscribe(topic, 0, h)
	token.Wait()

	if err := token.Error(); err != nil {
		logger.Error("MQTT订阅失败", logger.String("topic", topic), logger.Error2(err))
		return err
	}

	logger.Info("MQTT订阅成功", logger.String("topic", topic))
	return nil
}

// Unsubscribe 取消订阅
func Unsubscribe(topic string) error {
	handlersMu.Lock()
	delete(handlers, topic)
	handlersMu.Unlock()

	token := client.Unsubscribe(topic)
	token.Wait()

	if err := token.Error(); err != nil {
		logger.Error("MQTT取消订阅失败", logger.String("topic", topic), logger.Error2(err))
		return err
	}

	logger.Info("MQTT取消订阅成功", logger.String("topic", topic))
	return nil
}

// Publish 发布消息
func Publish(topic string, payload interface{}) error {
	token := client.Publish(topic, 0, false, payload)
	token.Wait()

	if err := token.Error(); err != nil {
		logger.Error("MQTT发布失败", logger.String("topic", topic), logger.Error2(err))
		return err
	}

	return nil
}

// Close 关闭MQTT连接
func Close() {
	if client != nil && client.IsConnected() {
		client.Disconnect(250)
		logger.Info("MQTT连接已关闭")
	}
}
