package convert

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"
)

// ToStruct 将map转换为结构体
func ToStruct(data map[string]interface{}, obj interface{}) error {
	val := reflect.ValueOf(obj).Elem()
	typ := val.Type()

	// 遍历结构体字段
	for i := 0; i < val.NumField(); i++ {
		field := typ.Field(i)

		// 处理嵌入字段
		if field.Anonymous {
			embed := reflect.New(field.Type)
			ToStruct(data, embed.Interface())
			val.Field(i).Set(embed.Elem())
			continue
		}

		// 获取json标签
		jsonName := field.Tag.Get("json")
		if jsonName == "" || jsonName == "-" {
			continue
		}
		jsonName = strings.Split(jsonName, ",")[0]

		// 获取值
		v, ok := data[jsonName]
		if !ok || v == nil {
			continue
		}

		fieldVal := val.Field(i)
		if !fieldVal.CanSet() {
			continue
		}

		// 根据类型设置值
		switch fieldVal.Kind() {
		case reflect.String:
			// 字符串
			switch sv := v.(type) {
			case string:
				fieldVal.SetString(sv)
			default:
				fieldVal.SetString(fmt.Sprint(sv))
			}

		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			// 整数
			switch sv := v.(type) {
			case string:
				if iv, err := strconv.ParseInt(sv, 10, 64); err == nil {
					fieldVal.SetInt(iv)
				}
			case float64:
				fieldVal.SetInt(int64(sv))
			case int:
				fieldVal.SetInt(int64(sv))
			}

		case reflect.Bool:
			// 布尔值
			switch sv := v.(type) {
			case string:
				fieldVal.SetBool(sv == "true" || sv == "1")
			case bool:
				fieldVal.SetBool(sv)
			case int, float64:
				fieldVal.SetBool(sv != 0)
			}
		}
	}

	return nil //
}
