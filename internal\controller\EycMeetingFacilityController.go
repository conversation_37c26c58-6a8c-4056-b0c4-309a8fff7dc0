package controller

import (
	"eyc3_meeting/internal/model"
	"eyc3_meeting/internal/service"
)

// EycMeetingFacilityController 会议设施控制器
// 采用 BaseController 泛型，统一风格
type EycMeetingFacilityController struct {
	BaseController[model.EycMeetingFacility, *service.EycMeetingFacilityService]
}

// 全局会议设施服务与控制器实例
var (
	eycMeetingFacilityService    = new(service.EycMeetingFacilityService)
	eycMeetingFacilityController = EycMeetingFacilityController{
		BaseController: BaseController[model.EycMeetingFacility, *service.EycMeetingFacilityService]{Service: eycMeetingFacilityService},
	}
)

// 标准CRUD接口实现
var (
	// GetEycMeetingFacilityList 获取设施列表（分页、可筛选）
	GetEycMeetingFacilityList = eycMeetingFacilityController.GetList(true, "name:%")

	// GetAllEycMeetingFacilities 获取所有设施
	GetAllEycMeetingFacilities = eycMeetingFacilityController.GetAll(true, "name:%")

	// GetEycMeetingFacilityInfo 获取单个设施详情
	GetEycMeetingFacilityInfo = eycMeetingFacilityController.GetInfo()

	// AddEycMeetingFacility 新增设施
	// corpid 会从JWT中自动注入
	AddEycMeetingFacility = eycMeetingFacilityController.Add(true, "name")

	// ModifyEycMeetingFacility 修改设施
	ModifyEycMeetingFacility = eycMeetingFacilityController.Modify(true, "id:i,name")

	// DeleteEycMeetingFacility 删除设施
	DeleteEycMeetingFacility = eycMeetingFacilityController.Delete()
)
