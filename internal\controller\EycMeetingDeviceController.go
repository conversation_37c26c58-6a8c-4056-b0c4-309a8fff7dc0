package controller

import (
	"context"
	"eyc3_meeting/internal/model"
	"eyc3_meeting/internal/pkg/response"
	"eyc3_meeting/internal/service"

	"net/http"
	"strconv"

	"github.com/cloudwego/hertz/pkg/app"
)

// EycMeetingDeviceController 设备控制器
type EycMeetingDeviceController struct {
	BaseController[model.EycMeetingDevice, *service.EycMeetingDeviceService]
}

// 全局设备服务与控制器实例
var (
	eycMeetingDeviceService    = new(service.EycMeetingDeviceService)
	eycMeetingDeviceController = EycMeetingDeviceController{
		BaseController: BaseController[model.EycMeetingDevice, *service.EycMeetingDeviceService]{Service: eycMeetingDeviceService},
	}
)

// --- 接口实现 ---
var (
	// 以下方法直接使用了 BaseController 的通用实现
	GetEycMeetingDeviceList = eycMeetingDeviceController.GetList(true, "title:%")
	GetAllEycMeetingDevice  = eycMeetingDeviceController.GetAll(true, "device_type")
	GetEycMeetingDeviceInfo = eycMeetingDeviceController.GetInfo()
	AddEycMeetingDevice     = eycMeetingDeviceController.Add(true,
		"title", "sn", "model", "device_type", "light_brightness",
		"volume", "custom_template", "template_free", "template_idle",
		"template_using", "related_door", "system_mode", "auto_power",
		"power_on_time", "power_off_time", "status", "template_image",
		"network", "ip", "mac",
	)
	ModifyEycMeetingDevice = eycMeetingDeviceController.Modify(true,
		"title", "sn", "model", "device_type", "light_brightness",
		"volume", "custom_template", "template_free", "template_idle",
		"template_using", "related_door", "system_mode", "auto_power",
		"power_on_time", "power_off_time", "status", "template_image",
	)
	DeleteEycMeetingDevice = eycMeetingDeviceController.Delete()
	UnbindAndResetDevice   = UnbindAndResetHandler()

	// 以下是自定义的接口
	GetDeviceTypeCount  = GetDeviceTypeCountHandler()
	GetDevicesAndGroups = GetDevicesAndGroupsHandler()
)

// GetDeviceTypeCountHandler 获取设备类型数量统计 - 控制器层
func GetDeviceTypeCountHandler() func(ctx context.Context, c *app.RequestContext) {
	return func(ctx context.Context, c *app.RequestContext) {
		counts, err := eycMeetingDeviceService.GetDeviceTypeCount(c)
		if err != nil {
			response.Fail(c, response.StatusBadRequest, "获取失败: "+err.Error())
			return
		}
		response.Success(c, counts)
	}
}

// GetDevicesAndGroupsHandler 获取统一的设备和分组列表
func GetDevicesAndGroupsHandler() func(ctx context.Context, c *app.RequestContext) {
	return func(ctx context.Context, c *app.RequestContext) {
		sortType := c.Query("sort_type")
		if sortType == "" {
			sortType = "0"
		}
		deviceType := c.Query("device_type")

		service := service.EycMeetingDeviceService{}
		result, err := service.GetDevicesAndGroups(c, sortType, deviceType)
		if err != nil {
			response.Fail(c, http.StatusInternalServerError, "获取设备及分组列表失败")
			return
		}
		response.Success(c, result)
	}
}

// UnbindAndResetHandler 解绑并重置设备
func UnbindAndResetHandler() func(c context.Context, ctx *app.RequestContext) {
	return func(c context.Context, ctx *app.RequestContext) {
		idStr := ctx.Param("id")
		id, err := strconv.ParseUint(idStr, 10, 64)
		if err != nil {
			response.Fail(ctx, response.StatusBadRequest, "无效的ID")
			return
		}
		if err := eycMeetingDeviceService.UnbindAndResetService(ctx, uint(id)); err != nil {
			response.Fail(ctx, response.StatusServerError, "解绑并重置失败: "+err.Error())
			return
		}
		response.Success(ctx, nil)
	}
}

// GetDeviceInfoViewHandler 获取设备详情
func (ctl *EycMeetingDeviceController) GetDeviceInfoViewHandler() func(c context.Context, ctx *app.RequestContext) {
	return func(c context.Context, ctx *app.RequestContext) {
		idStr := ctx.Param("id")
		id, err := strconv.ParseUint(idStr, 10, 64)
		if err != nil {
			response.Fail(ctx, response.StatusBadRequest, "无效的ID")
			return
		}
		info, found, err := ctl.Service.GetDeviceInfoView(ctx, uint(id))
		if err != nil {
			response.Fail(ctx, response.StatusServerError, "获取设备详情失败: "+err.Error())
			return
		}
		if !found {
			response.Fail(ctx, response.StatusNotFound, "设备不存在")
			return
		}
		response.Success(ctx, info)
	}
}

// 导出自定义接口
var (
	GetDeviceInfoView = eycMeetingDeviceController.GetDeviceInfoViewHandler()
)
