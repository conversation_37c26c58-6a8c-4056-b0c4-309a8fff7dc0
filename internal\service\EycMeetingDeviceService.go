package service

import (
	"errors"
	"eyc3_meeting/internal/model"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/cloudwego/hertz/pkg/app"
	"golang.org/x/text/collate"
	"golang.org/x/text/language"
	"gorm.io/gorm"
)

// toInt 尝试将接口值转换为整数
func toInt(v any) (int, bool) {
	if v == nil {
		return 0, false
	}
	switch val := v.(type) {
	case int:
		return val, true
	case float64:
		return int(val), true
	case string:
		i, err := strconv.Atoi(val)
		if err == nil {
			return i, true
		}
	}
	return 0, false
}

// EycMeetingDeviceService 设备服务
type EycMeetingDeviceService struct {
	BaseServiceImpl[model.EycMeetingDevice]
}

// AddService 新增设备，重写基础方法以实现自定义逻辑
func (s *EycMeetingDeviceService) AddService(ac *app.RequestContext, data interface{}) (uint, error) {
	m, ok := data.(map[string]interface{})
	if !ok {
		return 0, fmt.Errorf("参数类型错误")
	}

	device := &model.EycMeetingDevice{}

	// 从 map 解析参数
	if title, ok := m["title"].(string); ok && title != "" {
		device.Title = title
	} else {
		return 0, errors.New("设备名称不能为空")
	}
	if sn, ok := m["sn"].(string); ok && sn != "" {
		device.SN = sn
	} else {
		return 0, errors.New("设备SN号不能为空")
	}

	// 从 context 获取 corpid
	corpIDValue, exists := ac.Get("corp_id")
	if !exists {
		return 0, errors.New("无法获取企业ID，请检查登录状态")
	}
	corpID, ok := corpIDValue.(string)
	if !ok || corpID == "" {
		return 0, errors.New("无效的企业ID")
	}
	device.Corpid = corpID

	// -- 新增逻辑：处理 network, ip, mac --
	if network, ok := m["network"].(string); ok {
		device.Network = network
	}
	if ip, ok := m["ip"].(string); ok {
		device.IP = ip
	}
	if mac, ok := m["mac"].(string); ok {
		device.Mac = mac
	}
	// -- 新增逻辑结束 --

	// 特殊业务逻辑：处理数字类型的 device_type 和 system_mode
	if deviceTypeNum, ok := toInt(m["device_type"]); ok {
		switch deviceTypeNum {
		case 0: //0是会议室
			device.DeviceType = model.DeviceTypeDoor
		case 1: //1是大屏
			device.DeviceType = model.DeviceTypeScreen
		default:
			return 0, errors.New("无效的设备类型")
		}
	} else {
		device.DeviceType = model.DeviceTypeDoor // 默认值
	}

	if systemModeNum, ok := toInt(m["system_mode"]); ok {
		switch systemModeNum {
		case 0:
			device.SystemMode = model.SystemModeStandard //标准模式
		case 1:
			device.SystemMode = model.SystemModeSplitScreen //分屏模式
		case 2:
			device.SystemMode = model.SystemModeDoorControl //门禁模式
		default:
			return 0, errors.New("无效的系统模式")
		}
	} else {
		device.SystemMode = model.SystemModeStandard // 默认值
	}

	s.applyDeviceLogic(device, m) // 应用其他字段和默认值逻辑
	// 检查重名：分步、独立检查，确保唯一性
	// 1. 检查新 title 是否已被使用
	var count_title int64
	if err := model.DB.Model(&model.EycMeetingDevice{}).Where("corp_id = ? AND (title = ? OR sn = ?)", device.Corpid, device.Title, device.Title).Count(&count_title).Error; err != nil {
		return 0, fmt.Errorf("检查设备名称唯一性时出错: %w", err)
	}
	if count_title > 0 {
		return 0, errors.New("设备名称已被占用")
	}

	// 2. 检查新 sn 是否已被使用
	var count_sn int64
	if err := model.DB.Model(&model.EycMeetingDevice{}).Where("corp_id = ? AND (title = ? OR sn = ?)", device.Corpid, device.SN, device.SN).Count(&count_sn).Error; err != nil {
		return 0, fmt.Errorf("检查设备SN唯一性时出错: %w", err)
	}
	if count_sn > 0 {
		return 0, errors.New("设备SN号已被占用")
	}
	/*
			model.DB 应该是一个已经初始化好的数据库连接对象（很可能使用了像 GORM 这样的ORM库）。
			.Begin() 方法会向数据库发送一个开始事务的命令，并返回一个事务处理器 tx。
			从这行代码之后，所有通过 tx 对象执行的数据库操作（如创建、更新、删除）都将包含在这个事务里。这些操作在事务被提交（Commit）之前，
		并不会永久性地写入数据库。
	*/
	tx := model.DB.Begin() //开启一个新的数据库事务
	defer func() {
		//如果程序没有发生 panic，那么 recover() 会返回 nil，if 语句块内的代码就不会执行。
		if r := recover(); r != nil {
			tx.Rollback() //会告诉数据库：取消这个事务里已经执行的所有操作，将数据恢复到事务开始之前的状态。
		}
	}()

	now := time.Now()
	device.CreatedAt = model.LocalTime(now)
	device.UpdatedAt = model.LocalTime(now)
	//tx.Create(device) 功能: 这行代码的作用是在数据库中创建一个新的记录
	if err := tx.Create(device).Error; err != nil {
		tx.Rollback()
		return 0, err
	}

	if err := tx.Commit().Error; err != nil {
		return 0, err
	}

	return uint(device.ID), nil
}

// ModifyService 修改设备，重写基础方法
func (s *EycMeetingDeviceService) ModifyService(ac *app.RequestContext, id uint, data interface{}) error {
	m, ok := data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("参数类型错误")
	}

	corpIDValue, exists := ac.Get("corp_id")
	if !exists {
		return errors.New("无法获取企业ID")
	}
	corpID, _ := corpIDValue.(string)

	// 查找现有设备
	var existingDevice model.EycMeetingDevice
	if err := model.DB.Where("id = ? AND corp_id = ?", id, corpID).First(&existingDevice).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("设备不存在或无权访问")
		}
		return err
	}

	// 从 map 更新字段
	if title, ok := m["title"].(string); ok && title != "" {
		existingDevice.Title = title
	} else {
		return errors.New("设备名称不能为空")
	}
	if sn, ok := m["sn"].(string); ok && sn != "" {
		existingDevice.SN = sn
	} else {
		return errors.New("设备SN号不能为空")
	}

	// 翻译数字类型
	if deviceTypeNum, ok := toInt(m["device_type"]); ok {
		switch deviceTypeNum {
		case 0:
			existingDevice.DeviceType = model.DeviceTypeDoor
		case 1:
			existingDevice.DeviceType = model.DeviceTypeScreen
		default:
			return errors.New("无效的设备类型")
		}
	}

	if systemModeNum, ok := toInt(m["system_mode"]); ok {
		switch systemModeNum {
		case 0:
			existingDevice.SystemMode = model.SystemModeStandard
		case 1:
			existingDevice.SystemMode = model.SystemModeSplitScreen
		case 2:
			existingDevice.SystemMode = model.SystemModeDoorControl
		default:
			return errors.New("无效的系统模式")
		}
	}

	s.applyDeviceLogic(&existingDevice, m) // 应用其他字段

	// 检查重名 (排除自己)：分步、独立检查，确保唯一性
	// 1. 检查新 title 是否已被其他设备使用
	var count_title int64
	if err := model.DB.Model(&model.EycMeetingDevice{}).Where("corp_id = ? AND id != ? AND (title = ? OR sn = ?)", existingDevice.Corpid, id, existingDevice.Title, existingDevice.Title).Count(&count_title).Error; err != nil {
		return fmt.Errorf("检查设备名称唯一性时出错: %w", err)
	}
	if count_title > 0 {
		return errors.New("设备名称已被用作现有设备的名称或SN号")
	}

	// 2. 检查新 sn 是否已被其他设备使用
	var count2_sn int64
	if err := model.DB.Model(&model.EycMeetingDevice{}).Where("corp_id = ? AND id != ? AND (title = ? OR sn = ?)", existingDevice.Corpid, id, existingDevice.SN, existingDevice.SN).Count(&count2_sn).Error; err != nil {
		return fmt.Errorf("检查设备SN唯一性时出错: %w", err)
	}
	if count2_sn > 0 {
		return errors.New("设备SN号已被用作现有设备的名称或SN号")
	}
	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	existingDevice.UpdatedAt = model.LocalTime(time.Now())
	if err := tx.Save(&existingDevice).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// DeleteService 删除设备，重写基础方法
func (s *EycMeetingDeviceService) DeleteService(ac *app.RequestContext, id uint) error {
	corpIDValue, exists := ac.Get("corp_id")
	if !exists {
		return errors.New("无法获取企业ID")
	}
	corpID, _ := corpIDValue.(string)

	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var device model.EycMeetingDevice
	if err := tx.Where("id = ? AND corp_id = ?", id, corpID).First(&device).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("设备不存在或无权访问")
		}
		return fmt.Errorf("查找要删除的设备时出错: %w", err)
	}

	if err := tx.Delete(&device).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// GetListService 获取设备列表，重写以支持自定义筛选
func (s *EycMeetingDeviceService) GetListService(ac *app.RequestContext, page, pageSize int, fields map[string]string, orderBy string) (model.PageResult[model.EycMeetingDevice], error) {
	db := model.DB.Model(&model.EycMeetingDevice{})
	pageResult := model.PageResult[model.EycMeetingDevice]{Items: []model.EycMeetingDevice{}}

	corpID, _ := ac.Get("corp_id")
	db = db.Where("corp_id = ?", corpID)

	if keyword, ok := fields["keyword"]; ok && keyword != "" {
		db = db.Where("title LIKE ?", "%"+keyword+"%")
	}
	if groupIDStr, ok := fields["group_id"]; ok && groupIDStr != "" {
		if groupID, err := strconv.Atoi(groupIDStr); err == nil && groupID > 0 {
			db = db.Where("group_id = ?", groupID)
		}
	}
	if deviceType, ok := fields["device_type"]; ok && deviceType != "" {
		db = db.Where("device_type = ?", deviceType)
	}

	if err := db.Count(&pageResult.Total).Error; err != nil {
		return pageResult, err
	}

	// 处理排序
	sortType, _ := ac.Get("sortType")
	if sortTypeStr, ok := sortType.(string); ok && sortTypeStr != "" {
		switch sortTypeStr {
		case "0":
			orderBy = "CONVERT(title USING gbk) COLLATE gbk_chinese_ci ASC" // 按设备名称排序，使用中文排序规则
		case "1":
			orderBy = "created_at ASC" // 按添加时间正序
		case "2":
			orderBy = "created_at DESC" // 按添加时间倒序
		}
	}

	if orderBy == "" {
		orderBy = "id DESC" // 默认排序
	}
	db = db.Order(orderBy)

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	if err := db.Find(&pageResult.Items).Error; err != nil {
		return pageResult, err
	}

	return pageResult, nil
}

// applyDeviceLogic 是一个辅助函数，用于处理创建和更新时的通用设备字段逻辑
func (s *EycMeetingDeviceService) applyDeviceLogic(device *model.EycMeetingDevice, m map[string]interface{}) {
	// 使用类型断言来安全地处理 map 中的值
	if val, ok := m["model"].(string); ok {
		device.Model = val
	}
	if val, ok := toInt(m["light_brightness"]); ok {
		device.LightBrightness = val
	}
	if val, ok := toInt(m["volume"]); ok {
		device.Volume = val
	}
	if val, ok := toInt(m["custom_template"]); ok {
		device.CustomTemplate = val
	}
	if val, ok := m["template_free"].(string); ok {
		device.TemplateFree = val
	}
	if val, ok := m["template_idle"].(string); ok {
		device.TemplateIdle = val
	}
	if val, ok := m["template_using"].(string); ok {
		device.TemplateUsing = val
	}
	if val, ok := m["template_image"].(string); ok {
		device.TemplateImage = val
	}
	if val, ok := toInt(m["related_door"]); ok {
		device.RelatedDoor = val
	}
	if val, ok := toInt(m["auto_power"]); ok {
		device.AutoPower = val
	}
	if val, ok := m["power_on_time"].(string); ok {
		t, err := time.Parse("15:04:05", val)
		if err == nil {
			device.PowerOnTime = model.LocalTime(t)
		}
	}
	if val, ok := m["power_off_time"].(string); ok {
		t, err := time.Parse("15:04:05", val)
		if err == nil {
			device.PowerOffTime = model.LocalTime(t)
		}
	}
	if val, ok := toInt(m["status"]); ok {
		device.Status = val
	}

	// 模板逻辑
	if device.CustomTemplate == 1 {
		if device.TemplateFree == "" {
			device.TemplateFree = "#1678FF"
		}
		if device.TemplateIdle == "" {
			device.TemplateIdle = "#1678FF"
		}
		if device.TemplateUsing == "" {
			device.TemplateUsing = "#F7885C"
		}
		device.TemplateImage = "0"
	} else {
		device.TemplateFree = ""
		device.TemplateIdle = ""
		device.TemplateUsing = ""
	}

	// 自动开关机时间逻辑
	if device.AutoPower == 1 {
		// 检查时间是否为零时，即尚未设置
		if time.Time(device.PowerOnTime).IsZero() {
			t, _ := time.Parse("15:04:05", "07:00:00")
			device.PowerOnTime = model.LocalTime(t)
		}
		if time.Time(device.PowerOffTime).IsZero() {
			t, _ := time.Parse("15:04:05", "23:59:59")
			device.PowerOffTime = model.LocalTime(t)
		}
	}
}

// updateGroupInTx 是一个辅助函数，用于在事务中更新设备分组的设备列表
func (s *EycMeetingDeviceService) updateGroupInTx(tx *gorm.DB, groupID int, deviceID int, isAdd bool) error {
	var group model.EycMeetingDeviceGroup
	if err := tx.First(&group, groupID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		} // 分组不存在，静默处理
		return fmt.Errorf("查找设备分组失败: %w", err)
	}

	var deviceIDs []string
	if group.DeviceIds != "" {
		deviceIDs = strings.Split(group.DeviceIds, ",")
	}

	idMap := make(map[string]bool)
	var cleanIDs []string
	for _, idStr := range deviceIDs {
		trimmedID := strings.TrimSpace(idStr)
		if trimmedID != "" && trimmedID != "0" {
			if _, exists := idMap[trimmedID]; !exists {
				idMap[trimmedID] = true
				cleanIDs = append(cleanIDs, trimmedID)
			}
		}
	}

	deviceIDStr := strconv.Itoa(deviceID)
	_, exists := idMap[deviceIDStr]

	if isAdd {
		if !exists {
			cleanIDs = append(cleanIDs, deviceIDStr)
		}
	} else {
		if exists {
			var finalIDs []string
			for _, idStr := range cleanIDs {
				if idStr != deviceIDStr {
					finalIDs = append(finalIDs, idStr)
				}
			}
			cleanIDs = finalIDs
		}
	}

	return tx.Model(&group).Update("device_ids", strings.Join(cleanIDs, ",")).Error
}

// DeviceOrGroupView 是一个用于统一展示设备和设备分组的视图模型
type DeviceOrGroupView struct {
	ID          int       `json:"id"`
	Title       string    `json:"title"` // 名称
	Type        string    `json:"type"`  // "device" or "group"
	DeviceCount int       `json:"device_count"`
	Model       string    `json:"model,omitempty"` // 仅用于设备
	CreatedAt   time.Time `json:"created_at"`
}

// GetDevicesAndGroups 获取设备和分组的统一列表，增加了deviceType筛选和分组优先排序
func (s *EycMeetingDeviceService) GetDevicesAndGroups(ac *app.RequestContext, sortType string, deviceType string) ([]DeviceOrGroupView, error) {
	corpID, exists := ac.Get("corp_id")
	if !exists {
		return nil, errors.New("无法获取企业ID")
	}

	// 1. 获取该企业下的 *所有* 设备和分组，以便进行全面的分组逻辑判断
	var allDevicesInDB []model.EycMeetingDevice
	if err := model.DB.Where("corp_id = ?", corpID).Find(&allDevicesInDB).Error; err != nil {
		return nil, fmt.Errorf("获取所有设备列表失败: %w", err)
	}

	// 创建一个设备ID到设备详情的映射，方便快速查找
	deviceMap := make(map[int]model.EycMeetingDevice)
	for _, device := range allDevicesInDB {
		deviceMap[device.ID] = device
	}

	var allGroups []model.EycMeetingDeviceGroup
	if err := model.DB.Where("corp_id = ?", corpID).Find(&allGroups).Error; err != nil {
		return nil, fmt.Errorf("获取设备分组列表失败: %w", err)
	}

	var result []DeviceOrGroupView
	groupedDeviceIDs := make(map[int]bool) // 跟踪所有已分组的设备ID

	// 2. 智能处理分组：根据deviceType筛选，并计算分组内的设备数量
	for _, group := range allGroups {
		if group.DeviceIds == "" {
			continue // 跳过没有设备的空分组
		}

		deviceIDs := strings.Split(group.DeviceIds, ",")
		matchingDeviceCount := 0

		for _, idStr := range deviceIDs {
			id, err := strconv.Atoi(strings.TrimSpace(idStr))
			if err != nil || id == 0 {
				continue
			}
			groupedDeviceIDs[id] = true // 标记为已分组

			// 如果启用了筛选，则只计算符合类型的设备
			if deviceType != "" {
				if device, ok := deviceMap[id]; ok && device.DeviceType == deviceType {
					matchingDeviceCount++
				}
			} else {
				// 未启用筛选，计算所有设备
				matchingDeviceCount++
			}
		}

		// 只有在分组内有符合条件的设备时，才将该分组加入结果列表
		if matchingDeviceCount > 0 {
			result = append(result, DeviceOrGroupView{
				ID:          group.ID,
				Title:       group.Title,
				Type:        "group",
				DeviceCount: matchingDeviceCount,
				CreatedAt:   time.Time(group.CreatedAt),
			})
		}
	}

	// 3. 处理未分组的设备：根据deviceType进行筛选
	for _, device := range allDevicesInDB {
		// 如果设备未被分组
		if _, isGrouped := groupedDeviceIDs[device.ID]; !isGrouped {
			// 如果启用了筛选，则只添加符合类型的设备
			if deviceType != "" {
				if device.DeviceType == deviceType {
					result = append(result, DeviceOrGroupView{
						ID:          device.ID,
						Title:       device.Title,
						Type:        "device",
						DeviceCount: 1,
						Model:       device.Model,
						CreatedAt:   time.Time(device.CreatedAt),
					})
				}
			} else {
				// 未启用筛选，添加所有未分组的设备
				result = append(result, DeviceOrGroupView{
					ID:          device.ID,
					Title:       device.Title,
					Type:        "device",
					DeviceCount: 1,
					Model:       device.Model,
					CreatedAt:   time.Time(device.CreatedAt),
				})
			}
		}
	}

	// 4. 排序逻辑修改：始终将分组排在设备前面，然后再按指定规则排序
	collator := collate.New(language.Make("zh-u-co-pinyin"), collate.IgnoreCase)
	sort.Slice(result, func(i, j int) bool {
		// 规则1：如果类型不同，组("group")永远排在设备("device")前面
		if result[i].Type != result[j].Type {
			return result[i].Type == "group"
		}

		// 规则2：如果类型相同，则按指定的 sortType 进行排序
		switch sortType {
		case "0": // 按名称排序 (a-z)，使用拼音
			return collator.CompareString(result[i].Title, result[j].Title) < 0
		case "1": // 按添加时间正序
			return result[i].CreatedAt.Before(result[j].CreatedAt)
		case "2": // 按添加时间倒序
			return result[i].CreatedAt.After(result[j].CreatedAt)
		default:
			return false // 默认保持原始顺序
		}
	})

	return result, nil
}

// GetDeviceTypeCount 获取各类型设备数量
func (s *EycMeetingDeviceService) GetDeviceTypeCount(ac *app.RequestContext) (map[string]int64, error) {
	tempResult := make(map[string]int64)
	corpID, exists := ac.Get("corp_id")
	if !exists {
		return nil, errors.New("无法获取企业ID")
	}

	var counts []struct {
		DeviceType string `json:"device_type"`
		Count      int64  `json:"count"`
	}

	// 使用 GROUP BY 一次性查询所有类型的数量
	if err := model.DB.Model(&model.EycMeetingDevice{}).
		Select("device_type, count(*) as count").
		Where("corp_id = ?", corpID).
		Group("device_type").
		Scan(&counts).Error; err != nil {
		return nil, err
	}

	for _, c := range counts {
		tempResult[c.DeviceType] = c.Count
	}

	// 确保 "0" 和 "1" 类型即使数量为0也存在
	if _, ok := tempResult["0"]; !ok {
		tempResult["0"] = 0
	}
	if _, ok := tempResult["1"]; !ok {
		tempResult["1"] = 0
	}

	// 转换为 "door" 和 "screen" 作为键
	finalResult := make(map[string]int64)
	finalResult["door"] = tempResult["0"]
	finalResult["screen"] = tempResult["1"]

	return finalResult, nil
}

// DeviceInfoView 是用于设备详情页的响应模型
type DeviceInfoView struct {
	model.EycMeetingDevice
	NetworkStatus string `json:"network_status"` // 网络状态文字描述: "在线" 或 "离线"
}

// GetDeviceInfoView 获取单个设备详情，专为API响应设计
func (s *EycMeetingDeviceService) GetDeviceInfoView(ac *app.RequestContext, id uint) (*DeviceInfoView, bool, error) {
	// 1. 调用基础服务获取原始设备信息
	device, found, err := s.GetInfoService(ac, id)
	if !found || err != nil {
		return nil, found, err
	}

	// 2. 创建用于API响应的视图模型
	deviceInfo := &DeviceInfoView{
		EycMeetingDevice: device,
	}

	// 3. 根据Status字段计算NetworkStatus
	if device.Status == 1 {
		deviceInfo.NetworkStatus = "在线"
	} else {
		deviceInfo.NetworkStatus = "离线"
	}

	return deviceInfo, true, nil
}

// UnbindAndResetService 解绑并重置设备
func (s *EycMeetingDeviceService) UnbindAndResetService(ac *app.RequestContext, id uint) error {
	return model.DB.Transaction(func(tx *gorm.DB) error {
		// 1. 查找设备
		var device model.EycMeetingDevice
		if err := tx.First(&device, id).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return errors.New("设备不存在")
			}
			return err
		}

		// 2. 从设备组中解绑
		var groups []model.EycMeetingDeviceGroup
		// 使用FIND_IN_SET查找所有包含该设备ID的组
		if err := tx.Where("FIND_IN_SET(?, device_ids)", id).Find(&groups).Error; err != nil {
			return fmt.Errorf("查找设备所属的分组失败: %w", err)
		}

		for _, group := range groups {
			idStr := strconv.Itoa(int(id))
			ids := strings.Split(group.DeviceIds, ",")
			newIds := make([]string, 0, len(ids)-1)
			for _, currentId := range ids {
				if currentId != idStr {
					newIds = append(newIds, currentId)
				}
			}
			group.DeviceIds = strings.Join(newIds, ",")
			if err := tx.Save(&group).Error; err != nil {
				return fmt.Errorf("从分组 '%s' 中解绑设备失败: %w", group.Title, err)
			}
		}

		// 3. 重置设备信息为默认值
		// 注意：GORM的 `default` tag 只在Create时生效，Update时需要手动设置。
		device.Title = device.SN // 将名称重置为SN号
		device.LightBrightness = 100
		device.Volume = 100
		device.CustomTemplate = 0
		device.TemplateFree = ""
		device.TemplateIdle = ""
		device.TemplateUsing = ""
		device.RelatedDoor = 0
		device.SystemMode = model.SystemModeStandard
		device.AutoPower = 0
		// 时间类型需要特殊处理以设为默认值
		powerOnTimeDefault, _ := time.Parse("15:04:05", "07:00:00")
		powerOffTimeDefault, _ := time.Parse("15:04:05", "23:59:59")
		device.PowerOnTime = model.LocalTime(powerOnTimeDefault)
		device.PowerOffTime = model.LocalTime(powerOffTimeDefault)
		device.TemplateImage = ""
		device.UpdatedAt = model.LocalTime(time.Now())

		if err := tx.Save(&device).Error; err != nil {
			return fmt.Errorf("重置设备信息失败: %w", err)
		}

		return nil
	})
}
