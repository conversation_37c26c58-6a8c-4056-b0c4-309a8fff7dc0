package router

import (
	"eyc3_meeting/internal/middleware"
	"eyc3_meeting/internal/router/auth"
	"eyc3_meeting/internal/router/meeting"
	"eyc3_meeting/internal/router/sys"

	"github.com/cloudwego/hertz/pkg/app/server"
)

// Register 注册所有路由
func Register(r *server.Hertz) {
	// 注册全局CORS中间件
	r.Use(middleware.CORSMiddleware())

	// 注册认证路由
	auth.Register(r)

	// 注册系统路由
	sys.Register(r)

	// 注册会议相关路由
	meeting.RegisterMeetingRoutes(r)
}
