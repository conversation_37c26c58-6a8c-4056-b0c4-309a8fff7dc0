package controller

import (
	"eyc3_meeting/internal/model"
	"eyc3_meeting/internal/service"
)

// EycMeetingGroupController 会议室分组控制器
// 采用 BaseController 泛型，统一风格

type EycMeetingGroupController struct {
	BaseController[model.EycMeetingGroup, *service.EycMeetingGroupService]
}

// 全局会议室分组服务与控制器实例
var (
	eycMeetingGroupService    = new(service.EycMeetingGroupService)
	eycMeetingGroupController = EycMeetingGroupController{
		BaseController: BaseController[model.EycMeetingGroup, *service.EycMeetingGroupService]{Service: eycMeetingGroupService},
	}
)

// --- 标准CRUD接口实现 ---
var (
	// GetEycMeetingGroupList 获取会议室分组列表（分页、可筛选）
	GetEycMeetingGroupList = eycMeetingGroupController.GetList(true, "title:%")

	// GetAllEycMeetingGroups 获取所有会议室分组
	GetAllEycMeetingGroups = eycMeetingGroupController.GetAll(true, "title:%")

	// GetEycMeetingGroupInfo 获取单个会议室分组详情
	GetEycMeetingGroupInfo = eycMeetingGroupController.GetInfo()

	// AddEycMeetingGroup 新增会议室分组
	AddEycMeetingGroup = eycMeetingGroupController.Add(true, "title,sort,roomIds,meet_room_name")

	// ModifyEycMeetingGroup 修改会议室分组
	ModifyEycMeetingGroup = eycMeetingGroupController.Modify(true, "id:i,title,sort,roomIds,meet_room_name")

	// DeleteEycMeetingGroup 删除会议室分组
	DeleteEycMeetingGroup = eycMeetingGroupController.Delete()
)
