package service

import (
	"errors"

	"eyc3_meeting/internal/model"
	"eyc3_meeting/internal/pkg/logger"
)

// Login 登录服务
func Login(username, password string) (*model.User, error) {
	// 从数据库获取用户
	user, err := model.GetUserByUsername(username)
	if err != nil {
		logger.Error("登录查询用户失败", logger.Error2(err))
		return nil, err
	}

	// 检查用户是否存在
	if user == nil {
		return nil, errors.New("用户名不存在")
	}

	// 检查密码是否正确
	if !user.CheckPassword(password) {
		return nil, errors.New("密码错误")
	}

	// 检查用户状态
	if user.Status != 1 {
		return nil, errors.New("账号已被禁用")
	}

	return user, nil
}

// Register 注册服务
func Register(user *model.User) error {
	// 检查用户名是否已存在
	existUser, err := model.GetUserByUsername(user.Username)
	if err != nil {
		logger.Error("注册查询用户失败", logger.Error2(err))
		return err
	}

	if existUser != nil {
		return errors.New("用户名已存在")
	}

	// 创建用户
	err = model.CreateUser(user)
	if err != nil {
		logger.Error("创建用户失败", logger.Error2(err))
		return err
	}

	return nil
}
