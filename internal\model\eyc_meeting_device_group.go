package model

import (
	"eyc3_meeting/internal/pkg/logger"
	"time"
)

const TableNameEycDeviceGroup = "eyc_meeting_device_group"

// EycMeetingDeviceGroup 设备分组表
// 用于管理不同类型的设备分组信息
type EycMeetingDeviceGroup struct {
	ID         int       `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`
	Corpid     string    `gorm:"column:corp_id;type:varchar(64);not null;index;comment:企业ID" json:"corp_id"`
	Title      string    `gorm:"column:title;type:varchar(128);not null;comment:分组名称" json:"title"`
	DeviceType string    `gorm:"column:device_type;type:varchar(20);comment:设备类型：door=门牌，screen=大屏" json:"device_type"`
	DeviceIds  string    `gorm:"column:device_ids;type:text;comment:关联设备ID列表，逗号分隔" json:"device_ids"`
	CreatedAt  time.Time `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at;type:timestamp;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`
}

// TableName 指定表名
func (*EycMeetingDeviceGroup) TableName() string {
	return TableNameEycDeviceGroup
}

// IsGroupDeviceExists  检查同企业下分组名称是否已存在
func IsGroupDeviceExists(corpid string, title string) (bool, error) {
	var count int64
	err := DB.Model(&EycMeetingDeviceGroup{}).
		Where("corp_id = ? AND title = ?", corpid, title).
		Count(&count).Error

	if err != nil {
		logger.Error("检查分组名称是否存在时出错",
			logger.String("corpid", corpid),
			logger.String("title", title),
			logger.Error2(err))
		return false, err
	}

	return count > 0, nil
}
