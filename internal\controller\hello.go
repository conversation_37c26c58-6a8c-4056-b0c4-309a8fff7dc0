package controller

import (
	"context"
	"eyc3_meeting/internal/pkg/logger"
	"eyc3_meeting/internal/pkg/request"
	"eyc3_meeting/internal/pkg/response"
	"eyc3_meeting/internal/service"

	"github.com/cloudwego/hertz/pkg/app"
)

// HelloRequest 请求参数，用于展示统一参数处理
type HelloRequest struct {
	Name string `json:"name" form:"name" query:"name"`
}

// Hello 接口
func Hello(c context.Context, ctx *app.RequestContext) {
	// 获取用户信息
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "未登录")
		return
	}

	// 尝试解析请求参数
	var req HelloRequest
	if err := request.Bind(ctx, &req); err == nil && req.Name != "" {
		logger.Info("请求参数",
			logger.Int("user_id", int(userID.(uint))),
			logger.String("name", req.Name),
		)
	}

	// 记录访问日志
	logger.Info("Hello API请求",
		logger.Int("user_id", int(userID.(uint))),
		logger.String("client_ip", ctx.ClientIP()),
		logger.String("method", string(ctx.Method())),
	)

	// 调用service
	message, err := service.GetHelloMessage()
	if err != nil {
		response.ServerError(ctx, err)
		return
	}

	// 自定义问候语
	if req.Name != "" {
		message = "Hello, " + req.Name + "!"
	}

	// 返回响应
	response.Success(ctx, message)
}
