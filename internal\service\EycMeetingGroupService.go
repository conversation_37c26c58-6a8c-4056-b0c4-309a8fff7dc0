package service

import (
	"context"
	"errors"
	"eyc3_meeting/internal/model"
	"fmt"
	"strconv"
	"strings"

	"github.com/cloudwego/hertz/pkg/app"
	"gorm.io/gorm"
)

// EycMeetingGroupService 会议室分组服务
type EycMeetingGroupService struct {
	BaseServiceImpl[model.EycMeetingGroup]
}

// AddService 新增会议室分组
func (s *EycMeetingGroupService) AddService(ac *app.RequestContext, data interface{}) (uint, error) {
	m, ok := data.(map[string]interface{})
	if !ok {
		return 0, fmt.Errorf("参数类型错误")
	}

	corpid, _ := ac.Get("corp_id")
	title, _ := m["title"].(string)
	if title == "" {
		return 0, errors.New("分组名称不能为空")
	}

	// 检查重名
	if exists, err := model.IsGroupTitleExists(corpid.(string), title); err != nil {
		return 0, err
	} else if exists {
		return 0, errors.New("分组名称已存在")
	}

	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	roomIDStr, _ := m["roomIds"].(string)
	var roomIDs []int
	if roomIDStr != "" {
		idStrSlice := strings.Split(roomIDStr, ",")
		for _, idStr := range idStrSlice {
			trimmedIDStr := strings.TrimSpace(idStr)
			if id, err := strconv.Atoi(trimmedIDStr); err == nil {
				roomIDs = append(roomIDs, id)
			}
		}
	}

	var sortValue int
	if sort, ok := m["sort"]; ok {
		switch v := sort.(type) {
		case float64:
			sortValue = int(v)
		case string:
			sortValue, _ = strconv.Atoi(v)
		case int:
			sortValue = v
		}
	}
	meetRoomName, _ := m["meet_room_name"].(string)

	group := model.EycMeetingGroup{
		Corpid:       corpid.(string),
		Title:        title,
		RoomIds:      roomIDStr,
		Sort:         sortValue,
		MeetRoomName: meetRoomName,
	}

	// 1. 创建分组
	if err := tx.Create(&group).Error; err != nil {
		tx.Rollback()
		return 0, err
	}

	// 2. 更新会议室的 group_id
	if len(roomIDs) > 0 {
		if err := tx.Model(&model.EycMeetingRoom{}).Where("id IN ?", roomIDs).Update("group_id", group.ID).Error; err != nil {
			tx.Rollback()
			return 0, err
		}
	}

	return uint(group.ID), tx.Commit().Error
}

// ModifyService 修改会议室分组
func (s *EycMeetingGroupService) ModifyService(ac *app.RequestContext, id uint, data interface{}) error {
	m, ok := data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("参数类型错误")
	}

	title, _ := m["title"].(string)
	if title == "" {
		return errors.New("分组名称不能为空")
	}

	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var group model.EycMeetingGroup
	if err := tx.First(&group, id).Error; err != nil {
		tx.Rollback()
		return errors.New("分组不存在")
	}

	// 解析旧的room_ids
	oldRoomIDStrs := strings.Split(group.RoomIds, ",")
	oldRoomIDs := make(map[int]bool)
	for _, idStr := range oldRoomIDStrs {
		if id, err := strconv.Atoi(strings.TrimSpace(idStr)); err == nil {
			oldRoomIDs[id] = true
		}
	}

	// 解析新的room_ids
	newRoomIDStr, _ := m["roomIds"].(string)
	var newRoomIDs []int
	if newRoomIDStr != "" {
		idStrSlice := strings.Split(newRoomIDStr, ",")
		for _, idStr := range idStrSlice {
			trimmedIDStr := strings.TrimSpace(idStr)
			if id, err := strconv.Atoi(trimmedIDStr); err == nil {
				newRoomIDs = append(newRoomIDs, id)
			}
		}
	}
	newRoomIDMap := make(map[int]bool)
	for _, id := range newRoomIDs {
		newRoomIDMap[id] = true
	}

	// 计算差异
	var roomsToUngroup []int
	var roomsToGroup []int

	for id := range oldRoomIDs {
		if !newRoomIDMap[id] {
			roomsToUngroup = append(roomsToUngroup, id)
		}
	}

	for id := range newRoomIDMap {
		if !oldRoomIDs[id] {
			roomsToGroup = append(roomsToGroup, id)
		}
	}

	// 1. 将被移除的会议室的group_id设为0
	if len(roomsToUngroup) > 0 {
		if err := tx.Model(&model.EycMeetingRoom{}).Where("id IN ?", roomsToUngroup).Update("group_id", 0).Error; err != nil {
			tx.Rollback()
			return err
		}
	}
	// 2. 将新加入的会议室的group_id设为当前分组id
	if len(roomsToGroup) > 0 {
		if err := tx.Model(&model.EycMeetingRoom{}).Where("id IN ?", roomsToGroup).Update("group_id", id).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 3. 更新分组本身
	var sortValue int
	if sort, ok := m["sort"]; ok {
		switch v := sort.(type) {
		case float64:
			sortValue = int(v)
		case string:
			sortValue, _ = strconv.Atoi(v)
		case int:
			sortValue = v
		}
	}
	meetRoomName, _ := m["meet_room_name"].(string)

	group.Title = title
	group.RoomIds = newRoomIDStr
	group.Sort = sortValue
	group.MeetRoomName = meetRoomName
	if err := tx.Save(&group).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// DeleteService 删除会议室分组
func (s *EycMeetingGroupService) DeleteService(ac *app.RequestContext, id uint) error {
	var group model.EycMeetingGroup
	if err := model.DB.First(&group, id).Error; err != nil {
		return errors.New("分组不存在")
	}

	if strings.TrimSpace(group.MeetRoomName) != "" {
		return errors.New("此分组下的会议室名称不为空，请先移除该分组下的所有会议室")
	}
	if strings.TrimSpace(group.RoomIds) != "" {
		return errors.New("此分组下的会议室不为空，请先移除该分组下的所有会议室")
	}

	if err := model.DB.Delete(&group).Error; err != nil {
		return err
	}

	return nil
}

// GetInfoService
func (s *EycMeetingGroupService) GetInfoService(ac *app.RequestContext, id uint) (model.EycMeetingGroup, bool, error) {
	var group model.EycMeetingGroup
	if err := model.DB.First(&group, id).Error; err != nil {
		return group, false, err
	}

	if err := s.populateRoomsForGroup(&group); err != nil {
		return group, false, err
	}

	return group, true, nil
}

// GetListService
func (s *EycMeetingGroupService) GetListService(ac *app.RequestContext, page, perPage int, fields map[string]string, orderBy string) (model.PageResult[model.EycMeetingGroup], error) {
	var groups []model.EycMeetingGroup
	var total int64
	var pageResult model.PageResult[model.EycMeetingGroup]

	db := model.DB.Model(&model.EycMeetingGroup{})

	corpID, _ := ac.Get("corp_id")
	if corpIDStr, ok := corpID.(string); ok && corpIDStr != "" {
		db = db.Where("corp_id = ?", corpIDStr)
	}

	if keyword, ok := fields["keyword"]; ok && keyword != "" {
		db = db.Where("title LIKE ?", "%"+keyword+"%")
	}

	if err := db.Count(&total).Error; err != nil {
		return pageResult, err
	}

	pageResult.Total = total
	pageResult.Items = []model.EycMeetingGroup{}

	if orderBy == "" {
		orderBy = "id DESC"
	}
	db = db.Order(orderBy)

	if page > 0 && perPage > 0 {
		offset := (page - 1) * perPage
		db = db.Offset(offset).Limit(perPage)
	}

	if total > 0 {
		if err := db.Find(&groups).Error; err != nil {
			return pageResult, err
		}
		if err := s.populateRoomsForGroups(groups); err != nil {
			return pageResult, err
		}
		pageResult.Items = groups
	}

	return pageResult, nil
}

// FindOrCreateByName 根据名称查找
func (s *EycMeetingGroupService) FindOrCreateByName(c context.Context, name string) (*model.EycMeetingGroup, error) {
	if name == "" {
		return nil, errors.New("分组名称不能为空")
	}

	rawCorpID, exists := c.Value("corp_id").(string)
	if !exists || rawCorpID == "" {
		return nil, errors.New("无法从 context 中获取 corp_id")
	}

	group := &model.EycMeetingGroup{
		Title:  name,
		Corpid: rawCorpID,
	}

	err := model.DB.Where("title = ? AND corpid = ?", name, rawCorpID).FirstOrCreate(group).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("查找或创建分组失败: %w", err)
	}

	return group, nil
}

// populateRoomsForGroup 为单个分组填充会议室信息
func (s *EycMeetingGroupService) populateRoomsForGroup(group *model.EycMeetingGroup) error {
	if group == nil {
		return errors.New("分组不能为空")
	}

	if group.RoomIds == "" {
		group.Rooms = []model.EycMeetingRoom{}
		return nil
	}

	roomIDStrs := strings.Split(group.RoomIds, ",")
	var roomIDs []int
	for _, idStr := range roomIDStrs {
		if id, err := strconv.Atoi(strings.TrimSpace(idStr)); err == nil {
			roomIDs = append(roomIDs, id)
		}
	}

	if len(roomIDs) == 0 {
		group.Rooms = []model.EycMeetingRoom{}
		return nil
	}

	var rooms []model.EycMeetingRoom
	if err := model.DB.Where("id IN ?", roomIDs).Find(&rooms).Error; err != nil {
		return err
	}

	group.Rooms = rooms
	group.RoomCount = len(rooms)
	return nil
}

// populateRoomsForGroups 为分组列表填充会议室信息
func (s *EycMeetingGroupService) populateRoomsForGroups(groups []model.EycMeetingGroup) error {
	if len(groups) == 0 {
		return nil
	}

	allRoomIDs := make(map[int]bool)
	for _, group := range groups {
		if group.RoomIds == "" {
			continue
		}
		roomIDStrs := strings.Split(group.RoomIds, ",")
		for _, idStr := range roomIDStrs {
			if id, err := strconv.Atoi(strings.TrimSpace(idStr)); err == nil {
				allRoomIDs[id] = true
			}
		}
	}

	var roomIDList []int
	for id := range allRoomIDs {
		roomIDList = append(roomIDList, id)
	}

	if len(roomIDList) == 0 {
		for i := range groups {
			groups[i].Rooms = []model.EycMeetingRoom{}
		}
		return nil
	}

	var allRooms []model.EycMeetingRoom
	if err := model.DB.Where("id IN ?", roomIDList).Find(&allRooms).Error; err != nil {
		return err
	}

	roomMap := make(map[int]model.EycMeetingRoom)
	for _, room := range allRooms {
		roomMap[room.ID] = room
	}

	for i := range groups {
		group := &groups[i]
		if group.RoomIds == "" {
			group.Rooms = []model.EycMeetingRoom{}
			continue
		}
		var groupRooms []model.EycMeetingRoom
		roomIDStrs := strings.Split(group.RoomIds, ",")
		for _, idStr := range roomIDStrs {
			if id, err := strconv.Atoi(strings.TrimSpace(idStr)); err == nil {
				if room, ok := roomMap[id]; ok {
					groupRooms = append(groupRooms, room)
				}
			}
		}
		group.Rooms = groupRooms
		group.RoomCount = len(groupRooms)
	}

	return nil
}
