package meeting

import (
	"eyc3_meeting/internal/controller"
	"eyc3_meeting/internal/middleware"

	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/app/server"
)

// RegisterDeviceRoutes 注册设备管理相关接口
func RegisterDeviceRoutes(r *server.Hertz) {
	device := r.Group("/device", middleware.JWT())

	// 设备管理相关接口
	device.POST("/post_add", controller.AddEycMeetingDevice)
	device.POST("/post_modify", controller.ModifyEycMeetingDevice)
	device.POST("/post_del", controller.DeleteEycMeetingDevice)
	device.GET("/get_info", controller.GetEycMeetingDeviceInfo)
	device.POST("/get_ls", controller.GetEycMeetingDeviceList)
	device.GET("/get_all", controller.GetAllEycMeetingDevice)

	// 新增设备类型统计接口
	device.GET("/type_count", controller.GetDeviceTypeCount)
	device.GET("/list_all", controller.GetDevicesAndGroups)
	// 设备分组相关接口
	device_group := r.Group("/device/group", middleware.JWT())

	device_group.POST("/post_add", controller.AddEycDeviceGroup)
	device_group.POST("/post_modify", controller.ModifyEycDeviceGroup)
	device_group.POST("/post_del", controller.DeleteEycDeviceGroup)
	device_group.GET("/get_info", controller.GetEycDeviceGroupInfo)
	device_group.POST("/get_ls", func(c context.Context, ctx *app.RequestContext) {
		controller.GetDeviceGroupListWithCount(c, ctx)
	})
	device_group.GET("/get_all", controller.GetAllEycDeviceGroups)

}
