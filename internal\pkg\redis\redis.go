package redis

import (
	"fmt"
	"time"

	"eyc3_meeting/config"
	"eyc3_meeting/internal/pkg/logger"

	"github.com/gomodule/redigo/redis"
)

var (
	// Pool Redis连接池
	Pool *redis.Pool
)

// Init 初始化Redis连接池
func Init() {
	redisConfig := config.AppConfig.Redis

	Pool = &redis.Pool{
		MaxIdle:     10,
		MaxActive:   50,
		IdleTimeout: 240 * time.Second,
		Wait:        true,
		Dial: func() (redis.Conn, error) {
			c, err := redis.Dial("tcp", redisConfig.URL,
				redis.DialPassword(redisConfig.Password),
				redis.DialDatabase(redisConfig.DB),
				redis.DialConnectTimeout(5*time.Second),
				redis.DialReadTimeout(3*time.Second),
				redis.DialWriteTimeout(3*time.Second),
			)
			if err != nil {
				logger.Error("Redis连接失败", logger.Error2(err))
				return nil, err
			}
			return c, nil
		},
		TestOnBorrow: func(c redis.Conn, t time.Time) error {
			if time.Since(t) < time.Minute {
				return nil
			}
			_, err := c.Do("PING")
			if err != nil {
				logger.Error("Redis PING失败", logger.Error2(err))
			}
			return err
		},
	}

	// 测试连接
	conn := Pool.Get()
	defer conn.Close()

	_, err := conn.Do("PING")
	if err != nil {
		logger.Error("Redis初始化失败", logger.Error2(err))
		panic(fmt.Sprintf("Redis初始化失败: %v", err))
	}

	logger.Info("Redis初始化成功")
}

// Get 获取Redis连接
func Get() redis.Conn {
	return Pool.Get()
}

// Set 设置Redis键值对
func Set(key string, value interface{}, expiration int) error {
	conn := Get()
	defer conn.Close()

	if expiration > 0 {
		_, err := conn.Do("SETEX", key, expiration, value)
		return err
	}

	_, err := conn.Do("SET", key, value)
	return err
}

// Get2 获取Redis值
func Get2(key string) (string, error) {
	conn := Get()
	defer conn.Close()

	return redis.String(conn.Do("GET", key))
}

// Delete 删除Redis键
func Delete(key string) error {
	conn := Get()
	defer conn.Close()

	_, err := conn.Do("DEL", key)
	return err
}

// Exists 检查键是否存在
func Exists(key string) (bool, error) {
	conn := Get()
	defer conn.Close()

	return redis.Bool(conn.Do("EXISTS", key))
}
