package service

import (
	"eyc3_meeting/internal/model"
	"fmt" // Example: if you add error wrapping

	"github.com/cloudwego/hertz/pkg/app"
)

// MenuService 菜单服务
type MenuService struct {
	BaseServiceImpl[model.Menu] // 继承BaseServiceImpl的所有方法
}

// AddService 添加新菜单项
// 参数: ctx - 请求上下文, data - 需要添加的菜单项数据（应为*model.Menu类型）
// 返回: uint - 新创建菜单的ID, error - 如果操作过程中发生错误，则返回错误信息
func (s *MenuService) AddService(ctx *app.RequestContext, data interface{}) (uint, error) {
	menu, ok := data.(*model.Menu)
	if !ok {
		return 0, fmt.Errorf("参数类型错误，期望*model.Menu，实际为%T", data)
	}
	// 1. (可选) 在这里添加特定于菜单的业务逻辑
	//    例如:
	//    - 校验菜单特有的字段 (e.g., menu.Path, menu.Component)
	//    - 设置默认的 Order 值（如果 BaseServiceImpl 不处理或需要特定逻辑）
	//      if menu.Order == 0 {
	//          // 假设 model.GetMaxMenuOrderForParent(appID, parentID) 获取同级最大Order
	//          maxOrder, err := model.GetMaxMenuOrderForParent(menu.AppID, menu.ParentID) // Эта строка вызовет ошибку, так как GetMaxMenuOrderForParent не определена
	//          if err != nil {
	//              return 0, fmt.Errorf("获取最大菜单顺序失败: %w", err)
	//          }
	//          menu.Order = maxOrder + 1
	//      }
	//    - 检查父菜单ID是否存在 (如果 ParentID != 0)
	//      if menu.ParentID != 0 {
	//          _, found, err := s.GetInfoService(menu.ParentID) // Uses BaseServiceImpl.GetInfoService
	//          if err != nil {
	//              return 0, fmt.Errorf("检查父菜单失败: %w", err)
	//          }
	//          if !found {
	//              return 0, fmt.Errorf("父菜单 ID %d 不存在", menu.ParentID)
	//          }
	//      }

	// 2. 调用嵌入的 BaseServiceImpl 的 AddService 方法来执行通用的添加逻辑
	newID, err := s.BaseServiceImpl.AddService(ctx, menu)
	if err != nil {
		return 0, fmt.Errorf("添加菜单失败: %w", err) // 可以选择包装错误
	}

	// 3. (可选) 在菜单成功添加后，执行其他特定于菜单的逻辑
	//    例如: 清除相关缓存，发送通知等

	return newID, nil
}
