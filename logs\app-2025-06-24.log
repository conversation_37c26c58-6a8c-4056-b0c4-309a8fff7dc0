{"level":"INFO","time":"2025-06-24T09:50:46.719+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:46.966+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":221,"影响行数":1}
{"level":"INFO","time":"2025-06-24T09:50:47.122+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":155,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:47.202+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:47.409+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":206,"影响行数":1}
{"level":"INFO","time":"2025-06-24T09:50:47.619+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":208,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:47.899+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":279,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:48.026+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:48.325+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":298,"影响行数":1}
{"level":"INFO","time":"2025-06-24T09:50:48.554+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":229,"影响行数":3}
{"level":"INFO","time":"2025-06-24T09:50:48.622+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:48.732+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-24T09:50:48.902+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":169,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:49.009+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:49.169+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-06-24T09:50:49.325+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":155,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:49.385+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:49.620+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":233,"影响行数":1}
{"level":"INFO","time":"2025-06-24T09:50:49.815+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":195,"影响行数":3}
{"level":"INFO","time":"2025-06-24T09:50:49.865+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:49.953+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":86,"影响行数":1}
{"level":"INFO","time":"2025-06-24T09:50:50.040+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:50.098+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:50.245+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-06-24T09:50:50.357+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":112,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:50.430+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":72,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:50.584+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":153,"影响行数":1}
{"level":"INFO","time":"2025-06-24T09:50:50.682+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:50.716+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":34,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:50.849+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-06-24T09:50:51.016+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":165,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T09:50:51.017+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-24T09:50:51.124+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-24T09:50:51.125+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-24T09:50:51.126+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-24T09:50:51.126+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"WARN","time":"2025-06-24T13:34:57.248+0800","caller":"model/logger.go:81","msg":"GORM慢查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 4 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":1038,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:34:57.473+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND id != 4 AND (title = '测试门牌001' OR sn = '测试门牌001')","耗时":224,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:34:57.751+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND id != 4 AND (title = 'SN00123456787' OR sn = 'SN00123456787')","耗时":278,"影响行数":1}
{"level":"ERROR","time":"2025-06-24T13:34:57.958+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"UPDATE `eyc_meeting_device` SET `corp_id`='ding424e63f5c9ac81e1ffe93478753d9884',`title`='测试门牌001',`sn`='SN00123456787',`model`='D3',`device_type`='1',`light_brightness`=90,`volume`=80,`custom_template`=1,`template_free`='#1678FF',`template_idle`='#1678FF',`template_using`='#F7885C',`related_door`=0,`system_mode`='1',`network`='YCSERV',`ip`='********',`mac`='34:04:9E:61:D3:D4',`auto_power`=1,`power_on_time`='0000-01-01 08:00:00',`power_off_time`='0000-01-01 22:00:00',`status`=0,`created_at`='2025-06-23 14:09:37',`updated_at`='2025-06-24 13:34:57.879',`template_image`='0' WHERE `id` = 4","耗时":79,"error":"year is not in the range [1, 9999]: 0"}
{"level":"ERROR","time":"2025-06-24T13:34:58.111+0800","caller":"controller/base.go:466","msg":"修改记录失败","error":"year is not in the range [1, 9999]: 0"}
{"level":"ERROR","time":"2025-06-24T13:35:07.191+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"分组名称不能为空"}
{"level":"INFO","time":"2025-06-24T13:35:18.888+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND FIND_IN_SET(1, device_ids) ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":287,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:35:19.581+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":693,"影响行数":1}
{"level":"ERROR","time":"2025-06-24T13:35:19.857+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备 '修改后的设备名' 已存在于分组 'D2组更新' 中，不能重复添加"}
{"level":"INFO","time":"2025-06-24T13:35:34.114+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":207,"影响行数":0}
{"level":"INFO","time":"2025-06-24T13:35:39.976+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 10 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":94,"影响行数":0}
{"level":"INFO","time":"2025-06-24T13:35:45.189+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 22 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":104,"影响行数":0}
{"level":"INFO","time":"2025-06-24T13:36:39.487+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 4 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":453,"影响行数":0}
{"level":"INFO","time":"2025-06-24T13:36:52.471+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-24T13:36:52.471+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-24T13:36:52.471+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-24T13:36:57.353+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":38,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:36:57.513+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:36:57.613+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:36:57.680+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:36:57.813+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":133,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:36:57.962+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":148,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:36:58.213+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":250,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:36:58.273+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:36:58.447+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":174,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:36:58.613+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":165,"影响行数":3}
{"level":"INFO","time":"2025-06-24T13:36:58.681+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:36:58.842+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":161,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:36:59.048+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":205,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:36:59.161+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":113,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:36:59.305+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":143,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:36:59.519+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":212,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:36:59.588+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:36:59.719+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:37:00.009+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":289,"影响行数":3}
{"level":"INFO","time":"2025-06-24T13:37:00.076+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:37:00.186+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:37:00.334+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":147,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:37:00.402+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:37:00.566+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":163,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:37:00.699+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":132,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:37:00.772+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:37:00.896+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":123,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:37:01.003+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:37:01.077+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:37:01.189+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":111,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:37:01.290+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":101,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T13:37:01.290+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-24T13:37:01.400+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-24T13:37:01.401+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-24T13:37:01.401+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-24T13:37:01.401+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-24T13:37:11.712+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 4 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":191,"影响行数":0}
{"level":"INFO","time":"2025-06-24T13:37:31.449+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 20 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":611,"影响行数":0}
{"level":"INFO","time":"2025-06-24T13:37:39.852+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND FIND_IN_SET(1, device_ids) ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":375,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:37:39.999+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":147,"影响行数":1}
{"level":"ERROR","time":"2025-06-24T13:37:40.081+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备 '修改后的设备名' 已存在于分组 'D2组更新' 中，不能重复添加"}
{"level":"INFO","time":"2025-06-24T13:37:44.781+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND FIND_IN_SET(1, device_ids) ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":153,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:37:44.969+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":188,"影响行数":1}
{"level":"ERROR","time":"2025-06-24T13:37:45.110+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备 '修改后的设备名' 已存在于分组 'D2组更新' 中，不能重复添加"}
{"level":"INFO","time":"2025-06-24T13:38:07.075+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND FIND_IN_SET(1, device_ids) ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":251,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:38:07.362+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":286,"影响行数":1}
{"level":"ERROR","time":"2025-06-24T13:38:07.511+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备 '修改后的设备名' 已存在于分组 'D2组更新' 中，不能重复添加"}
{"level":"ERROR","time":"2025-06-24T13:38:20.173+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备分组类型不能为空"}
{"level":"INFO","time":"2025-06-24T13:38:34.822+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = 'gy'","耗时":216,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:38:35.022+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device_group` (`corp_id`,`title`,`device_type`,`device_ids`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','gy','0','','2025-06-24 13:38:34.823','2025-06-24 13:38:34.823')","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:38:50.091+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = 'gy'","耗时":127,"影响行数":1}
{"level":"ERROR","time":"2025-06-24T13:38:50.255+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"分组名称已存在"}
{"level":"INFO","time":"2025-06-24T13:39:28.048+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 6 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":257,"影响行数":1}
{"level":"INFO","time":"2025-06-24T13:39:28.286+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id IN (1,2)","耗时":238,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:21:10.831+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-24T15:21:10.832+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-24T15:21:10.833+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-24T15:23:57.338+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:23:57.468+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":113,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:23:57.598+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:23:57.668+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:23:57.784+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":116,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:23:57.913+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:23:58.122+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":208,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:23:58.164+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":40,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:23:58.320+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":156,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:23:58.488+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":167,"影响行数":3}
{"level":"INFO","time":"2025-06-24T15:23:58.620+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:23:58.798+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":177,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:23:59.003+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":204,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:23:59.083+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:23:59.243+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:23:59.388+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:23:59.469+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:23:59.628+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:23:59.791+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":162,"影响行数":3}
{"level":"INFO","time":"2025-06-24T15:23:59.846+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:24:00.008+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:24:00.147+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":138,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:24:00.224+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":75,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:24:00.423+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":198,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:24:00.663+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":239,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:24:00.762+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:24:00.884+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:24:01.066+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":182,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:24:01.123+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:24:01.285+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":160,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:24:01.458+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":173,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T15:24:01.458+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-24T15:24:01.589+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-24T15:24:01.591+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-24T15:24:01.591+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-24T15:24:01.592+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-24T15:24:12.503+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = 'gy'","耗时":140,"影响行数":1}
{"level":"ERROR","time":"2025-06-24T15:24:12.588+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"分组名称已存在"}
{"level":"INFO","time":"2025-06-24T15:24:16.581+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 6 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":170,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:24:16.744+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id IN (1,2)","耗时":163,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:24:25.644+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 3 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":186,"影响行数":0}
{"level":"ERROR","time":"2025-06-24T15:24:25.644+0800","caller":"controller/base.go:466","msg":"修改记录失败","error":"设备分组不存在"}
{"level":"INFO","time":"2025-06-24T15:24:36.816+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 6 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":186,"影响行数":1}
{"level":"ERROR","time":"2025-06-24T15:24:36.816+0800","caller":"controller/base.go:466","msg":"修改记录失败","error":"分组名称不能为空"}
{"level":"INFO","time":"2025-06-24T15:24:45.084+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 6 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:24:45.270+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE id != 6 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND FIND_IN_SET(1, device_ids) ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":138,"影响行数":0}
{"level":"INFO","time":"2025-06-24T15:24:45.412+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE id != 6 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND FIND_IN_SET(2, device_ids) ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":141,"影响行数":0}
{"level":"INFO","time":"2025-06-24T15:24:45.566+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = 'D2组更新' AND id != 6","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:24:45.730+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='1,2',`device_type`='1',`title`='D2组更新',`updated_at`='2025-06-24 15:24:45.566' WHERE id = 6","耗时":163,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:24:45.880+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device` SET `group_id`=0,`updated_at`='2025-06-24 15:24:45.731' WHERE group_id = 6","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:24:45.982+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE id IN (1,2) AND group_id != 0","耗时":102,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:24:46.159+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device` SET `group_id`=6,`updated_at`='2025-06-24 15:24:45.983' WHERE id IN (1,2)","耗时":176,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:24:59.502+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 6 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":231,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:24:59.738+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device` SET `group_id`=0,`updated_at`='2025-06-24 15:24:59.606' WHERE id IN (1,2)","耗时":133,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:24:59.925+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 6","耗时":187,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:25:04.551+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":107,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:25:04.704+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY id desc LIMIT 10","耗时":152,"影响行数":3}
{"level":"INFO","time":"2025-06-24T15:31:52.098+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-06-24T15:31:52.332+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY id desc LIMIT 10","耗时":234,"影响行数":3}
{"level":"INFO","time":"2025-06-24T16:17:05.938+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-24T16:17:05.939+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-24T16:17:05.939+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-24T16:17:11.450+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:11.745+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":267,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:17:11.963+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":218,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:12.069+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:12.225+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":155,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:17:12.444+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":218,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:12.703+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":257,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:12.855+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":151,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:13.110+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":253,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:17:13.342+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":231,"影响行数":3}
{"level":"INFO","time":"2025-06-24T16:17:13.497+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":155,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:13.730+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":231,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:17:14.118+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":388,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:14.252+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:14.458+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":205,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:17:14.782+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":323,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:14.974+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":190,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:15.264+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":289,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:17:15.628+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":363,"影响行数":3}
{"level":"INFO","time":"2025-06-24T16:17:15.804+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":175,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:16.210+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":405,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:17:16.529+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":318,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:16.677+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":147,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:17.023+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":344,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:17:17.343+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":319,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:17.504+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":160,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:17.823+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":318,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:17:18.152+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":329,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:18.215+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:18.515+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":299,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:17:18.737+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":221,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:17:18.737+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-24T16:17:19.054+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-24T16:17:19.056+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-24T16:17:19.056+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-24T16:17:19.058+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-24T16:35:07.275+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-24T16:35:07.276+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-24T16:35:07.276+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-24T16:35:11.609+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":81,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:11.857+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":221,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:35:12.043+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":184,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:12.173+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:12.411+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":237,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:35:12.695+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":284,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:12.990+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":293,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:13.193+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":201,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:13.392+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:35:13.675+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":283,"影响行数":3}
{"level":"INFO","time":"2025-06-24T16:35:13.780+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":103,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:14.035+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":255,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:35:14.361+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":325,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:14.423+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:14.591+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":167,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:35:14.828+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":237,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:14.966+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:15.131+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":163,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:35:15.301+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":169,"影响行数":3}
{"level":"INFO","time":"2025-06-24T16:35:15.450+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":148,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:15.827+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":376,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:35:16.076+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":248,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:16.184+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:16.383+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:35:16.575+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":191,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:16.650+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:16.831+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:35:17.051+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":218,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:17.131+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:17.292+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":161,"影响行数":1}
{"level":"INFO","time":"2025-06-24T16:35:17.471+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":177,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T16:35:17.472+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-24T16:35:17.633+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-24T16:35:17.634+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-24T16:35:17.634+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-24T16:35:17.635+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-24T16:35:37.383+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":196,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:28:43.140+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-24T17:28:43.140+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-24T17:28:43.141+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-24T17:29:06.866+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":51,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:07.050+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":155,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:29:07.271+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":220,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:07.413+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:07.501+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":86,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:29:07.590+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":88,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:07.802+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":212,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:07.875+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:07.945+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":70,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:29:08.043+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":97,"影响行数":3}
{"level":"INFO","time":"2025-06-24T17:29:08.098+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:08.203+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":103,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:29:08.293+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:08.335+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":41,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:08.469+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:29:08.600+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":131,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:08.663+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:08.793+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:29:08.913+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":119,"影响行数":3}
{"level":"INFO","time":"2025-06-24T17:29:08.965+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":51,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:09.080+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:29:09.168+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:09.231+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:09.348+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":116,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:29:09.488+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":138,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:09.550+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:09.755+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":203,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:29:09.838+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:09.886+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:09.998+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":111,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:29:10.120+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:29:10.121+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-24T17:29:10.233+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-24T17:29:10.234+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-24T17:29:10.234+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-24T17:29:10.235+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-24T17:29:26.533+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":142,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:29:35.613+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":97,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:29:50.454+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":158,"影响行数":11}
{"level":"INFO","time":"2025-06-24T17:29:57.577+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":125,"影响行数":11}
{"level":"INFO","time":"2025-06-24T17:29:57.685+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":108,"影响行数":3}
{"level":"INFO","time":"2025-06-24T17:49:01.968+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-24T17:49:01.973+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-24T17:49:01.973+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-24T17:49:08.874+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":47,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:09.016+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:49:09.117+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:09.226+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:09.441+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":214,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:49:09.550+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":108,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:09.659+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":107,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:09.710+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":50,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:09.797+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":86,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:49:09.898+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":100,"影响行数":3}
{"level":"INFO","time":"2025-06-24T17:49:09.940+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":41,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:10.041+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":100,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:49:10.138+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:10.172+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":32,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:10.296+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":123,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:49:10.454+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":157,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:10.532+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:10.628+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":96,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:49:10.759+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":130,"影响行数":3}
{"level":"INFO","time":"2025-06-24T17:49:10.800+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":40,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:10.923+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":121,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:49:11.041+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:11.094+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":52,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:11.284+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":189,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:49:11.381+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:11.411+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":28,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:11.587+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":176,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:49:11.684+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:11.748+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:11.878+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:49:12.006+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":125,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T17:49:12.006+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-24T17:49:12.096+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-24T17:49:12.098+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-24T17:49:12.098+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-24T17:49:12.099+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-24T17:49:47.350+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = 'gy'","耗时":218,"影响行数":1}
{"level":"ERROR","time":"2025-06-24T17:49:47.430+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"分组名称已存在"}
{"level":"INFO","time":"2025-06-24T17:49:53.663+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = 'gyy'","耗时":211,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:49:53.857+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device_group` (`corp_id`,`title`,`device_type`,`device_ids`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','gyy','0','','2025-06-24 17:49:53.663','2025-06-24 17:49:53.663')","耗时":194,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:50:00.375+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 6 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":228,"影响行数":0}
{"level":"INFO","time":"2025-06-24T17:50:09.862+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 7 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":89,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:50:09.957+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id IN (20) ORDER BY CONVERT(title USING gbk) asc","耗时":94,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:50:19.199+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 8 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":68,"影响行数":0}
{"level":"INFO","time":"2025-06-24T17:50:25.318+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 9 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":156,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:50:25.537+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id IN (31) ORDER BY CONVERT(title USING gbk) asc","耗时":219,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:50:34.073+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 6 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":176,"影响行数":0}
{"level":"INFO","time":"2025-06-24T17:50:42.108+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 8 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":88,"影响行数":0}
{"level":"INFO","time":"2025-06-24T17:50:50.273+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 9 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":107,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:50:50.503+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE id != 9 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND FIND_IN_SET(1, device_ids) ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":177,"影响行数":0}
{"level":"INFO","time":"2025-06-24T17:50:50.674+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE id != 9 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND FIND_IN_SET(2, device_ids) ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":170,"影响行数":0}
{"level":"INFO","time":"2025-06-24T17:50:50.780+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = 'D2组更新' AND id != 9","耗时":106,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:50:50.948+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='1,2',`device_type`='1',`title`='D2组更新',`updated_at`='2025-06-24 17:50:50.78' WHERE id = 9","耗时":168,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:50:56.133+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:50:56.314+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY id desc LIMIT 10","耗时":180,"影响行数":4}
{"level":"INFO","time":"2025-06-24T17:51:09.758+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":178,"影响行数":11}
{"level":"INFO","time":"2025-06-24T17:51:09.913+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":154,"影响行数":4}
{"level":"INFO","time":"2025-06-24T17:51:21.828+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":160,"影响行数":11}
{"level":"INFO","time":"2025-06-24T17:51:46.016+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":102,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:52:13.933+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 8 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":158,"影响行数":0}
{"level":"ERROR","time":"2025-06-24T17:52:14.027+0800","caller":"controller/base.go:494","msg":"删除记录失败","id":8,"error":"设备不存在或无权访问"}
{"level":"INFO","time":"2025-06-24T17:52:23.032+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 6 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":79,"影响行数":0}
{"level":"ERROR","time":"2025-06-24T17:52:23.091+0800","caller":"controller/base.go:494","msg":"删除记录失败","id":6,"error":"设备不存在或无权访问"}
{"level":"INFO","time":"2025-06-24T17:52:27.558+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 11 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":118,"影响行数":0}
{"level":"ERROR","time":"2025-06-24T17:52:27.630+0800","caller":"controller/base.go:494","msg":"删除记录失败","id":11,"error":"设备不存在或无权访问"}
{"level":"INFO","time":"2025-06-24T17:52:42.038+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":244,"影响行数":11}
{"level":"INFO","time":"2025-06-24T17:52:58.796+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 1 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":195,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:52:58.960+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:53:13.441+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":242,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:53:13.736+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY id DESC","耗时":295,"影响行数":10}
{"level":"ERROR","time":"2025-06-24T17:53:19.823+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"INFO","time":"2025-06-24T17:53:31.404+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌001' OR sn = '测试门牌001')","耗时":141,"影响行数":1}
{"level":"ERROR","time":"2025-06-24T17:53:31.404+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称已被占用"}
{"level":"INFO","time":"2025-06-24T17:53:39.256+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌0011' OR sn = '测试门牌0011')","耗时":98,"影响行数":1}
{"level":"INFO","time":"2025-06-24T17:53:39.338+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = 'SN00123456787' OR sn = 'SN00123456787')","耗时":81,"影响行数":1}
{"level":"ERROR","time":"2025-06-24T17:53:39.338+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备SN号已被占用"}
{"level":"INFO","time":"2025-06-24T17:53:53.333+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌001' OR sn = '测试门牌001')","耗时":115,"影响行数":1}
{"level":"ERROR","time":"2025-06-24T17:53:53.333+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称已被占用"}
{"level":"INFO","time":"2025-06-24T17:54:03.468+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":75,"影响行数":2}
{"level":"INFO","time":"2025-06-24T17:54:26.287+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":246,"影响行数":10}
{"level":"INFO","time":"2025-06-24T18:06:19.561+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 9 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":126,"影响行数":1}
{"level":"INFO","time":"2025-06-24T18:06:19.691+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id IN (1,2) ORDER BY CONVERT(title USING gbk) asc","耗时":129,"影响行数":0}
{"level":"INFO","time":"2025-06-24T18:06:41.964+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-24T18:06:41.964+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-24T18:06:41.964+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-24T18:06:43.757+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:43.917+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-06-24T18:06:44.009+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":91,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:44.071+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:44.173+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-06-24T18:06:44.303+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:44.437+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:44.531+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:44.710+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":178,"影响行数":1}
{"level":"INFO","time":"2025-06-24T18:06:44.872+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":161,"影响行数":3}
{"level":"INFO","time":"2025-06-24T18:06:44.941+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:45.090+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":148,"影响行数":1}
{"level":"INFO","time":"2025-06-24T18:06:45.245+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":155,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:45.326+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:45.568+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":240,"影响行数":1}
{"level":"INFO","time":"2025-06-24T18:06:45.752+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":182,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:45.838+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:46.012+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":173,"影响行数":1}
{"level":"INFO","time":"2025-06-24T18:06:46.141+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":129,"影响行数":3}
{"level":"INFO","time":"2025-06-24T18:06:46.201+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:46.371+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":170,"影响行数":1}
{"level":"INFO","time":"2025-06-24T18:06:46.502+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:46.567+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:46.718+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-06-24T18:06:46.820+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":102,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:46.854+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":32,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:46.967+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":112,"影响行数":1}
{"level":"INFO","time":"2025-06-24T18:06:47.142+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":174,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:47.221+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:47.376+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-06-24T18:06:47.511+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":134,"影响行数":-1}
{"level":"INFO","time":"2025-06-24T18:06:47.513+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-24T18:06:47.634+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-24T18:06:47.636+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-24T18:06:47.636+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-24T18:06:47.637+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-24T18:08:33.634+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 9 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":113,"影响行数":1}
{"level":"INFO","time":"2025-06-24T18:08:33.843+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id IN (1,2) ORDER BY CONVERT(title USING gbk) asc","耗时":209,"影响行数":0}
{"level":"INFO","time":"2025-06-24T18:08:37.678+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-06-24T18:08:37.863+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY id desc LIMIT 10","耗时":185,"影响行数":4}
{"level":"INFO","time":"2025-06-24T18:37:25.557+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌001' OR sn = '测试门牌001')","耗时":132,"影响行数":1}
{"level":"ERROR","time":"2025-06-24T18:37:25.560+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称已被占用"}
{"level":"INFO","time":"2025-06-24T18:37:42.052+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌001' OR sn = '测试门牌001')","耗时":171,"影响行数":1}
{"level":"ERROR","time":"2025-06-24T18:37:42.053+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称已被占用"}
{"level":"INFO","time":"2025-06-24T18:49:50.397+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-24T18:49:50.398+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-24T18:49:50.398+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
