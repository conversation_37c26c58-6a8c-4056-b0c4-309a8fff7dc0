package controller

import (
	"eyc3_meeting/internal/model"
	"eyc3_meeting/internal/service"
)

// EycMeetingBookingController 会议预订控制器
// 采用 BaseController 泛型，统一风格
type EycMeetingBookingController struct {
	BaseController[model.EycMeetingBooking, *service.EycMeetingBookingService]
}

// 全局会议预订服务与控制器实例
var (
	eycMeetingBookingService    = new(service.EycMeetingBookingService)
	eycMeetingBookingController = EycMeetingBookingController{
		BaseController: BaseController[model.EycMeetingBooking, *service.EycMeetingBookingService]{Service: eycMeetingBookingService},
	}
)

// 标准CRUD接口实现
var (
	// GetEycMeetingBookingList 获取会议预订列表（分页、可筛选）
	GetEycMeetingBookingList = eycMeetingBookingController.GetList(true,
		"keyword", "room_ids", "created_by", "start_time", "end_time",
	)

	// 获取所有预订
	GetAllEycMeetingBookings = eycMeetingBookingController.GetAll(true, "title:%")

	// GetEycMeetingBookingInfo 获取单个会议预订详情
	GetEycMeetingBookingInfo = eycMeetingBookingController.GetInfo()

	// AddEycMeetingBooking 新增会议预订
	AddEycMeetingBooking = eycMeetingBookingController.Add(true)

	// ModifyEycMeetingBooking 修改会议预订
	ModifyEycMeetingBooking = eycMeetingBookingController.Modify(true)

	// DeleteEycMeetingBooking 删除会议预订
	DeleteEycMeetingBooking = eycMeetingBookingController.Delete()

	// QuickBookMeeting 快速预订会议
	QuickBookMeeting = eycMeetingBookingController.Add(true, "room_ids:i,date,time_slots,participants")

	// GetAvailableRooms 获取可用会议室列表
	GetAvailableRooms = eycMeetingBookingController.GetList(true, "date,group_id:i,keyword:%,time_slots")
)
