package service

import (
	"eyc3_meeting/internal/model"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/cloudwego/hertz/pkg/app"
)

// EycMeetingScheduleService 会议日程服务
type EycMeetingScheduleService struct{}

// ScheduleRequest 列表时间视图请求参数
type ScheduleRequest struct {
	Date    string `json:"date" query:"date"`         // YYYY-MM-DD 格式
	GroupID int    `json:"group_id" query:"group_id"` // 可选的会议室分组ID
}

// TimeSlot 定义了时间轴上的一个时间段
type TimeSlot struct {
	StartTime string `json:"start_time"` // HH:mm
	EndTime   string `json:"end_time"`   // HH:mm
	Status    string `json:"status"`     // available, booked, past
}

// RoomScheduleResponse 每个会议室及其对应的时间轴
type RoomScheduleResponse struct {
	model.EycMeetingRoom
	Slots []TimeSlot `json:"slots"`
}

// parseFlexibleDate 尝试使用多种布局解析日期字符串
func parseFlexibleDate(dateStr string) (time.Time, error) {
	// 定义所有支持的日期格式布局
	layouts := []string{
		"2006-01-02", // YYYY-MM-DD
		"2006-1-2",   // YYYY-M-D
		"2006-01-2",  // YYYY-MM-D
		"2006-1-02",  // YYYY-M-DD
	}

	for _, layout := range layouts {
		parsedTime, err := time.ParseInLocation(layout, dateStr, time.Local)
		if err == nil {
			return parsedTime, nil // 一旦成功，立即返回
		}
	}

	// 如果所有格式都失败了
	return time.Time{}, fmt.Errorf("日期格式无效或不支持，请使用 YYYY-MM-DD 格式")
}

// GetListTimeView 获取会议室列表时间视图的核心逻辑
func (s *EycMeetingScheduleService) GetListTimeView(ac *app.RequestContext, req *ScheduleRequest) ([]RoomScheduleResponse, error) {
	corpID, _ := ac.Get("corp_id")
	corpIDStr, _ := corpID.(string)

	// 1. 获取符合条件的会议室列表
	var rooms []model.EycMeetingRoom
	db := model.DB.Model(&model.EycMeetingRoom{}).Where("corpid = ?", corpIDStr)
	if req.GroupID > 0 {
		db = db.Where("group_id = ?", req.GroupID)
	}
	if err := db.Order("sort ASC, id DESC").Find(&rooms).Error; err != nil {
		return nil, fmt.Errorf("查询会议室列表失败: %w", err)
	}
	if len(rooms) == 0 {
		return []RoomScheduleResponse{}, nil
	}

	// 2. 获取指定日期的所有预定记录
	dayStart, err := parseFlexibleDate(req.Date)
	if err != nil {
		return nil, err // 直接返回辅助函数生成的错误
	}

	var bookings []model.EycMeetingBooking
	dbQuery := model.DB.Model(&model.EycMeetingBooking{}).
		Where("corp_id = ?", corpIDStr).
		Where("status != ?", model.BookingStatusCanceled).
		Where("DATE(start_time) <= ?", req.Date).
		Where("DATE(end_time) >= ?", req.Date)

	err = dbQuery.Debug().Find(&bookings).Error
	if err != nil {
		return nil, fmt.Errorf("查询预定记录失败: %w", err)
	}

	// 将预定记录按会议室ID进行映射，方便查找
	bookingMap := make(map[int][]model.EycMeetingBooking)
	for _, b := range bookings {
		roomIDs := strings.Split(b.RoomIDs, ",")
		for _, roomIDStr := range roomIDs {
			roomID, err := strconv.Atoi(roomIDStr)
			if err == nil {
				bookingMap[roomID] = append(bookingMap[roomID], b)
			}
		}
	}

	// 3. 为每个会议室生成时间轴
	var response []RoomScheduleResponse
	now := time.Now()
	// 定义工作时间范围和时间片间隔
	workingStartHour, workingEndHour, slotDuration := 8, 22, 30*time.Minute

	for _, room := range rooms {
		var slots []TimeSlot
		// 遍历工作时间，生成时间片
		for t := dayStart.Add(time.Duration(workingStartHour) * time.Hour); t.Hour() < workingEndHour; t = t.Add(slotDuration) {
			slotStart := t
			slotEnd := t.Add(slotDuration)

			slot := TimeSlot{
				StartTime: slotStart.Format("15:04"),
				EndTime:   slotEnd.Format("15:04"),
				Status:    "available", // 默认为可用
			}

			// 检查当前时间，判断是否已过期
			if slotEnd.Before(now) {
				slot.Status = "past"
			}

			// 检查该时间片是否已被预定
			if roomBookings, ok := bookingMap[room.ID]; ok {
				for _, b := range roomBookings {
					// 判断时间片是否与预定记录重叠: (start1 < end2) && (start2 < end1)
					if slotStart.Before(b.EndTime) && b.StartTime.Before(slotEnd) {
						slot.Status = "booked"
						break // 一旦发现重叠，即可确定为booked，跳出内层循环
					}
				}
			}

			// 如果过期状态下又被预定了，覆盖为booked
			if slot.Status == "past" {
				if roomBookings, ok := bookingMap[room.ID]; ok {
					for _, b := range roomBookings {
						if slotStart.Before(b.EndTime) && b.StartTime.Before(slotEnd) {
							slot.Status = "booked"
							break
						}
					}
				}
			}

			slots = append(slots, slot)
		}

		response = append(response, RoomScheduleResponse{
			EycMeetingRoom: room,
			Slots:          slots,
		})
	}

	return response, nil
}
