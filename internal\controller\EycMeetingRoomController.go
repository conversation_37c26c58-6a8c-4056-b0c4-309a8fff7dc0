package controller

import (
	"context"
	"eyc3_meeting/internal/model"
	"eyc3_meeting/internal/pkg/response"
	"eyc3_meeting/internal/service"

	"github.com/cloudwego/hertz/pkg/app"
)

// EycMeetingRoomController 会议室控制器
type EycMeetingRoomController struct {
	BaseController[model.EycMeetingRoom, *service.EycMeetingRoomService]
}

// 全局会议室服务与控制器实例
var (
	eycMeetingRoomService    = new(service.EycMeetingRoomService)
	eycMeetingRoomController = EycMeetingRoomController{
		BaseController: BaseController[model.EycMeetingRoom, *service.EycMeetingRoomService]{Service: eycMeetingRoomService},
	}
)

// --- 标准CRUD接口实现 ---
var (
	// GetEycMeetingRoomList 获取会议室列表（分页、可筛选）
	// 支持的筛选字段: meet_room_name (模糊), group_id (精确)
	GetEycMeetingRoomList = eycMeetingRoomController.GetList(true, "meet_room_name:%,group_id")

	// GetAllEycMeetingRooms 获取所有会议室
	// 支持的筛选字段: meet_room_name (模糊), group_id (精确)
	GetAllEycMeetingRooms = eycMeetingRoomController.GetAll(true, "meet_room_name:%,group_id")

	// GetEycMeetingRoomInfo 获取单个会议室详情
	GetEycMeetingRoomInfo = eycMeetingRoomController.GetInfo()

	// AddEycMeetingRoom 新增会议室 - 使用通用处理器
	AddEycMeetingRoom = eycMeetingRoomController.Add(true, "meet_room_name,capacity,group_id,location,facility,device,is_open_booking,image,remark")

	// ModifyEycMeetingRoom 修改会议室
	// corpid 不允许修改
	ModifyEycMeetingRoom = eycMeetingRoomController.Modify(true, "id:i,meet_room_name,capacity,group_id,location,facility,device,is_open_booking,image,remark,sort")

	// DeleteEycMeetingRoom 删除会议室
	DeleteEycMeetingRoom = eycMeetingRoomController.Delete()
)

// --- 自定义接口实现 ---

// UpdateMeetingRoomAdvancedSettingsHandler 更新会议室高级设置
// @Summary 更新会议室高级设置
// @Description 只更新传入的非空高级设置字段
// @Tags 会议室管理
// @Accept json
// @Produce json
// @Param body body model.EycMeetingRoomAdvancedSettings true "高级设置"
// @Success 200 {object} response.Response "{"code":200,"msg":"更新成功"}"
// @Router /room/post_advanced_settings [post]
func UpdateMeetingRoomAdvancedSettings(ctx context.Context, ac *app.RequestContext) {
	var req model.EycMeetingRoomAdvancedSettings
	if err := ac.BindAndValidate(&req); err != nil {
		response.BadRequest(ac, "参数错误: "+err.Error())
		return
	}
	if req.ID == 0 {
		response.BadRequest(ac, "会议室ID不能为空")
		return
	}

	if err := eycMeetingRoomService.UpdateEycMeetingRoomAdvancedSettings(ac, &req); err != nil {
		response.Fail(ac, response.StatusBadRequest, err.Error())
		return
	}

	response.Success(ac, "更新成功", false)
}

// UpdateMeetingRoomSort 更新未分组会议室排序
func UpdateMeetingRoomSort(ctx context.Context, ac *app.RequestContext) {
	var req service.RoomSortRequest
	if err := ac.BindAndValidate(&req); err != nil {
		response.BadRequest(ac, "参数错误: "+err.Error())
		return
	}

	if err := eycMeetingRoomService.UpdateEycMeetingRoomSort(ac, &req); err != nil {
		response.Fail(ac, response.StatusBadRequest, err.Error())
		return
	}
	response.Success(ac, "排序更新成功", false)
}

// MoveMeetingRoomSort 移动会议室到指定排序
func MoveMeetingRoomSort(ctx context.Context, ac *app.RequestContext) {
	var req struct {
		ID         int `json:"id"`
		TargetSort int `json:"target_sort"`
	}
	if err := ac.BindAndValidate(&req); err != nil {
		response.BadRequest(ac, "参数错误: "+err.Error())
		return
	}
	if err := eycMeetingRoomService.MoveMeetingRoomToSort(ac, req.ID, req.TargetSort); err != nil {
		response.Fail(ac, response.StatusBadRequest, err.Error())
		return
	}
	response.Success(ac, "移动成功", false)
}

// BatchAddEycMeetingRoomFromExcel 从Excel批量添加会议室
func BatchAddEycMeetingRoomFromExcel(c context.Context, ac *app.RequestContext) {
	file, err := ac.FormFile("file")
	if err != nil {
		response.BadRequest(ac, "文件上传失败: "+err.Error())
		return
	}

	f, err := file.Open()
	if err != nil {
		response.ServerError(ac, err)
		return
	}
	defer f.Close()

	if err := eycMeetingRoomService.BatchAddFromExcel(ac, f); err != nil {
		response.Fail(ac, response.StatusBadRequest, "导入失败: "+err.Error())
		return
	}

	response.Success(ac, "批量导入成功", false)
}

// DownloadBatchAddTemplate 下载批量添加会议室的Excel模板
func DownloadBatchAddTemplate(c context.Context, ac *app.RequestContext) {
	buf, err := eycMeetingRoomService.GenerateTemplateExcel()
	if err != nil {
		response.Fail(ac, response.StatusServerError, "生成模板失败: "+err.Error())
		return
	}

	ac.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ac.Header("Content-Disposition", "attachment; filename=meeting_room_template.xlsx")
	ac.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", buf.Bytes())
}
