---
description:
globs:
alwaysApply: false
---
# Server 项目开发规范 v1.2

## 1. 项目概述

本文档是 `nebulaserver` 后端项目的核心开发规范，旨在统一团队的开发标准，提高代码质量和可维护性。所有开发人员必须遵循此规范。

## 2. 项目结构

```
/
├── cmd/                    # 命令行入口
├── config/                 # 配置相关
├── internal/               # 内部包 (项目核心代码)
│   ├── controller/         # 控制器层 (Controller)
│   ├── dto/                # 数据传输对象 (DTO)
│   ├── middleware/         # 中间件
│   ├── model/              # 数据模型层 (Model)
│   ├── pkg/                # 内部公共包/工具库
│   ├── router/             # 路由管理
│   └── service/            # 服务层 (Service)
├── proto/                  # Protocol Buffers 定义
├── scripts/                # 脚本文件
├── go.mod                  # Go模块文件
├── go.sum                  # Go依赖版本锁定
└── README.md               # 项目高级说明文档
```

## 3. 核心分层架构详解

本项目遵循严格的 **MDC (Model-Service-Controller)** 分层架构，并引入 **DTO (Data Transfer Object)** 层，以实现清晰的职责分离。

-   **Model 层 (`internal/model`)**
    -   **唯一职责**: 定义与数据库表结构 **完全一致** 的 Go 结构体。
    -   所有字段和 `gorm` 标签都直接用于数据库的读写操作。
    -   **严禁** 在此层编写任何业务逻辑、数据转换或数据库查询代码。

-   **DTO 层 (`internal/dto`)**
    -   **唯一职责**: 定义用于 **API 数据传输** 的 Go 结构体。
    -   用于定义请求的 Body 结构和响应的数据结构，是 `Controller` 与外界交互的数据契约。
    -   其结构可以根据 API 的需求灵活设计，可以与 `Model` 完全不同。

-   **Service 层 (`internal/service`)**
    -   **唯一职责**: 存放 **所有业务逻辑**。
    -   负责处理一项或多项业务功能，例如用户注册、订单处理等。
    -   它会调用 `Model` 层与数据库交互，并可能对 `Model` 数据进行处理，最终组装成 `DTO` 或业务结果返回给 `Controller`。

-   **Controller 层 (`internal/controller`)**
    -   **职责**: 作为 HTTP 请求的入口和"胶水层"，保持"轻薄"。
    -   1.  **解析请求**: 从请求中获取参数、查询、Body 等数据。
    -   2.  **调用服务**: 调用一个或多个 `Service` 层的方法来执行业务逻辑。
    -   3.  **返回响应**: 将 `Service` 返回的数据（通常是 `DTO`）封装成统一的 JSON 格式返回给客户端。
    -   **严禁** 在此层编写任何复杂的业务逻辑。

## 4. 命名与开发规范

### 4.1 结构体命名规范 (Struct Naming)

-   **Controller**: 必须以 `Controller` 为后缀。
    -   格式: `type <业务模块>Controller struct`
    -   示例: `type MenuController struct`, `type UserController struct`

-   **Service**: 必须以 `Service` 为后缀。
    -   格式: `type <业务模块>Service struct`
    -   示例: `type MenuService struct`, `type UserService struct`

-   **Model**: 直接使用业务模块的名词，**不加**任何后缀。
    -   格式: `type <业务模块> struct`
    -   示例: `type Menu struct`, `type User struct`

-   **DTO**: 根据其用途以 `DTO` 或具体功能词为后缀。
    -   格式: `type <功能>DTO struct`, `type <功能>Request struct`, `type <功能>Response struct`
    -   示例: `type MenuItemDTO struct`, `type LoginRequest struct`

### 4.2 路由管理规范 (Routing)

-   使用统一的动作命名，并通过 HTTP 方法区分。
    -   `GET /<module>/get_ls`: 获取列表 (分页)
    -   `GET /<module>/get_all`: 获取全部数据
    -   `GET /<module>/get_info`: 获取单条数据 (通常带 `?id=`)
    -   `POST /<module>/post_add`: 添加数据
    -   `POST /<module>/post_modify`: 修改数据
    -   `POST /<module>/post_del`: 删除数据 (通常带 `?id=`)

### 4.3 接口响应规范 (API Response)

-   **统一响应格式**:
    ```json
    {
      "status": 0,      // 状态码: 0-成功，非0-失败
      "msg": "",        // 错误信息 (失败时必须有值)
      "data": {}        // 响应数据 (成功时返回)
    }
    ```
-   **分页响应格式**: `data` 字段内必须包含 `items` 和 `total`。
    ```json
    {
      "status": 0,
      "msg": "",
      "data": {
        "items": [],    // 数据内容数组
        "total": 100    // 数据总数
      }
    }
    ```

### 4.4 代码风格 (Code Style)
-   严格遵循 Go 官方代码规范，提交前必须使用 `gofmt` 或 `goimports` 格式化代码。
-   使用 `golangci-lint` 进行代码检查。
-   公开的函数、方法、重要变量必须添加清晰的中文注释。
-   单行代码长度建议不超过 `120` 字符。
-   单个函数长度建议不超过 `50` 行（不含注释和空行）。
-   包名使用小写、简洁的名词。

## 5. 数据库规范 (Database)
-   主数据库为 MySQL，ORM 框架为 GORM。
-   所有写操作（Create, Update, Delete）必须在 **Service 层** 使用事务。
-   表名：全小写，使用下划线分隔 (e.g., `user_accounts`)。
-   字段名：全小写，使用下划线分隔 (e.g., `user_name`)。
-   所有表必须包含 `id`, `created_at`, `updated_at`, `deleted_at` 字段 (可通过继承 `BaseModel` 实现)。

## 6. 日志与错误处理 (Logging & Errors)
-   日志统一使用 `internal/pkg/logger`。
-   日志格式为 JSON，方便机器解析。
-   **错误必须在发生的最低层级记录日志**。例如，Service 层数据库操作失败，应立即在 Service 层记录日志，Controller 层只需处理并返回错误响应，无需重复记录。
-   关键操作（如登录、支付、重要数据修改）必须记录日志。
