package model

// Menu 菜单项数据库模型
type Menu struct {
	BaseModel        // 继承BaseModel的所有字段和方法
	AppID     uint   `json:"appid" gorm:"column:appid;index"` // 关联的应用ID
	ParentID  uint   `json:"parent_id" gorm:"index"`          // 父菜单ID，0表示顶级菜单
	Name      string `json:"name" gorm:"size:50;index"`       // 菜单名称标识
	Path      string `json:"path" gorm:"size:255"`            // 菜单路径
	Component string `json:"component" gorm:"size:100"`       // 组件类型
	IsHome    int    `json:"is_home" gorm:"default:0"`        // 是否为首页
	IframeURL string `json:"iframe_url" gorm:"size:255"`      // iframe URL
	URLType   int    `json:"url_type" gorm:"default:1"`       // URL类型
	KeepAlive int    `json:"keep_alive" gorm:"default:0"`     // 是否保持活跃
	IsFull    int    `json:"is_full" gorm:"default:0"`        // 是否全屏
	IsLink    bool   `json:"is_link" gorm:"default:false"`    // 是否为链接
	PageSign  string `json:"page_sign" gorm:"size:50"`        // 页面标识
	Title     string `json:"title" gorm:"size:100;not null"`  // 菜单标题
	Icon      string `json:"icon" gorm:"size:100"`            // 菜单图标
	Hide      bool   `json:"hide" gorm:"default:false"`       // 是否隐藏
	Order     int    `json:"order" gorm:"default:0"`          // 排序
	Children  []Menu `json:"children" gorm:"-"`               // 子菜单，非数据库字段
}
