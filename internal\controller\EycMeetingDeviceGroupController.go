package controller

import (
	"context"
	"eyc3_meeting/internal/model"
	"eyc3_meeting/internal/pkg/response"
	"eyc3_meeting/internal/service"
	"strconv"

	"github.com/cloudwego/hertz/pkg/app"
)

// EycMeetingDeviceGroupController 设备分组控制器
// 采用 BaseController 泛型，统一风格
type EycMeetingDeviceGroupController struct {
	BaseController[model.EycMeetingDeviceGroup, *service.EycMeetingDeviceGroupService]
}

// 全局设备分组服务与控制器实例
var (
	eycMeetingDeviceGroupService    = new(service.EycMeetingDeviceGroupService)
	eycMeetingDeviceGroupController = EycMeetingDeviceGroupController{
		BaseController: BaseController[model.EycMeetingDeviceGroup, *service.EycMeetingDeviceGroupService]{Service: eycMeetingDeviceGroupService},
	}
)

// GetDeviceGroupListWithCount 获取带设备计数的设备分组列表
func GetDeviceGroupListWithCount(ctx context.Context, c *app.RequestContext) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.Query("page"))
	if page <= 0 {
		page = 1
	}
	pageSize, _ := strconv.Atoi(c.Query("page_size"))
	if pageSize <= 0 {
		pageSize = 10
	}

	// 解析过滤参数
	fields := make(map[string]string)
	if title := c.Query("title"); title != "" {
		fields["title"] = "%" + title + "%" // 支持模糊查询
	}
	// 从JWT中间件获取corp_id，确保数据隔离
	if corpID, exists := c.Get("corp_id"); exists {
		if idStr, ok := corpID.(string); ok {
			fields["corp_id"] = idStr
		}
	}

	// 调用服务层获取数据
	result, err := eycMeetingDeviceGroupService.GetListWithCount(page, pageSize, fields, "id desc")
	if err != nil {
		response.ServerError(c, err)
		return
	}

	// 返回成功响应
	response.SuccessPage(c, result.Items, result.Total)
}

// --- 标准CRUD接口实现 ---
var (
	// GetEycDeviceGroupList 获取设备分组列表（分页、可筛选）
	// 支持的筛选字段: title (模糊), device_type (精确)
	GetEycDeviceGroupList = eycMeetingDeviceGroupController.GetList(true, "title:%,device_type")

	// GetAllEycDeviceGroups 获取所有设备分组
	// 支持的筛选字段: title (模糊), device_type (精确)
	GetAllEycDeviceGroups = eycMeetingDeviceGroupController.GetAll(true, "title:%,device_type")

	// GetEycDeviceGroupInfo 获取单个设备分组详情
	GetEycDeviceGroupInfo = func(ctx context.Context, c *app.RequestContext) {
		var req struct {
			ID       uint   `json:"id" query:"id"`
			Keyword  string `json:"keyword" query:"keyword"`
			SortType string `json:"sort_type" query:"sort_type"`
		}

		if err := c.BindAndValidate(&req); err != nil {
			response.Fail(c, response.StatusBadRequest, "参数错误: "+err.Error())
			return
		}
		if req.ID == 0 {
			response.Fail(c, response.StatusBadRequest, "ID 不能为空")
			return
		}

		result, err := eycMeetingDeviceGroupService.GetGroupDetailsService(c, req.ID, req.Keyword, req.SortType)
		if err != nil {
			response.Fail(c, response.StatusBadRequest, "获取失败: "+err.Error())
			return
		}

		response.Success(c, result)
	}

	// AddEycDeviceGroup 新增设备分组
	// corp_id 会从JWT中自动注入，无需前端传递
	AddEycDeviceGroup = eycMeetingDeviceGroupController.Add(true, "title,device_type,device_ids")

	// ModifyEycDeviceGroup 修改设备分组
	// corp_id 不允许修改
	ModifyEycDeviceGroup = func(ctx context.Context, c *app.RequestContext) {
		var req struct {
			ID         uint   `json:"id"`
			Title      string `json:"title"`
			DeviceType string `json:"device_type"`
			DeviceIds  string `json:"device_ids"`
			Force      bool   `json:"force"` // 强制移动
		}

		if err := c.BindAndValidate(&req); err != nil {
			response.Fail(c, response.StatusBadRequest, "参数错误: "+err.Error())
			return
		}
		if req.ID == 0 {
			response.Fail(c, response.StatusBadRequest, "ID 不能为空")
			return
		}

		// 将请求转换为 map[string]interface{} 以复用通用逻辑
		updateData := map[string]interface{}{
			"title":       req.Title,
			"device_type": req.DeviceType,
			"device_ids":  req.DeviceIds,
		}

		// 调用服务层
		if err := eycMeetingDeviceGroupService.ModifyGroupWithForce(c, req.ID, updateData, req.Force); err != nil {
			response.Fail(c, response.StatusBadRequest, err.Error())
			return
		}

		response.SuccessWithMsg(c, "修改成功")
	}

	// DeleteEycDeviceGroup 删除设备分组
	DeleteEycDeviceGroup = eycMeetingDeviceGroupController.Delete()
)
