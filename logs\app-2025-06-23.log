{"level":"INFO","time":"2025-06-23T08:56:13.941+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:14.183+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":229,"影响行数":1}
{"level":"INFO","time":"2025-06-23T08:56:14.260+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:14.341+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:14.422+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":81,"影响行数":1}
{"level":"INFO","time":"2025-06-23T08:56:14.547+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":123,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:14.714+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":165,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:14.756+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":41,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:14.915+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-06-23T08:56:15.031+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":116,"影响行数":3}
{"level":"INFO","time":"2025-06-23T08:56:15.109+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:15.286+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":177,"影响行数":1}
{"level":"INFO","time":"2025-06-23T08:56:15.425+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:15.502+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:15.636+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-06-23T08:56:15.782+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":145,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:15.868+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:15.994+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":126,"影响行数":1}
{"level":"INFO","time":"2025-06-23T08:56:16.109+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":114,"影响行数":3}
{"level":"INFO","time":"2025-06-23T08:56:16.166+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:16.291+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-06-23T08:56:16.392+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:16.434+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":42,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:16.562+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-06-23T08:56:16.700+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:16.808+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":107,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:16.959+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":150,"影响行数":1}
{"level":"INFO","time":"2025-06-23T08:56:17.101+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:17.148+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:17.281+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-06-23T08:56:17.384+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":102,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T08:56:17.384+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-23T08:56:17.483+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-23T08:56:17.484+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-23T08:56:17.484+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-23T08:56:17.486+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-23T11:02:21.052+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-23T11:02:21.053+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-23T11:02:21.053+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-23T11:02:23.077+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":135,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:23.187+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":98,"影响行数":1}
{"level":"INFO","time":"2025-06-23T11:02:23.363+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":175,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:23.398+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":33,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:23.507+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":109,"影响行数":1}
{"level":"INFO","time":"2025-06-23T11:02:23.708+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":200,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:23.872+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:23.932+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:24.095+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-06-23T11:02:24.295+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":200,"影响行数":3}
{"level":"INFO","time":"2025-06-23T11:02:24.352+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:24.480+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-06-23T11:02:24.642+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":161,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:24.714+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:24.925+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":210,"影响行数":1}
{"level":"INFO","time":"2025-06-23T11:02:25.092+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":166,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:25.163+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:25.313+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":150,"影响行数":1}
{"level":"INFO","time":"2025-06-23T11:02:25.520+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":206,"影响行数":3}
{"level":"INFO","time":"2025-06-23T11:02:25.570+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":50,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:25.787+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":216,"影响行数":1}
{"level":"INFO","time":"2025-06-23T11:02:25.982+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":195,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:26.058+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:26.217+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-06-23T11:02:26.375+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":157,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:26.454+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:26.612+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-23T11:02:26.739+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:26.781+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":42,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:26.905+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":123,"影响行数":1}
{"level":"INFO","time":"2025-06-23T11:02:26.990+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-06-23T11:02:26.990+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-23T11:02:27.124+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-23T11:02:27.129+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-23T11:02:27.130+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-23T11:02:27.131+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-23T18:30:04.992+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-23T18:30:04.994+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-23T18:30:04.994+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
