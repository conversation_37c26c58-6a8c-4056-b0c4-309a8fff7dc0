package model

import (
	"database/sql/driver"
	"fmt"
	"time"
)

// LocalTime 自定义时间类型，用于格式化JSON输出并适配数据库
type LocalTime time.Time

// MarshalJSON 实现json.Marshaler接口，用于JSON序列化
func (t LocalTime) MarshalJSON() ([]byte, error) {
	// 格式化为 "YYYY-MM-DD HH:MM:SS"
	formatted := fmt.Sprintf("\"%s\"", time.Time(t).Format("2006-01-02 15:04:05"))
	return []byte(formatted), nil
}

// Value 实现 driver.Valuer 接口, 在保存时告诉 GORM 如何将 LocalTime 类型写入数据库
func (t LocalTime) Value() (driver.Value, error) {
	// 如果时间是零值，则存入 NULL
	if time.Time(t).IsZero() {
		return nil, nil
	}
	return time.Time(t), nil
}

// Scan 实现 sql.Scanner 接口, 在读取时告诉 GORM 如何将数据库中的时间类型扫描到 LocalTime 中
func (t *LocalTime) Scan(value interface{}) error {
	if value == nil {
		*t = LocalTime(time.Time{})
		return nil
	}
	// 从数据库读取的时间是 time.Time 类型
	if vt, ok := value.(time.Time); ok {
		*t = LocalTime(vt)
		return nil
	}
	return fmt.Errorf("can not convert %v to LocalTime", value)
}

const TableNameEycDevice = "eyc_meeting_device"

// 设备类型常量
const (
	DeviceTypeDoor   = "0" // 门牌
	DeviceTypeScreen = "1" // 大屏
)

// SystemMode 系统模式
const (
	SystemModeStandard    = "0" // 标准模式
	SystemModeSplitScreen = "1" // 分屏模式
	SystemModeDoorControl = "2" // 纯门禁
)

// EycDevice 设备表
type EycMeetingDevice struct {
	ID              int       `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Corpid          string    `gorm:"column:corp_id;not null;comment:corp_id" json:"corpid"`                      // 架构corpid
	Title           string    `gorm:"column:title;not null;comment:设备名称" json:"title"`                            // 设备名称
	SN              string    `gorm:"column:sn;comment:设备序列号" json:"sn"`                                          // 设备序列号
	Model           string    `gorm:"column:model;comment:设备型号" json:"model"`                                     // 设备型号
	DeviceType      string    `gorm:"column:device_type;not null;default:'door';comment:设备类型" json:"device_type"` // 设备类型：door-门牌, screen-大屏
	LightBrightness int       `gorm:"column:light_brightness;default:100;comment:灯带亮度" json:"light_brightness"`   // 灯带亮度
	Volume          int       `gorm:"column:volume;default:100;comment:设备音量" json:"volume"`                       // 设备音量
	CustomTemplate  int       `gorm:"column:custom_template;default:0;comment:是否自定义模板" json:"custom_template"`    // 是否自定义模板:0-否,1-是
	TemplateFree    string    `gorm:"column:template_free;comment:暂无会议模板;" json:"template_free"`                  // 暂无会议模板
	TemplateIdle    string    `gorm:"column:template_idle;comment:会议空闲模板;" json:"template_idle"`                  // 会议空闲模板
	TemplateUsing   string    `gorm:"column:template_using;comment:会议使用中模板;" json:"template_using"`               // 会议使用中模板
	RelatedDoor     int       `gorm:"column:related_door;default:0;comment:是否关联门牌" json:"related_door"`           // 是否关联门牌:0-否,1-是
	SystemMode      string    `gorm:"column:system_mode;default:'standard';comment:会议系统模式" json:"system_mode"`    // 会议系统模式:standard-标准模式,split_screen-分屏模式,door_control-纯门禁
	Network         string    `gorm:"column:network;type:varchar(255);comment:网络连接" json:"network"`
	IP              string    `gorm:"column:ip;type:varchar(255);comment:ip地址" json:"ip"`
	Mac             string    `gorm:"column:mac;type:varchar(255);comment:mac地址" json:"mac"`
	AutoPower       int       `gorm:"column:auto_power;default:0;comment:是否启用自动开关机" json:"auto_power"`                                                // 是否开启自动开关机:0-否,1-是
	PowerOnTime     LocalTime `gorm:"column:power_on_time;not null;default:CURRENT_TIMESTAMP;comment:开机时间;default:'07:00:00'" json:"power_on_time"`   // 开机时间
	PowerOffTime    LocalTime `gorm:"column:power_off_time;not null;default:CURRENT_TIMESTAMP;comment:关机时间;default:'23:59:59'" json:"power_off_time"` // 关机时间
	Status          int       `gorm:"column:status;default:1;comment:设备状态" json:"status"`                                                             // 设备状态:1-在线,0-离线
	CreatedAt       LocalTime `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`
	UpdatedAt       LocalTime `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
	TemplateImage   string    `gorm:"column:template_image;comment:会议室模板" json:"template_image"`
}

// TableName EycDevice's table name
func (*EycMeetingDevice) TableName() string {
	return TableNameEycDevice
}

// IsDeviceTitleExists 检查在指定企业下，设备名称是否已存在
// excludeID > 0 时，表示在检查时需要排除的设备ID（通常用于更新操作）
func (d *EycMeetingDevice) IsDeviceTitleExists(corpid string, title string, excludeID int) (bool, error) {
	var count int64
	query := DB.Model(&EycMeetingDevice{}).
		Where("corp_id = ? AND title = ?", corpid, title)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	err := query.Count(&count).Error
	if err != nil {
		// 在模型层不直接返回错误日志，由上层业务逻辑处理
		return false, err
	}
	return count > 0, nil
}
