package controller

import (
	"context"
	"strconv"

	"eyc3_meeting/internal/model"
	"eyc3_meeting/internal/pkg/logger"
	"eyc3_meeting/internal/pkg/response"

	"github.com/cloudwego/hertz/pkg/app"
)

// CurrentUser 获取当前用户信息接口
func CurrentUser(c context.Context, ctx *app.RequestContext) {
	// 从上下文中获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "未登录")
		return
	}

	// 获取用户信息
	user, err := model.GetUserByID(strconv.Itoa(int(userID.(uint))))
	if err != nil {
		logger.Error("获取用户信息失败", logger.Error2(err))
		response.ServerError(ctx, err)
		return
	}

	if user == nil {
		response.NotFound(ctx, "用户不存在")
		return
	}

	// 构造菜单按钮
	buttons := []map[string]interface{}{
		{
			"type":          "button",
			"iconClassName": "pr-2",
			"icon":          "fa fa-user-gear",
			"label":         "个人设置",
			"onClick":       "window.location.hash = \"#/user_setting\"",
		},
		{
			"type":          "button",
			"iconClassName": "pr-2",
			"label":         "退出登录",
			"icon":          "fa-solid fa-right-from-bracket",
			"onClick":       "window.$owl.logout()",
		},
	}

	// 如果用户没有设置头像，使用默认头像
	avatar := "https://demo.owladmin.com/admin-assets/default-avatar.png"
	if user.Avatar != "" {
		avatar = user.Avatar
	}

	// 构造响应数据
	responseData := map[string]interface{}{
		"name":   user.Nickname, // 使用Nickname代替Name
		"avatar": avatar,
		"menus": map[string]interface{}{
			"type":          "dropdown-button",
			"hideCaret":     true,
			"trigger":       "hover",
			"label":         user.Nickname, // 使用Nickname代替Name
			"className":     "h-full w-full",
			"btnClassName":  "navbar-user w-full",
			"menuClassName": "min-w-0",
			"icon":          avatar,
			"buttons":       buttons,
		},
	}

	// 返回响应
	response.Success(ctx, responseData)
}
