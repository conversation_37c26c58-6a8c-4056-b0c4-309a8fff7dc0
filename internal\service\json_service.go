package service

//
//import (
//	"encoding/json"
//	"errors"
//	"eyc3_meeting/internal/model"
//)
//
//// JSONService 提供处理JSON字段的服务方法
//type JSONService struct{}
//
//// ProcessJSONFields 处理会议室的JSON字段
//func (s *JSONService) ProcessJSONFields(room *model.EycMeetingRoom, requestData map[string]interface{}) error {
//	// 处理Image字段
//	if imageData, exists := requestData["image"]; exists {
//		jsonData, err := s.convertToJSON(imageData)
//		if err == nil {
//			room.Image = jsonData
//		} else {
//			return errors.New("处理图片JSON字段失败: " + err.Error())
//		}
//	}
//
//	// 处理VisibleScope字段
//	if visibleScopeData, exists := requestData["visible_scope"]; exists {
//		jsonData, err := s.convertToJSON(visibleScopeData)
//		if err == nil {
//			room.VisibleScope = jsonData
//		} else {
//			return errors.New("处理可见范围JSON字段失败: " + err.Error())
//		}
//	} else {
//		// 如果不存在，设置默认值为空JSON数组
//		room.VisibleScope = json.RawMessage([]byte("[]"))
//	}
//
//	return nil
//}
//
//// ProcessAdvancedSettings 处理会议室高级设置的JSON字段
//func (s *JSONService) ProcessAdvancedSettings(advancedSettings *model.EycMeetingRoomAdvancedSettings, requestData map[string]interface{}) error {
//	// 处理VisibleScope字段
//	if visibleScopeData, exists := requestData["visible_scope"]; exists {
//		jsonData, err := s.convertToJSON(visibleScopeData)
//		if err == nil {
//			advancedSettings.VisibleScope = jsonData
//		} else {
//			return errors.New("处理可见范围JSON字段失败: " + err.Error())
//		}
//	} else {
//		// 如果不存在，设置默认值为空JSON数组
//		advancedSettings.VisibleScope = json.RawMessage([]byte("[]"))
//	}
//
//	return nil
//}
//
//// convertToJSON 将各种类型的数据转换为JSON格式的json.RawMessage
//func (s *JSONService) convertToJSON(data interface{}) (json.RawMessage, error) {
//	switch v := data.(type) {
//	case string:
//		// 如果是字符串，可能是JSON格式的字符串，也可能是普通字符串
//		// 尝试解析JSON
//		var jsonObj interface{}
//		err := json.Unmarshal([]byte(v), &jsonObj)
//		if err != nil {
//			// 如果不是有效的JSON，则将其视为普通字符串
//			jsonBytes, err := json.Marshal(v)
//			if err != nil {
//				return nil, err
//			}
//			return json.RawMessage(jsonBytes), nil
//		}
//		// 已经是有效的JSON字符串，直接返回
//		return json.RawMessage(v), nil
//	case []interface{}, map[string]interface{}:
//		// 如果是数组或对象，直接转换为JSON
//		jsonBytes, err := json.Marshal(v)
//		if err != nil {
//			return nil, err
//		}
//		return json.RawMessage(jsonBytes), nil
//	default:
//		// 其他类型，尝试直接转换为JSON
//		jsonBytes, err := json.Marshal(v)
//		if err != nil {
//			return nil, err
//		}
//		return json.RawMessage(jsonBytes), nil
//	}
//}
