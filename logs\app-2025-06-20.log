{"level":"INFO","time":"2025-06-20T08:55:55.205+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:55:55.395+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-06-20T08:55:55.613+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":217,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:55:55.715+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":101,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:55:55.931+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":215,"影响行数":1}
{"level":"INFO","time":"2025-06-20T08:55:56.119+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":187,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:55:56.325+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":205,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:55:56.421+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:55:56.610+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":188,"影响行数":1}
{"level":"INFO","time":"2025-06-20T08:55:56.814+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":204,"影响行数":3}
{"level":"INFO","time":"2025-06-20T08:55:56.970+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":155,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:55:57.231+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":258,"影响行数":1}
{"level":"INFO","time":"2025-06-20T08:55:57.470+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":238,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:55:57.581+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:55:57.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":255,"影响行数":1}
{"level":"INFO","time":"2025-06-20T08:55:58.045+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":208,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:55:58.140+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:55:58.415+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":275,"影响行数":1}
{"level":"INFO","time":"2025-06-20T08:55:58.630+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":213,"影响行数":3}
{"level":"INFO","time":"2025-06-20T08:55:58.693+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:55:58.890+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":196,"影响行数":1}
{"level":"INFO","time":"2025-06-20T08:55:59.031+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:55:59.119+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:55:59.333+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":213,"影响行数":1}
{"level":"INFO","time":"2025-06-20T08:55:59.530+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":196,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:55:59.636+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:55:59.938+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":302,"影响行数":1}
{"level":"INFO","time":"2025-06-20T08:56:00.129+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":190,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:56:00.243+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":113,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:56:00.481+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":237,"影响行数":1}
{"level":"INFO","time":"2025-06-20T08:56:00.690+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":208,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T08:56:00.690+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-20T08:56:00.935+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-20T08:56:00.936+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-20T08:56:00.936+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-20T08:56:00.937+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-20T09:02:05.285+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":233,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:02:05.861+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":803,"影响行数":11}
{"level":"INFO","time":"2025-06-20T09:02:06.109+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":247,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:02:12.840+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":234,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:02:12.840+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":230,"影响行数":11}
{"level":"INFO","time":"2025-06-20T09:02:13.067+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":226,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:02:26.198+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":140,"影响行数":11}
{"level":"INFO","time":"2025-06-20T09:03:42.749+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":203,"影响行数":11}
{"level":"INFO","time":"2025-06-20T09:22:35.168+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":174,"影响行数":11}
{"level":"INFO","time":"2025-06-20T09:22:35.187+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":192,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:22:35.360+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":190,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:22:41.626+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":193,"影响行数":11}
{"level":"INFO","time":"2025-06-20T09:25:16.282+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":228,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:25:16.298+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":240,"影响行数":11}
{"level":"INFO","time":"2025-06-20T09:25:16.542+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":243,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:25:49.997+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":107,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:25:50.027+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":135,"影响行数":11}
{"level":"INFO","time":"2025-06-20T09:25:50.143+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":116,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:38:58.837+0800","caller":"service/EycMeetingBookingService.go:310","msg":"处理参会人信息开始","type":"string","raw_value":"{\"departments\":[{\"dept_id\":\"278542050\",\"name\":\"部门2\"},{\"dept_id\":\"299939017\",\"name\":\"软件事业部\"}],\"users\":[{\"userid\":\"135933524724483168\",\"name\":\"张菲菲\",\"avatar\":\"https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg\"},{\"userid\":\"2865524504-1710204866\",\"name\":\"王佳文\",\"avatar\":\"https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png\"}]}"}
{"level":"INFO","time":"2025-06-20T09:38:58.838+0800","caller":"service/EycMeetingBookingService.go:324","msg":"参会人信息为字符串类型","value":"{\"departments\":[{\"dept_id\":\"278542050\",\"name\":\"部门2\"},{\"dept_id\":\"299939017\",\"name\":\"软件事业部\"}],\"users\":[{\"userid\":\"135933524724483168\",\"name\":\"张菲菲\",\"avatar\":\"https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg\"},{\"userid\":\"2865524504-1710204866\",\"name\":\"王佳文\",\"avatar\":\"https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png\"}]}"}
{"level":"INFO","time":"2025-06-20T09:38:58.838+0800","caller":"service/EycMeetingBookingService.go:414","msg":"参会人信息处理完成","final_json":"{\"departments\":[{\"dept_id\":\"278542050\",\"name\":\"部门2\"},{\"dept_id\":\"299939017\",\"name\":\"软件事业部\"}],\"users\":[{\"avatar\":\"https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg\",\"name\":\"张菲菲\",\"userid\":\"135933524724483168\"},{\"avatar\":\"https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png\",\"name\":\"王佳文\",\"userid\":\"2865524504-1710204866\"}]}"}
{"level":"ERROR","time":"2025-06-20T09:38:58.838+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"重复结束日期格式错误: parsing time \"<nil>\" as \"2006-01-02T15:04:05Z07:00\": cannot parse \"<nil>\" as \"2006\""}
{"level":"INFO","time":"2025-06-20T09:39:30.259+0800","caller":"service/EycMeetingBookingService.go:310","msg":"处理参会人信息开始","type":"string","raw_value":"{\"departments\":[{\"dept_id\":\"278542050\",\"name\":\"部门2\"},{\"dept_id\":\"299939017\",\"name\":\"软件事业部\"}],\"users\":[{\"userid\":\"135933524724483168\",\"name\":\"张菲菲\",\"avatar\":\"https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg\"},{\"userid\":\"2865524504-1710204866\",\"name\":\"王佳文\",\"avatar\":\"https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png\"}]}"}
{"level":"INFO","time":"2025-06-20T09:39:30.259+0800","caller":"service/EycMeetingBookingService.go:324","msg":"参会人信息为字符串类型","value":"{\"departments\":[{\"dept_id\":\"278542050\",\"name\":\"部门2\"},{\"dept_id\":\"299939017\",\"name\":\"软件事业部\"}],\"users\":[{\"userid\":\"135933524724483168\",\"name\":\"张菲菲\",\"avatar\":\"https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg\"},{\"userid\":\"2865524504-1710204866\",\"name\":\"王佳文\",\"avatar\":\"https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png\"}]}"}
{"level":"INFO","time":"2025-06-20T09:39:30.260+0800","caller":"service/EycMeetingBookingService.go:414","msg":"参会人信息处理完成","final_json":"{\"departments\":[{\"dept_id\":\"278542050\",\"name\":\"部门2\"},{\"dept_id\":\"299939017\",\"name\":\"软件事业部\"}],\"users\":[{\"avatar\":\"https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg\",\"name\":\"张菲菲\",\"userid\":\"135933524724483168\"},{\"avatar\":\"https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png\",\"name\":\"王佳文\",\"userid\":\"2865524504-1710204866\"}]}"}
{"level":"INFO","time":"2025-06-20T09:39:30.453+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_bookings` (`corp_id`,`title`,`room_ids`,`room_names`,`location`,`start_time`,`end_time`,`is_full_day`,`participants`,`allow_invite_others`,`notify_types`,`enable_notify`,`reminders`,`repeat_type`,`repeat_end_date`,`description`,`attachments`,`status`,`created_by`,`created_by_name`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','第三季度技术分享会','301','未来之光会议室','总部A座三楼','2024-08-01 10:00:00','2024-08-01 12:00:00',0,'{\"departments\":[{\"dept_id\":\"278542050\",\"name\":\"部门2\"},{\"dept_id\":\"299939017\",\"name\":\"软件事业部\"}],\"users\":[{\"avatar\":\"https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg\",\"name\":\"张菲菲\",\"userid\":\"135933524724483168\"},{\"avatar\":\"https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png\",\"name\":\"王佳文\",\"userid\":\"2865524504-1710204866\"}]}',1,'app,sms',1,'\"[{\\\"minutes_before\\\": 30, \\\"label\\\": \\\"会议前30分钟\\\"},{\\\"minutes_before\\\": 1440, \\\"label\\\": \\\"会议前1天\\\"}]\"',0,NULL,'本次分享会主要内容：\n1. AI在项目中的应用\n2. Go语言性能优化实践\n3. Q&A环节','\"[{\\\"name\\\": \\\"AI应用分享.pptx\\\", \\\"url\\\": \\\"https://example.com/files/ai_share.pptx\\\", \\\"size\\\": 5120, \\\"type\\\": \\\"pptx\\\"}]\"',1,'','','2025-06-20 09:39:30.326','2025-06-20 09:39:30.326')","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:39:30.589+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_bookings` SET `reminders`='\"[{\\\"minutes_before\\\": 30, \\\"label\\\": \\\"会议前30分钟\\\"},{\\\"minutes_before\\\": 1440, \\\"label\\\": \\\"会议前1天\\\"}]\"',`updated_at`='2025-06-20 09:39:30.454' WHERE `id` = 27","耗时":135,"影响行数":0}
{"level":"INFO","time":"2025-06-20T09:39:30.708+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_bookings` SET `attachments`='\"[{\\\"name\\\": \\\"AI应用分享.pptx\\\", \\\"url\\\": \\\"https://example.com/files/ai_share.pptx\\\", \\\"size\\\": 5120, \\\"type\\\": \\\"pptx\\\"}]\"',`updated_at`='2025-06-20 09:39:30.589' WHERE `id` = 27","耗时":119,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:40:25.317+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":150,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:40:25.318+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":146,"影响行数":11}
{"level":"INFO","time":"2025-06-20T09:40:25.417+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":97,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:40:30.362+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":133,"影响行数":11}
{"level":"ERROR","time":"2025-06-20T09:40:43.634+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_name = '测试'","耗时":70,"error":"Error 1054 (42S22): Unknown column 'group_name' in 'where clause'"}
{"level":"ERROR","time":"2025-06-20T09:40:43.732+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"检查分组名称唯一性失败: Error 1054 (42S22): Unknown column 'group_name' in 'where clause'"}
{"level":"INFO","time":"2025-06-20T09:41:17.793+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":147,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:41:17.793+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":147,"影响行数":11}
{"level":"INFO","time":"2025-06-20T09:41:17.925+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":132,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:42:25.133+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":215,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:42:25.176+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":257,"影响行数":11}
{"level":"INFO","time":"2025-06-20T09:42:25.384+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":207,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:42:33.801+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":170,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:42:33.842+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":209,"影响行数":11}
{"level":"INFO","time":"2025-06-20T09:42:34.061+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":218,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:46:47.525+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":178,"影响行数":11}
{"level":"INFO","time":"2025-06-20T09:46:47.753+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":227,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:48:19.145+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-20T09:48:19.146+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-20T09:48:19.146+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-20T09:48:24.401+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:24.640+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":226,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:48:24.878+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":236,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:24.925+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":47,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:25.097+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":171,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:48:25.307+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":208,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:25.531+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":223,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:25.623+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:25.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":212,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:48:25.982+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":146,"影响行数":3}
{"level":"INFO","time":"2025-06-20T09:48:26.086+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":102,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:26.280+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":194,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:48:26.516+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":235,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:26.636+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:26.913+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":275,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:48:27.140+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":226,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:27.227+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":86,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:27.526+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":298,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:48:27.728+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":201,"影响行数":3}
{"level":"INFO","time":"2025-06-20T09:48:27.847+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:28.161+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":313,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:48:28.387+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":226,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:28.518+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":131,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:28.748+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":229,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:48:29.055+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":306,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:29.140+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:29.295+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:48:29.532+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":236,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:29.582+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:29.717+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:48:29.948+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":229,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:48:29.948+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-20T09:48:30.166+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-20T09:48:30.168+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-20T09:48:30.169+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-20T09:48:30.171+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-20T09:50:40.138+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":113,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:50:40.606+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":580,"影响行数":11}
{"level":"INFO","time":"2025-06-20T09:50:40.748+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":142,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:50:42.899+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 31 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:52:08.360+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":190,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:52:08.360+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":188,"影响行数":11}
{"level":"INFO","time":"2025-06-20T09:52:08.557+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":196,"影响行数":2}
{"level":"INFO","time":"2025-06-20T09:52:26.031+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 31 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:54:06.480+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-20T09:54:06.481+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-20T09:54:06.481+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-20T09:54:11.334+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:11.560+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":200,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:54:11.741+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":181,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:11.801+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:11.921+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:54:12.041+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:12.237+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":195,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:12.358+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:12.562+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":203,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:54:12.798+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":236,"影响行数":3}
{"level":"INFO","time":"2025-06-20T09:54:12.883+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:13.074+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":190,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:54:13.244+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":169,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:13.304+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:13.494+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":189,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:54:13.647+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":153,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:13.687+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":38,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:13.776+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":89,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:54:13.884+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":107,"影响行数":3}
{"level":"INFO","time":"2025-06-20T09:54:13.968+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:14.079+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:54:14.183+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":103,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:14.244+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:14.423+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":178,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:54:14.559+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":135,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:14.618+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:14.752+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:54:14.878+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":125,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:14.948+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:15.078+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:54:15.188+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:54:15.190+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-20T09:54:15.274+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-20T09:54:15.276+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-20T09:54:15.276+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-20T09:54:15.278+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-20T09:54:20.335+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"分组名称不能为空"}
{"level":"ERROR","time":"2025-06-20T09:54:47.944+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND id != 0 AND EXISTS (SELECT 1 FROM json_each(device_ids) WHERE json_each.value IN (1)) LIMIT 1","耗时":66,"error":"Error 1064 (42000): You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '(device_ids) WHERE json_each.value IN (?)) LIMIT ?' at line 1"}
{"level":"INFO","time":"2025-06-20T09:54:48.043+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = 'D3'","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:54:48.172+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device_group` (`corp_id`,`title`,`device_type`,`device_ids`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','D3','0','1','2025-06-20 09:54:48.043','2025-06-20 09:54:48.043')","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:54:48.298+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE id IN (1) AND group_id != 0","耗时":126,"影响行数":1}
{"level":"ERROR","time":"2025-06-20T09:54:48.418+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"选择的设备中有已被其他分组关联的设备"}
{"level":"INFO","time":"2025-06-20T09:58:19.533+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-20T09:58:19.533+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-20T09:58:19.533+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-20T09:58:23.851+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:24.048+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:58:24.214+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":166,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:24.313+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:24.498+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":184,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:58:24.648+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:24.838+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":188,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:24.931+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:25.111+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:58:25.294+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":182,"影响行数":3}
{"level":"INFO","time":"2025-06-20T09:58:25.391+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:25.569+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":177,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:58:25.788+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":218,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:25.911+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:26.148+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":237,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:58:26.314+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":165,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:26.431+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:26.659+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":227,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:58:26.877+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":218,"影响行数":3}
{"level":"INFO","time":"2025-06-20T09:58:26.941+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:27.113+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":171,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:58:27.241+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:27.292+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:27.528+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":236,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:58:27.759+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":229,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:27.976+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":216,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:28.113+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:58:28.278+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":165,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:28.359+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:28.491+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:58:28.666+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":174,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T09:58:28.666+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-20T09:58:28.948+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-20T09:58:28.950+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-20T09:58:28.951+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-20T09:58:28.953+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-20T09:58:35.292+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND FIND_IN_SET(1, device_ids) ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":153,"影响行数":1}
{"level":"INFO","time":"2025-06-20T09:58:35.458+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":165,"影响行数":1}
{"level":"ERROR","time":"2025-06-20T09:58:35.569+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备 '修改后的设备名' 已存在于分组 'E3组更新' 中，不能重复添加"}
{"level":"INFO","time":"2025-06-20T10:00:42.478+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":126,"影响行数":11}
{"level":"INFO","time":"2025-06-20T10:00:42.634+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":155,"影响行数":2}
{"level":"INFO","time":"2025-06-20T10:00:42.844+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":489,"影响行数":2}
{"level":"INFO","time":"2025-06-20T10:01:57.668+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-20T10:01:57.669+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-20T10:01:57.669+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-20T10:10:14.385+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:14.611+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":197,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:10:14.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":224,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:14.940+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":103,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:15.106+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":165,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:10:15.302+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":195,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:15.482+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":179,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:15.601+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:15.796+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":194,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:10:15.966+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":169,"影响行数":3}
{"level":"INFO","time":"2025-06-20T10:10:16.050+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:16.249+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:10:16.372+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:16.446+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:16.644+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":198,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:10:16.790+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":145,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:16.891+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:17.110+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":218,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:10:17.264+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":153,"影响行数":3}
{"level":"INFO","time":"2025-06-20T10:10:17.324+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:17.502+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":177,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:10:17.681+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":179,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:17.786+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":103,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:17.990+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":203,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:10:18.145+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":154,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:18.230+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:18.407+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":176,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:10:18.548+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:18.640+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:18.810+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:10:18.951+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:10:18.951+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-20T10:10:19.127+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-20T10:10:19.127+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-20T10:10:19.127+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-20T10:10:19.129+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-20T10:10:24.531+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND FIND_IN_SET(1, device_ids) ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":205,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:10:24.791+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":259,"影响行数":1}
{"level":"ERROR","time":"2025-06-20T10:10:24.876+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备 '修改后的设备名' 已存在于分组 '' 中，不能重复添加"}
{"level":"INFO","time":"2025-06-20T10:11:10.330+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND FIND_IN_SET(1, device_ids) ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":170,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:11:10.480+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":149,"影响行数":1}
{"level":"ERROR","time":"2025-06-20T10:11:10.528+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备 '修改后的设备名' 已存在于分组 '' 中，不能重复添加"}
{"level":"INFO","time":"2025-06-20T10:20:40.018+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-20T10:20:40.019+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-20T10:20:40.019+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-20T10:20:43.575+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:43.746+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:20:43.895+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:43.957+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":60,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:44.082+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:20:44.213+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:44.378+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":164,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:44.461+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":82,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:44.592+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:20:44.757+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":164,"影响行数":3}
{"level":"INFO","time":"2025-06-20T10:20:44.842+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:44.942+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:20:45.076+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:45.140+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:45.336+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":195,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:20:45.466+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:45.520+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:45.679+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:20:45.792+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":112,"影响行数":3}
{"level":"INFO","time":"2025-06-20T10:20:45.913+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:46.021+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":107,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:20:46.155+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:46.216+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":60,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:46.412+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":195,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:20:46.547+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:46.645+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:46.776+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:20:46.940+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:47.005+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:47.141+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:20:47.327+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":185,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:20:47.327+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-20T10:20:47.472+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-20T10:20:47.475+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-20T10:20:47.476+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-20T10:20:47.477+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-20T10:20:57.293+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"分组名称不能为空"}
{"level":"ERROR","time":"2025-06-20T10:21:47.774+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"分组名称不能为空"}
{"level":"INFO","time":"2025-06-20T10:21:52.911+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-20T10:21:52.911+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-20T10:21:52.911+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-20T10:21:54.620+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":52,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:54.728+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":94,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:21:54.860+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":132,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:54.907+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:55.053+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:21:55.189+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":135,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:55.287+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:55.329+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":40,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:55.464+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:21:55.573+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":109,"影响行数":3}
{"level":"INFO","time":"2025-06-20T10:21:55.646+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":72,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:55.812+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":165,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:21:55.984+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":171,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:56.072+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":88,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:56.227+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":153,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:21:56.392+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":164,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:56.458+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:56.607+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:21:56.788+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":180,"影响行数":3}
{"level":"INFO","time":"2025-06-20T10:21:56.829+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":41,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:56.930+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":100,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:21:57.050+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:57.095+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":44,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:57.268+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":173,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:21:57.401+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:57.456+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:57.594+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":137,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:21:57.722+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:57.777+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:57.957+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":180,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:21:58.032+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:21:58.032+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-20T10:21:58.157+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-20T10:21:58.157+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-20T10:21:58.158+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-20T10:21:58.158+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-20T10:22:03.098+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"分组名称不能为空"}
{"level":"INFO","time":"2025-06-20T10:23:46.612+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-20T10:23:46.613+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-20T10:23:46.613+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-20T10:23:49.467+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:49.585+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":93,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:23:49.694+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":107,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:49.760+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:49.919+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:23:50.039+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:50.130+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:50.178+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":47,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:50.311+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":133,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:23:50.434+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":122,"影响行数":3}
{"level":"INFO","time":"2025-06-20T10:23:50.472+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":37,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:50.606+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":133,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:23:50.794+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":187,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:50.906+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":112,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:51.049+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":141,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:23:51.191+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:51.284+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:51.474+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":189,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:23:51.604+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":130,"影响行数":3}
{"level":"INFO","time":"2025-06-20T10:23:51.697+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:51.899+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":202,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:23:52.049+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:52.142+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:52.277+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:23:52.397+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:52.440+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":42,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:52.566+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:23:52.686+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:52.732+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":45,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:52.851+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":118,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:23:52.961+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T10:23:52.962+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-20T10:23:53.063+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-20T10:23:53.065+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-20T10:23:53.065+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-20T10:23:53.067+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-20T10:23:57.997+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND FIND_IN_SET(1, device_ids) ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":143,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:23:58.172+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":174,"影响行数":1}
{"level":"ERROR","time":"2025-06-20T10:23:58.229+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备 '修改后的设备名' 已存在于分组 'E3组更新' 中，不能重复添加"}
{"level":"INFO","time":"2025-06-20T10:24:28.116+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND FIND_IN_SET(31, device_ids) ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":180,"影响行数":0}
{"level":"INFO","time":"2025-06-20T10:24:28.231+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = 'D3'","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:24:28.330+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device_group` (`corp_id`,`title`,`device_type`,`device_ids`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','D3','0','31','2025-06-20 10:24:28.231','2025-06-20 10:24:28.231')","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:24:28.465+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE id IN (31) AND group_id != 0","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-20T10:24:28.586+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device` SET `group_id`=9,`updated_at`='2025-06-20 10:24:28.465' WHERE id IN (31)","耗时":121,"影响行数":1}
{"level":"INFO","time":"2025-06-20T14:00:13.208+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-20T14:00:13.209+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-20T14:00:13.209+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-20T14:00:19.976+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:20.147+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-06-20T14:00:20.331+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":183,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:20.411+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:20.514+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":103,"影响行数":1}
{"level":"INFO","time":"2025-06-20T14:00:20.649+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":134,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:20.877+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":226,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:20.986+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":108,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:21.178+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":191,"影响行数":1}
{"level":"INFO","time":"2025-06-20T14:00:21.347+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":169,"影响行数":3}
{"level":"INFO","time":"2025-06-20T14:00:21.452+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:21.638+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":185,"影响行数":1}
{"level":"INFO","time":"2025-06-20T14:00:21.847+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":209,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:21.966+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:22.208+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":240,"影响行数":1}
{"level":"INFO","time":"2025-06-20T14:00:22.452+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":243,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:22.572+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:22.847+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":275,"影响行数":1}
{"level":"INFO","time":"2025-06-20T14:00:23.090+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":242,"影响行数":3}
{"level":"INFO","time":"2025-06-20T14:00:23.206+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:23.486+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":279,"影响行数":1}
{"level":"INFO","time":"2025-06-20T14:00:23.686+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:23.811+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:24.052+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":240,"影响行数":1}
{"level":"INFO","time":"2025-06-20T14:00:24.276+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":222,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:24.371+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:24.616+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":244,"影响行数":1}
{"level":"INFO","time":"2025-06-20T14:00:24.851+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":235,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:24.966+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":114,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:25.167+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":200,"影响行数":1}
{"level":"INFO","time":"2025-06-20T14:00:25.427+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":260,"影响行数":-1}
{"level":"INFO","time":"2025-06-20T14:00:25.427+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-20T14:00:25.594+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-20T14:00:25.595+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-20T14:00:25.595+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-20T14:00:25.596+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-20T14:03:16.991+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 3 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":113,"影响行数":0}
{"level":"INFO","time":"2025-06-20T14:03:22.746+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":197,"影响行数":1}
{"level":"INFO","time":"2025-06-20T14:05:18.270+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 3 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":111,"影响行数":0}
{"level":"INFO","time":"2025-06-20T14:05:24.963+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":186,"影响行数":1}
{"level":"INFO","time":"2025-06-20T14:05:46.122+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 3 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":98,"影响行数":0}
{"level":"INFO","time":"2025-06-20T14:05:57.777+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-06-20T14:08:27.674+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":204,"影响行数":2}
{"level":"INFO","time":"2025-06-20T14:08:28.424+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":952,"影响行数":11}
{"level":"INFO","time":"2025-06-20T14:08:28.627+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":202,"影响行数":3}
{"level":"INFO","time":"2025-06-20T14:26:18.376+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":218,"影响行数":1}
{"level":"INFO","time":"2025-06-20T18:32:03.191+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-20T18:32:03.192+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-20T18:32:03.192+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
