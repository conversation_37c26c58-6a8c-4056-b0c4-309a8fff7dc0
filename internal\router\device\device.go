package device

import (
	"eyc3_meeting/internal/controller"

	"github.com/cloudwego/hertz/pkg/route"
)

func SetupDeviceRouter(deviceRouter *route.RouterGroup) {
	deviceApi := deviceRouter.Group("/device")
	{
		deviceApi.POST("/get_ls", controller.GetEycMeetingDeviceList)    // 获取设备列表
		deviceApi.GET("/get_info", controller.GetEycMeetingDeviceInfo)   // 获取设备详情
		deviceApi.POST("/add", controller.AddEycMeetingDevice)           // 新增设备
		deviceApi.POST("/modify", controller.ModifyEycMeetingDevice)     // 修改设备
		deviceApi.POST("/delete", controller.DeleteEycMeetingDevice)     // 删除设备
		deviceApi.POST("/unbind_reset", controller.UnbindAndResetDevice) // 解绑并重置设备

		// 设备分组相关路由
		groupRouter := deviceApi.Group("/group")
		groupRouter.POST("/get_ls", controller.GetDeviceGroupListWithCount)
		groupRouter.GET("/get_all", controller.GetAllEycDeviceGroups)
		groupRouter.GET("/get_info", controller.GetEycDeviceGroupInfo)
		groupRouter.POST("/add", controller.AddEycDeviceGroup)
		groupRouter.POST("/modify", controller.ModifyEycDeviceGroup)
		groupRouter.POST("/delete", controller.DeleteEycDeviceGroup)

		// 其他设备相关
		deviceApi.GET("/get_type_count", controller.GetDeviceTypeCount)
		deviceApi.GET("/get_devices_and_groups", controller.GetDevicesAndGroups)
	}
}
