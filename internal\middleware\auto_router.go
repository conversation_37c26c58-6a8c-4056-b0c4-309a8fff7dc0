package middleware

import (
	"strings"

	"eyc3_meeting/internal/pkg/logger"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/route"
)

// AutoRouter 自动路由中间件
// 允许同一个路由处理不同的HTTP方法
type AutoRouter struct {
	handlers map[string]map[string][]app.HandlerFunc
}

// NewAutoRouter 创建自动路由中间件
func NewAutoRouter() *AutoRouter {
	return &AutoRouter{
		handlers: make(map[string]map[string][]app.HandlerFunc),
	}
}

// Register 注册处理器
// path: 路由路径
// methods: HTTP方法，如"GET", "POST"，传空表示所有方法
// handlers: 处理函数
func (ar *AutoRouter) Register(path string, methods []string, handlers ...app.HandlerFunc) {
	normPath := strings.TrimRight(path, "/")
	if normPath == "" {
		normPath = "/"
	}

	if _, ok := ar.handlers[normPath]; !ok {
		ar.handlers[normPath] = make(map[string][]app.HandlerFunc)
	}

	// 如果未指定方法，则对所有方法有效
	if len(methods) == 0 {
		ar.handlers[normPath]["*"] = handlers
		return
	}

	// 注册指定的方法
	for _, method := range methods {
		ar.handlers[normPath][strings.ToUpper(method)] = handlers
	}
}

// ApplyToRouter 将自动路由应用到Hertz路由器
func (ar *AutoRouter) ApplyToRouter(r *route.Engine) {
	// 注册ANY处理器
	for path, methodHandlers := range ar.handlers {
		if handlers, ok := methodHandlers["*"]; ok {
			r.Any(path, handlers...)
			logger.Info("注册路由", logger.String("path", path), logger.String("method", "ANY"))
		} else {
			// 为每个方法单独注册
			for method, handlers := range methodHandlers {
				switch method {
				case "GET":
					r.GET(path, handlers...)
				case "POST":
					r.POST(path, handlers...)
				case "PUT":
					r.PUT(path, handlers...)
				case "DELETE":
					r.DELETE(path, handlers...)
				case "PATCH":
					r.PATCH(path, handlers...)
				case "HEAD":
					r.HEAD(path, handlers...)
				case "OPTIONS":
					r.OPTIONS(path, handlers...)
				}
				logger.Info("注册路由", logger.String("path", path), logger.String("method", method))
			}
		}
	}
}
