package config

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
)

// Config 应用配置结构体
type Config struct {
	Server   ServerConfig
	Database DatabaseConfig
	Redis    RedisConfig
	JWT      JWTConfig
	MQTT     MQTTConfig
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port int
	Mode string
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string
	Port     int
	User     string
	Password string
	DBName   string
	DSN      string
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string
	Port     int
	Password string
	DB       int
	URL      string
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret string
	Expire time.Duration
}

// MQTTConfig MQTT配置
type MQTTConfig struct {
	Broker   string
	ClientID string
	Username string
	Password string
}

var AppConfig Config

// Init 初始化配置
func Init() {
	// 加载.env文件
	if err := godotenv.Load(); err != nil {
		log.Println("警告: 未找到.env文件, 使用环境变量")
	}

	// 从环境变量加载配置
	loadFromEnv()

	// 生成派生配置
	generateDerivedConfig()
}

// 从环境变量加载配置
func loadFromEnv() {
	// 服务器配置
	serverPort, _ := strconv.Atoi(getEnv("SERVER_PORT", "9505"))
	AppConfig.Server.Port = serverPort
	AppConfig.Server.Mode = getEnv("SERVER_MODE", "release")

	// 数据库配置
	AppConfig.Database.Host = getEnv("DB_HOST", "localhost")
	dbPort, _ := strconv.Atoi(getEnv("DB_PORT", "3306"))
	AppConfig.Database.Port = dbPort
	AppConfig.Database.User = getEnv("DB_USER", "root")
	AppConfig.Database.Password = getEnv("DB_PASSWORD", "")
	AppConfig.Database.DBName = getEnv("DB_NAME", "hertz_demo")

	// Redis配置
	AppConfig.Redis.Host = getEnv("REDIS_HOST", "localhost")
	redisPort, _ := strconv.Atoi(getEnv("REDIS_PORT", "6379"))
	AppConfig.Redis.Port = redisPort
	AppConfig.Redis.Password = getEnv("REDIS_PASSWORD", "")
	redisDB, _ := strconv.Atoi(getEnv("REDIS_DB", "0"))
	AppConfig.Redis.DB = redisDB

	// JWT配置
	AppConfig.JWT.Secret = getEnv("JWT_SECRET", "default_jwt_secret")
	jwtExpire, _ := strconv.Atoi(getEnv("JWT_EXPIRE", "315360000"))
	AppConfig.JWT.Expire = time.Duration(jwtExpire) * time.Second

	// MQTT配置
	AppConfig.MQTT.Broker = getEnv("MQTT_BROKER", "tcp://localhost:1883")
	AppConfig.MQTT.ClientID = getEnv("MQTT_CLIENT_ID", "hertz_client")
	AppConfig.MQTT.Username = getEnv("MQTT_USERNAME", "")
	AppConfig.MQTT.Password = getEnv("MQTT_PASSWORD", "")
}

// 生成派生配置
func generateDerivedConfig() {
	// 生成MySQL DSN - 兼容MySQL 8.0
	AppConfig.Database.DSN = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local&allowNativePasswords=true",
		AppConfig.Database.User,
		AppConfig.Database.Password,
		AppConfig.Database.Host,
		AppConfig.Database.Port,
		AppConfig.Database.DBName,
	)

	// 生成Redis URL
	AppConfig.Redis.URL = fmt.Sprintf("%s:%d", AppConfig.Redis.Host, AppConfig.Redis.Port)
}

// 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}
