package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

const TableNameEycMeetingBooking = "eyc_meeting_bookings"

// 会议通知类型常量
const (
	NotifyTypeApp = "app" // 应用内通知
	NotifyTypeSMS = "sms" // 短信通知
)

// 会议状态常量
const (
	BookingStatusPending   = 1 // 待确认
	BookingStatusConfirmed = 2 // 已确认
	BookingStatusCanceled  = 3 // 已取消
	BookingStatusFinished  = 4 // 已结束
)

// 重复类型常量
const (
	RepeatTypeNone     = 0 // 不重复
	RepeatTypeDaily    = 1 // 每天
	RepeatTypeWeekly   = 2 // 每周
	RepeatTypeMonthly  = 3 // 每月
	RepeatTypeYearly   = 4 // 每年
	RepeatTypeWorkdays = 5 // 工作日
)

// 参会人角色常量
const (
	ParticipantRoleMember    = 1 // 参与者
	ParticipantRoleOrganizer = 2 // 组织者
)

// 参会人状态常量
const (
	ParticipantStatusPending   = 0 // 未回应
	ParticipantStatusAccepted  = 1 // 已接受
	ParticipantStatusRejected  = 2 // 已拒绝
	ParticipantStatusTentative = 3 // 待定
)

// 是否常量
const (
	Disabled = 0 // 否
	Enabled  = 1 // 是
)

// Attachment 附件结构
type Attachment struct {
	Name string `json:"name"` // 文件名
	URL  string `json:"url"`  // 文件地址
	Size int64  `json:"size"` // 文件大小(KB)
	Type string `json:"type"` // 文件类型
}

// AttachmentList 附件列表类型
type AttachmentList []Attachment

// Scan 实现 sql.Scanner 接口
func (a *AttachmentList) Scan(value interface{}) error {
	if value == nil {
		*a = AttachmentList{}
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("附件数据类型错误")
	}
	return json.Unmarshal(bytes, a)
}

// Value 实现 driver.Valuer 接口
func (a AttachmentList) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return json.Marshal(a)
}

// Participants 参会人信息(使用json.RawMessage以支持灵活的数据结构)
type Participants json.RawMessage

// Scan 实现 sql.Scanner 接口
func (p *Participants) Scan(value interface{}) error {
	if value == nil {
		*p = nil
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("参会人数据类型错误")
	}
	*p = Participants(bytes)
	return nil
}

// Value 实现 driver.Valuer 接口
func (p Participants) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return []byte(p), nil
}

// Reminder 提醒设置
type Reminder struct {
	MinutesBefore int    `json:"minutes_before"` // 提前多少分钟
	Label         string `json:"label"`          // 显示文本
}

// ReminderList 提醒列表
type ReminderList []Reminder

// Scan 实现 sql.Scanner 接口
func (r *ReminderList) Scan(value interface{}) error {
	if value == nil {
		*r = ReminderList{}
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("提醒数据类型错误")
	}
	return json.Unmarshal(bytes, r)
}

// Value 实现 driver.Valuer 接口
func (r ReminderList) Value() (driver.Value, error) {
	if r == nil {
		return nil, nil
	}
	return json.Marshal(r)
}

// EycMeetingBooking 会议预订表
type EycMeetingBooking struct {
	ID     int    `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Corpid string `gorm:"column:corp_id;not null;comment:企业ID" json:"corpid"`
	Title  string `gorm:"column:title;not null;comment:会议标题" json:"title"`

	// 会议室信息
	RoomIDs   string `gorm:"column:room_ids;comment:会议室ID列表(逗号分隔)" json:"room_ids"`
	RoomNames string `gorm:"column:room_names;comment:会议室名称列表(逗号分隔)" json:"room_names"`
	Location  string `gorm:"column:location;comment:会议地点" json:"location"`

	// 时间信息
	StartTime time.Time `gorm:"column:start_time;not null;comment:开始时间" json:"start_time"`
	EndTime   time.Time `gorm:"column:end_time;not null;comment:结束时间" json:"end_time"`
	IsFullDay int       `gorm:"column:is_full_day;default:0;comment:是否全天(0-否,1-是)" json:"is_full_day"`

	// 参会人信息 - 改为使用RawJSON类型
	Participants      RawJSON `gorm:"column:participants;type:json;comment:参会人信息" json:"participants"`
	AllowInviteOthers int     `gorm:"column:allow_invite_others;default:1;comment:允许邀请其他成员加入日程(0-否,1-是)" json:"allow_invite_others"`

	// 通知设置
	NotifyTypes  string `gorm:"column:notify_types;comment:通知方式(逗号分隔:app,sms)" json:"notify_types"`
	EnableNotify int    `gorm:"column:enable_notify;default:1;comment:是否启用通知(0-否,1-是)" json:"enable_notify"`

	// 提醒设置
	Reminders RawJSON `gorm:"column:reminders;type:json;comment:提醒设置" json:"reminders"`

	// 重复设置
	RepeatType    int        `gorm:"column:repeat_type;default:0;comment:重复类型" json:"repeat_type"`
	RepeatEndDate *time.Time `gorm:"column:repeat_end_date;comment:重复结束日期" json:"repeat_end_date"`

	// 其他信息
	Description string  `gorm:"column:description;type:text;comment:会议内容" json:"description"`
	Attachments RawJSON `gorm:"column:attachments;type:json;comment:附件列表" json:"attachments"`
	Status      int     `gorm:"column:status;default:1;comment:状态" json:"status"`

	CreatedAt     time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`
	CreatedBy     string    `gorm:"column:created_by;comment:创建人ID" json:"created_by"`
	CreatedByName string    `gorm:"column:created_by_name;comment:创建人姓名" json:"created_by_name"`
}

// TableName EycMeetingBooking的表名
func (*EycMeetingBooking) TableName() string {
	return TableNameEycMeetingBooking
}
