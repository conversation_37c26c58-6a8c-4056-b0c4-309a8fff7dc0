{"level":"INFO","time":"2025-06-13T08:54:46.910+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":91,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:47.184+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":261,"影响行数":1}
{"level":"INFO","time":"2025-06-13T08:54:47.440+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":256,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:47.539+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:47.715+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":174,"影响行数":1}
{"level":"INFO","time":"2025-06-13T08:54:47.955+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":240,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:48.209+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":253,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:48.300+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:48.421+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-06-13T08:54:48.750+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":328,"影响行数":3}
{"level":"INFO","time":"2025-06-13T08:54:48.845+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:49.020+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":174,"影响行数":1}
{"level":"INFO","time":"2025-06-13T08:54:49.210+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":189,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:49.308+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:49.530+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":221,"影响行数":1}
{"level":"INFO","time":"2025-06-13T08:54:49.752+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":221,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:49.870+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:50.038+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":166,"影响行数":1}
{"level":"INFO","time":"2025-06-13T08:54:50.209+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":169,"影响行数":3}
{"level":"INFO","time":"2025-06-13T08:54:50.268+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:50.400+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-06-13T08:54:50.545+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:50.625+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:50.883+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":257,"影响行数":1}
{"level":"INFO","time":"2025-06-13T08:54:51.023+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:51.132+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":108,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:51.251+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":118,"影响行数":1}
{"level":"INFO","time":"2025-06-13T08:54:51.413+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":161,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:51.508+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:51.693+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":185,"影响行数":1}
{"level":"INFO","time":"2025-06-13T08:54:51.840+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":146,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T08:54:51.841+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T08:54:52.004+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T08:54:52.007+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T08:54:52.007+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T08:54:52.008+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T09:03:51.027+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T09:03:51.028+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T09:03:51.028+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T09:03:53.035+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:53.251+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":189,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:03:53.385+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:53.451+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:53.590+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":139,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:03:53.760+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":169,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:53.877+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:53.931+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:54.077+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:03:54.251+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":173,"影响行数":3}
{"level":"INFO","time":"2025-06-13T09:03:54.331+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:54.436+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":104,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:03:54.575+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":138,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:54.654+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:54.778+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:03:54.877+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:54.919+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":41,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:55.100+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":181,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:03:55.261+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":161,"影响行数":3}
{"level":"INFO","time":"2025-06-13T09:03:55.335+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:55.506+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":168,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:03:55.657+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":151,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:55.721+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:55.860+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:03:55.990+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:56.076+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:56.251+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":175,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:03:56.451+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:56.545+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:56.852+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":306,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:03:57.003+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":150,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:03:57.004+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T09:03:57.188+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T09:03:57.190+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T09:03:57.190+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T09:03:57.191+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-13T09:04:08.677+0800","caller":"middleware/jwt.go:41","msg":"解析Token失败","error":"token has invalid claims: token is expired"}
{"level":"ERROR","time":"2025-06-13T09:05:37.373+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":80,"error":"Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T09:05:37.373+0800","caller":"controller/base.go:349","msg":"获取所有记录失败","error":"Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T09:05:37.374+0800","caller":"response/response.go:130","msg":"服务器内部错误","error":"Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T09:05:41.812+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":286,"error":"Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T09:05:41.812+0800","caller":"controller/base.go:349","msg":"获取所有记录失败","error":"Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T09:05:41.812+0800","caller":"response/response.go:130","msg":"服务器内部错误","error":"Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T09:06:49.502+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":42,"error":"Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T09:06:49.502+0800","caller":"controller/base.go:349","msg":"获取所有记录失败","error":"Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T09:06:49.502+0800","caller":"response/response.go:130","msg":"服务器内部错误","error":"Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T09:07:00.534+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":64,"error":"Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T09:07:00.534+0800","caller":"controller/base.go:349","msg":"获取所有记录失败","error":"Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T09:07:00.535+0800","caller":"response/response.go:130","msg":"服务器内部错误","error":"Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T09:10:08.572+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T09:10:08.572+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T09:10:08.572+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T09:10:13.816+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:14.067+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":224,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:10:14.212+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:14.318+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":105,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:14.557+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":238,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:10:14.738+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":180,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:14.898+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:15.082+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":182,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:15.218+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:10:15.391+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":172,"影响行数":3}
{"level":"INFO","time":"2025-06-13T09:10:15.441+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":48,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:15.657+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":215,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:10:15.858+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":201,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:15.962+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":102,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:16.167+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":205,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:10:16.498+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":330,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:16.601+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":102,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:16.777+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":174,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:10:17.001+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":224,"影响行数":3}
{"level":"INFO","time":"2025-06-13T09:10:17.113+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:17.307+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":194,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:10:17.428+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:17.619+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":189,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:17.679+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":60,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:10:17.858+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":179,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:17.993+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:18.152+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:10:18.367+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":213,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:18.442+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:18.658+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":215,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:10:18.823+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":164,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:10:18.823+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T09:10:19.038+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T09:10:19.041+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T09:10:19.041+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T09:10:19.044+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T09:10:23.589+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":123,"影响行数":2}
{"level":"INFO","time":"2025-06-13T09:10:40.702+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":220,"影响行数":1}
{"level":"ERROR","time":"2025-06-13T09:11:38.795+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"INFO","time":"2025-06-13T09:23:04.275+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T09:23:04.275+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T09:23:04.276+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T09:23:08.369+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":189,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:08.598+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":201,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:23:08.703+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:08.761+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:08.883+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":121,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:23:09.012+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:09.157+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":143,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:09.234+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:09.357+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":123,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:23:09.451+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":93,"影响行数":3}
{"level":"INFO","time":"2025-06-13T09:23:09.513+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:09.634+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:23:09.744+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:09.820+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":75,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:09.932+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:23:10.128+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":195,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:10.221+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:10.353+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:23:10.497+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":143,"影响行数":3}
{"level":"INFO","time":"2025-06-13T09:23:10.577+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:10.712+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:23:10.918+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":204,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:11.038+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:11.161+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":122,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:23:11.348+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":187,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:11.463+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":114,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:11.592+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":128,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:23:11.792+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:11.873+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:12.052+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":178,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:23:12.263+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":210,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:23:12.263+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T09:23:12.492+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T09:23:12.493+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T09:23:12.493+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T09:23:12.495+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-13T09:23:51.394+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '测试门牌001'","耗时":68,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T09:23:51.394+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T09:24:38.644+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '测试门牌001'","耗时":64,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T09:24:38.644+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T09:26:33.275+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T09:26:33.276+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T09:26:33.276+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T09:26:37.351+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:37.878+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":513,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:26:38.173+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":295,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:38.234+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":60,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:38.401+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":166,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:26:38.570+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":168,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:38.735+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":165,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:38.825+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:39.015+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":188,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:26:39.190+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":174,"影响行数":3}
{"level":"INFO","time":"2025-06-13T09:26:39.261+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:39.407+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:26:39.516+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:39.557+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":41,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:39.718+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":161,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:26:39.896+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":177,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:39.990+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:40.197+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":206,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:26:40.384+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":186,"影响行数":3}
{"level":"INFO","time":"2025-06-13T09:26:40.483+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:40.605+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":121,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:26:40.745+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:40.840+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:41.007+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":166,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:26:41.254+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":247,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:41.286+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":31,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:41.428+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":141,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:26:41.611+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":182,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:41.705+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:41.922+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":216,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:26:42.087+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":164,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:26:42.088+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T09:26:42.232+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T09:26:42.233+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T09:26:42.234+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T09:26:42.235+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T09:28:26.346+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T09:28:26.347+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T09:28:26.347+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T09:28:28.153+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:28.329+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:28:28.454+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:28.522+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:28.680+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:28:28.930+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":249,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:29.043+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":112,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:29.106+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:29.379+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":272,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:28:29.618+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":238,"影响行数":3}
{"level":"INFO","time":"2025-06-13T09:28:29.743+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":125,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:29.867+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:28:30.024+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":156,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:30.073+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":48,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:30.219+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:28:30.360+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:30.404+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":43,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:30.493+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":88,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:28:30.713+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":220,"影响行数":3}
{"level":"INFO","time":"2025-06-13T09:28:30.769+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:30.904+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:28:31.133+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":228,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:31.227+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:31.377+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:28:31.614+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":236,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:31.807+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":191,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:32.047+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":239,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:28:32.254+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":206,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:32.368+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":113,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:32.549+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":181,"影响行数":1}
{"level":"INFO","time":"2025-06-13T09:28:32.729+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":179,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T09:28:32.729+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T09:28:32.860+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T09:28:32.862+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T09:28:32.862+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T09:28:32.864+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T09:28:39.719+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '测试门牌001'","耗时":200,"影响行数":1}
{"level":"ERROR","time":"2025-06-13T09:28:39.719+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称已存在"}
{"level":"INFO","time":"2025-06-13T09:28:47.687+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '测试门牌009'","耗时":159,"影响行数":1}
{"level":"ERROR","time":"2025-06-13T09:28:47.746+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE sn = 'SN00123456787' AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":59,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T09:28:47.969+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌009','SN00123456787','','door',100,100,0,'','','',0,'standard',0,'07:00:00','23:59:59',0,1,1,'2025-06-13 09:28:47.824','2025-06-13 09:28:47.824')","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:10:49.957+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T10:10:49.958+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T10:10:49.959+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T10:12:58.511+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:12:58.672+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:12:58.823+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":151,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:12:58.890+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:12:59.019+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":128,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:12:59.161+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:12:59.280+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:12:59.380+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:12:59.543+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":163,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:12:59.700+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":156,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:12:59.778+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:12:59.950+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":171,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:13:00.109+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:13:00.215+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:13:00.410+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":195,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:13:00.596+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":185,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:13:00.704+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":107,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:13:00.899+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":194,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:13:01.033+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":133,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:13:01.274+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":240,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:13:02.051+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":777,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:13:02.279+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":227,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:13:02.365+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:13:02.509+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:13:02.699+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":189,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:13:02.780+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:13:02.960+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":180,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:13:03.130+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":169,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:13:03.198+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:13:03.396+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":197,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:13:03.560+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:13:03.560+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T10:13:03.734+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T10:13:03.736+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T10:13:03.736+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T10:13:03.738+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-13T10:13:12.802+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"INFO","time":"2025-06-13T10:17:00.285+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T10:17:00.285+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T10:17:00.285+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T10:17:03.750+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:03.960+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":197,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:17:04.180+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":219,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:04.280+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:04.495+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":214,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:17:04.632+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":136,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:04.818+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":184,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:04.900+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":82,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:05.080+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":178,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:17:05.173+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":93,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:17:05.270+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:05.481+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":210,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:17:05.672+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":190,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:05.745+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:05.909+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:17:06.044+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":134,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:06.095+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":50,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:06.206+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:17:06.443+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":236,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:17:06.559+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:06.689+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:17:06.878+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":188,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:07.009+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:07.174+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:17:07.320+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:07.385+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:07.508+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":122,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:17:07.649+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:07.760+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:08.115+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":354,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:17:08.276+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":160,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:17:08.276+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T10:17:08.557+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T10:17:08.559+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T10:17:08.559+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T10:17:08.561+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-13T10:17:12.240+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE title = '测试门牌005' AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":43,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T10:17:12.336+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE sn = 'SN00123456787' AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":95,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T10:17:12.514+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌005','SN00123456787','','door',100,100,0,'','','',0,'standard',0,'08:00:00','22:00:00',0,1,1,'2025-06-13 10:17:12.389','2025-06-13 10:17:12.389')","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:24:18.061+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T10:24:18.062+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T10:24:18.062+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T10:24:21.666+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":72,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:21.792+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":100,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:24:21.912+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:21.969+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:22.079+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:24:22.186+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:22.266+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:22.301+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":34,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:22.427+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:24:22.524+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":96,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:24:22.583+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:22.677+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":93,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:24:22.797+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:22.857+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:22.986+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:24:23.082+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:23.154+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:23.303+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:24:23.488+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":184,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:24:23.519+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":30,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:23.678+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:24:23.817+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":138,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:23.881+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:23.972+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":90,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:24:24.116+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:24.187+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:24.332+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:24:24.628+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":295,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:24.728+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:24.928+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:24:25.071+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:24:25.071+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T10:24:25.286+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T10:24:25.288+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T10:24:25.288+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T10:24:25.289+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-13T10:24:32.513+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE title = '测试门牌006' AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":69,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T10:24:32.614+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE sn = 'SN00123456787' AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":99,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T10:24:32.848+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌006','SN00123456787','D3','door',90,80,1,'#1678FF','#1678FF','#F7885C',0,'split_screen',1,'08:00:00','22:00:00',1,1,0,'2025-06-13 10:24:32.681','2025-06-13 10:24:32.681')","耗时":166,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:24:33.044+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":196,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:24:33.254+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,7',`updated_at`='2025-06-13 10:24:33.044' WHERE `id` = 1","耗时":209,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:32:58.625+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T10:32:58.626+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T10:32:58.626+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T10:33:03.954+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:04.168+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":201,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:33:04.354+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":185,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:04.414+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:04.594+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:33:04.782+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":187,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:04.968+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":185,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:05.041+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":72,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:05.234+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":192,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:33:05.474+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":239,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:33:05.578+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":104,"影响行数":-1}
{"level":"WARN","time":"2025-06-13T10:33:07.117+0800","caller":"model/logger.go:81","msg":"GORM慢查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":1538,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:33:07.257+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:07.314+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:07.427+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":113,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:33:07.539+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":111,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:07.618+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:07.772+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:33:07.896+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":123,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:33:07.939+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":42,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:08.103+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":163,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:33:08.217+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":114,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:08.296+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:08.434+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:33:08.603+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":168,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:08.674+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:08.772+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":98,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:33:08.919+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":145,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:08.956+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":36,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:09.057+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":100,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:33:09.154+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:33:09.154+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T10:33:09.267+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T10:33:09.269+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T10:33:09.270+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T10:33:09.271+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-13T10:33:25.794+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE title = '测试门牌006' AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":136,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T10:33:25.894+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE sn = 'SN00123456787' AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":99,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T10:33:26.137+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌006','SN00123456787','D3','door',90,80,1,'#1678FF','#1678FF','#F7885C',0,'split_screen',1,'08:00:00','22:00:00',1,1,0,'2025-06-13 10:33:25.972','2025-06-13 10:33:25.972')","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:33:26.284+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":147,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:33:26.437+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,7,8',`updated_at`='2025-06-13 10:33:26.285' WHERE `id` = 1","耗时":153,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:41:07.980+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T10:41:07.981+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T10:41:07.981+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T10:41:12.107+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":123,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:12.262+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:41:12.455+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":192,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:12.545+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:12.697+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":151,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:41:12.832+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":134,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:12.961+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:13.108+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":146,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:13.380+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":272,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:41:13.540+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":159,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:41:13.606+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:13.727+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":121,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:41:13.907+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":179,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:13.965+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:14.088+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":123,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:41:14.180+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:14.254+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:14.365+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":109,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:41:14.472+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":106,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:41:14.572+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:14.766+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":193,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:41:14.972+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":205,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:15.062+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:15.210+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":147,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:41:15.412+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":200,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:15.501+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":88,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:15.692+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":190,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:41:15.862+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":169,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:15.963+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:16.132+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:41:16.323+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":190,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:41:16.323+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T10:41:16.497+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T10:41:16.498+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T10:41:16.498+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T10:41:16.499+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-13T10:41:16.742+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌006' OR sn = 'SN00123456787')","耗时":65,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T10:41:16.987+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌006','SN00123456787','D3','door',90,80,1,'#1678FF','#1678FF','#F7885C',0,'split_screen',1,'08:00:00','22:00:00',1,1,0,'2025-06-13 10:41:16.824','2025-06-13 10:41:16.824')","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:41:17.136+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":148,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:41:17.323+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,7,8,9',`updated_at`='2025-06-13 10:41:17.136' WHERE `id` = 1","耗时":187,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:47:15.044+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T10:47:15.045+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T10:47:15.045+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T10:47:19.530+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:19.659+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:47:19.820+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":160,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:19.910+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:20.112+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":202,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:47:20.250+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:20.451+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":194,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:20.530+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:20.710+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":180,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:47:20.950+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":239,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:47:21.069+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:21.344+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":274,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:47:21.619+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":274,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:21.727+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":107,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:21.912+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":185,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:47:22.125+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":212,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:22.224+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:22.414+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":190,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:47:22.624+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":210,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:47:22.730+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:22.929+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":198,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:47:23.099+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":170,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:23.182+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:23.280+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":97,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:47:23.504+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":223,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:23.567+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:23.668+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:47:23.770+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:23.833+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:24.064+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":230,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:47:24.410+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":345,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:47:24.410+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T10:47:24.604+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T10:47:24.605+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T10:47:24.605+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T10:47:24.606+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-13T10:47:28.264+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title IN ('测试门牌006','SN00123456787') OR sn IN ('测试门牌006','SN00123456787'))","耗时":51,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T10:47:28.459+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌006','SN00123456787','D3','door',90,80,1,'#1678FF','#1678FF','#F7885C',0,'split_screen',1,'08:00:00','22:00:00',1,1,0,'2025-06-13 10:47:28.336','2025-06-13 10:47:28.336')","耗时":123,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:47:28.598+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:47:28.763+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,7,8,9,10',`updated_at`='2025-06-13 10:47:28.599' WHERE `id` = 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:49:25.080+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T10:49:25.081+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T10:49:25.081+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T10:49:29.311+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:29.532+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":192,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:49:29.697+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":164,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:29.807+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:29.913+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":106,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:49:30.117+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":204,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:30.332+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":214,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:30.442+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":108,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:30.633+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":190,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:49:30.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":202,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:49:30.972+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":135,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:31.158+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":185,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:49:31.387+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":228,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:31.452+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:31.588+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:49:31.886+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":297,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:32.002+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:32.176+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":174,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:49:32.333+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":156,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:49:32.415+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":81,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:32.635+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":220,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:49:32.792+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":155,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:32.892+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:33.072+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:49:33.183+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:33.317+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:33.463+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:49:33.637+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":173,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:33.732+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:33.900+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":168,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:49:34.100+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:49:34.100+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T10:49:34.276+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T10:49:34.277+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T10:49:34.277+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T10:49:34.278+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-13T10:49:36.627+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌006' OR sn = '测试门牌006' OR title = 'SN00123456787' OR sn = 'SN00123456787')","耗时":91,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T10:49:36.921+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌006','SN00123456787','D3','door',90,80,1,'#1678FF','#1678FF','#F7885C',0,'split_screen',1,'08:00:00','22:00:00',1,1,0,'2025-06-13 10:49:36.717','2025-06-13 10:49:36.717')","耗时":204,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:49:37.097+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":174,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:49:37.293+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,7,8,9,10,11',`updated_at`='2025-06-13 10:49:37.098' WHERE `id` = 1","耗时":195,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:53:21.111+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T10:53:21.112+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T10:53:21.112+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T10:53:25.365+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:25.592+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":204,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:53:25.796+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":203,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:25.858+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:26.044+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":184,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:53:26.206+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":161,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:26.347+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:26.441+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:26.601+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:53:26.737+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":135,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:53:26.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:27.036+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:53:27.217+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":180,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:27.301+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:27.412+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:53:27.618+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":205,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:27.702+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:27.911+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":209,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:53:28.177+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":265,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:53:28.337+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:28.534+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":196,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:53:28.715+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":180,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:28.782+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:28.995+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":212,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:53:29.227+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":232,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:29.325+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:29.560+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":234,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:53:29.796+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":236,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:29.891+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:29.988+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":96,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:53:30.146+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":157,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:53:30.147+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T10:53:30.426+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T10:53:30.428+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T10:53:30.428+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T10:53:30.429+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-13T10:53:41.086+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌006' OR sn = '测试门牌006')","耗时":101,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T10:53:41.165+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = 'SN00123456787' OR sn = 'SN00123456787')","耗时":79,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T10:53:41.512+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌006','SN00123456787','D3','door',90,80,1,'#1678FF','#1678FF','#F7885C',0,'split_screen',1,'08:00:00','22:00:00',1,1,0,'2025-06-13 10:53:41.272','2025-06-13 10:53:41.272')","耗时":239,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:53:41.722+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":210,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:53:41.997+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,7,8,9,10,11,12',`updated_at`='2025-06-13 10:53:41.722' WHERE `id` = 1","耗时":275,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:56:17.783+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T10:56:17.784+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T10:56:17.784+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T10:56:25.249+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:25.381+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":117,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:56:25.508+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:25.831+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":322,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:26.158+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":327,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:56:26.295+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":135,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:26.425+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:26.501+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:26.661+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:56:26.854+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":193,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:56:26.982+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":127,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:27.146+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":163,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:56:27.306+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":160,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:27.361+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:27.509+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":147,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:56:27.658+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:27.714+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":55,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:27.845+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:56:28.050+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":204,"影响行数":3}
{"level":"INFO","time":"2025-06-13T10:56:28.122+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:28.261+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:56:28.399+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:28.455+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:28.635+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":180,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:56:28.799+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:28.870+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:29.043+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":171,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:56:29.166+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:29.259+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:29.403+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":142,"影响行数":1}
{"level":"INFO","time":"2025-06-13T10:56:29.519+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T10:56:29.520+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T10:56:29.658+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T10:56:29.659+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T10:56:29.659+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T10:56:29.660+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-13T10:57:05.554+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌006' OR sn = '测试门牌006')","耗时":42,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T10:57:29.971+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = 'SN00123456787' OR sn = 'SN00123456787')","耗时":56,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T11:01:09.619+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌006','SN00123456787','D3','door',90,80,1,'#1678FF','#1678FF','#F7885C',0,'split_screen',1,'08:00:00','22:00:00',1,1,0,'2025-06-13 11:01:09.48','2025-06-13 11:01:09.48')","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:01:09.726+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":106,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:01:09.889+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,7,8,9,10,11,12,13',`updated_at`='2025-06-13 11:01:09.727' WHERE `id` = 1","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:01:42.837+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T11:01:42.837+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T11:01:42.838+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T11:01:47.855+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:48.052+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":168,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:01:48.160+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":107,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:48.251+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:48.425+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":173,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:01:48.584+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:48.765+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":180,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:48.837+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:48.964+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:01:49.200+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":235,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:01:49.244+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":44,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:49.414+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:01:49.540+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":125,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:49.616+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":75,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:49.716+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":100,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:01:49.870+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":153,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:49.938+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:50.086+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":146,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:01:50.211+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":124,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:01:50.307+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:50.501+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":193,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:01:50.644+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:50.683+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":38,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:50.812+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:01:50.979+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":165,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:51.032+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":52,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:51.188+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":155,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:01:51.331+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:51.427+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:51.644+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":217,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:01:51.866+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":221,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:01:51.866+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T11:01:52.026+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T11:01:52.028+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T11:01:52.028+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T11:01:52.029+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-13T11:01:58.056+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌006' OR sn = '测试门牌006' OR title = 'SN00123456787' OR sn = 'SN00123456787')","耗时":67,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T11:01:58.227+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌006','SN00123456787','D3','door',90,80,1,'#1678FF','#1678FF','#F7885C',0,'split_screen',1,'08:00:00','22:00:00',1,1,0,'2025-06-13 11:01:58.056','2025-06-13 11:01:58.056')","耗时":170,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:01:58.352+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":124,"影响行数":1}
{"level":"ERROR","time":"2025-06-13T11:01:58.509+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,7,8,9,10,11,12,13,14',`updated_at`='2025-06-13 11:01:58.353' WHERE `id` = 1","耗时":156,"error":"Error 1406 (22001): Data too long for column 'device_ids' at row 1"}
{"level":"ERROR","time":"2025-06-13T11:01:58.564+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"Error 1406 (22001): Data too long for column 'device_ids' at row 1"}
{"level":"INFO","time":"2025-06-13T11:25:41.739+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T11:25:41.739+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T11:25:41.740+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T11:25:52.271+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:52.460+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:25:52.628+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":168,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:52.693+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:52.852+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:25:53.012+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:53.231+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":218,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:53.304+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:53.391+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":85,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:25:53.554+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":162,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:25:53.595+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":40,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:53.772+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":175,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:25:53.921+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":148,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:53.981+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:54.119+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":137,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:25:54.258+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":138,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:54.355+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:54.517+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":161,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:25:54.650+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":132,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:25:54.700+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:54.870+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:25:55.049+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":178,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:55.116+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:55.286+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":170,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:25:55.475+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":188,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:55.571+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:55.816+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":244,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:25:55.971+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":154,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:56.029+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:56.196+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":167,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:25:56.396+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":198,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:25:56.396+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T11:25:56.584+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T11:25:56.586+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T11:25:56.586+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T11:25:56.587+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-13T11:26:21.707+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌006' OR sn = '测试门牌006')","耗时":76,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T11:26:21.786+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = 'SN00123456787' OR sn = 'SN00123456787')","耗时":78,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T11:26:22.081+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌006','SN00123456787','D3','door',90,80,1,'#1678FF','#1678FF','#F7885C',0,'split_screen',1,'08:00:00','22:00:00',1,1,0,'2025-06-13 11:26:21.877','2025-06-13 11:26:21.877')","耗时":204,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:26:22.271+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":189,"影响行数":1}
{"level":"ERROR","time":"2025-06-13T11:26:22.482+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,7,8,9,10,11,12,13,15',`updated_at`='2025-06-13 11:26:22.272' WHERE `id` = 1","耗时":210,"error":"Error 1406 (22001): Data too long for column 'device_ids' at row 1"}
{"level":"ERROR","time":"2025-06-13T11:26:22.587+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"Error 1406 (22001): Data too long for column 'device_ids' at row 1"}
{"level":"INFO","time":"2025-06-13T11:26:29.965+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T11:26:29.966+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T11:26:29.966+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T11:26:31.960+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:32.104+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:26:32.271+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":166,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:32.372+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:32.532+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:26:32.622+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:32.760+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:32.809+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":48,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:32.961+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":151,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:26:33.191+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":229,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:26:33.259+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:33.372+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":113,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:26:33.511+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:33.572+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":60,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:33.733+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":160,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:26:33.821+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":88,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:33.881+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:34.015+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":133,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:26:34.151+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":136,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:26:34.213+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":60,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:34.372+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:26:34.510+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":138,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:34.612+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:34.792+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:26:35.016+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":223,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:35.109+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:35.221+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":111,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:26:35.436+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":215,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:35.514+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:35.686+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":171,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:26:35.840+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":153,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:26:35.840+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T11:26:35.995+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T11:26:35.996+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T11:26:35.996+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T11:26:35.997+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-13T11:26:39.493+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌006' OR sn = '测试门牌006')","耗时":60,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T11:26:39.534+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = 'SN00123456787' OR sn = 'SN00123456787')","耗时":41,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T11:26:39.751+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌006','SN00123456787','D3','door',90,80,1,'#1678FF','#1678FF','#F7885C',0,'split_screen',1,'08:00:00','22:00:00',1,1,0,'2025-06-13 11:26:39.616','2025-06-13 11:26:39.616')","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:26:39.872+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":120,"影响行数":1}
{"level":"ERROR","time":"2025-06-13T11:26:39.991+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,7,8,9,10,11,12,13,16',`updated_at`='2025-06-13 11:26:39.872' WHERE `id` = 1","耗时":119,"error":"Error 1406 (22001): Data too long for column 'device_ids' at row 1"}
{"level":"ERROR","time":"2025-06-13T11:26:40.051+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"Error 1406 (22001): Data too long for column 'device_ids' at row 1"}
{"level":"ERROR","time":"2025-06-13T11:26:56.987+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌006' OR sn = '测试门牌006')","耗时":70,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T11:26:57.070+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = 'SN00123456787' OR sn = 'SN00123456787')","耗时":83,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T11:26:57.291+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌006','SN00123456787','D3','door',90,80,1,'#1678FF','#1678FF','#F7885C',0,'split_screen',1,'08:00:00','22:00:00',1,1,0,'2025-06-13 11:26:57.172','2025-06-13 11:26:57.172')","耗时":118,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:26:57.468+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":177,"影响行数":1}
{"level":"ERROR","time":"2025-06-13T11:26:57.691+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,7,8,9,10,11,12,13,17',`updated_at`='2025-06-13 11:26:57.469' WHERE `id` = 1","耗时":222,"error":"Error 1406 (22001): Data too long for column 'device_ids' at row 1"}
{"level":"ERROR","time":"2025-06-13T11:26:57.738+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"Error 1406 (22001): Data too long for column 'device_ids' at row 1"}
{"level":"INFO","time":"2025-06-13T11:29:11.675+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T11:29:11.676+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T11:29:11.676+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T11:29:16.281+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:16.436+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":141,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:16.646+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":209,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:16.749+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":103,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:16.935+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":185,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:17.074+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":138,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:17.228+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":152,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:17.283+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:17.388+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":104,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:17.537+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":149,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:29:17.607+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:17.735+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":126,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:17.885+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":150,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:18.004+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:18.149+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:18.260+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:18.365+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:18.535+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":170,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:18.696+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":160,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:29:18.739+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":43,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:18.875+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:19.070+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":194,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:19.164+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:19.298+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:19.455+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":157,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:19.503+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:19.641+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:19.815+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":174,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:19.896+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:20.008+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":112,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:20.104+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:20.104+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T11:29:20.149+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":44,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:20.253+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":104,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:20.353+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 'eyc_meeting_device_group' AND table_type = 'BASE TABLE'","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:20.392+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":38,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:20.479+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":87,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:20.598+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` LIMIT 1","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:20.720+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 'eyc_meeting_device_group' ORDER BY ORDINAL_POSITION","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:20.864+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device_group` MODIFY COLUMN `corpid` longtext NOT NULL","耗时":142,"影响行数":4}
{"level":"INFO","time":"2025-06-13T11:29:21.041+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device_group` MODIFY COLUMN `group_name` longtext NOT NULL","耗时":176,"影响行数":4}
{"level":"INFO","time":"2025-06-13T11:29:21.238+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device_group` MODIFY COLUMN `device_type` longtext","耗时":197,"影响行数":4}
{"level":"INFO","time":"2025-06-13T11:29:21.415+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device_group` MODIFY COLUMN `device_ids` text","耗时":176,"影响行数":4}
{"level":"INFO","time":"2025-06-13T11:29:21.578+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device_group` MODIFY COLUMN `created_at` datetime(3) NULL","耗时":162,"影响行数":4}
{"level":"INFO","time":"2025-06-13T11:29:21.735+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device_group` MODIFY COLUMN `updated_at` datetime(3) NULL","耗时":157,"影响行数":4}
{"level":"INFO","time":"2025-06-13T11:29:21.793+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:21.913+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":118,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:22.066+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 'eyc_meeting_device' AND table_type = 'BASE TABLE'","耗时":153,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:22.109+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":43,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:22.261+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":151,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:22.377+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` LIMIT 1","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:22.494+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 'eyc_meeting_device' ORDER BY ORDINAL_POSITION","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:22.660+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `corp_id` longtext NOT NULL COMMENT 'corp_id'","耗时":166,"影响行数":11}
{"level":"INFO","time":"2025-06-13T11:29:22.822+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `title` longtext NOT NULL COMMENT '设备名称'","耗时":162,"影响行数":11}
{"level":"INFO","time":"2025-06-13T11:29:22.985+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `sn` longtext COMMENT '设备序列号'","耗时":163,"影响行数":11}
{"level":"INFO","time":"2025-06-13T11:29:23.175+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `model` longtext COMMENT '设备型号'","耗时":189,"影响行数":11}
{"level":"INFO","time":"2025-06-13T11:29:23.380+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `device_type` varchar(191) NOT NULL DEFAULT 'door' COMMENT '设备类型'","耗时":204,"影响行数":11}
{"level":"INFO","time":"2025-06-13T11:29:23.575+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `custom_template` bigint DEFAULT 0 COMMENT '是否自定义模板'","耗时":195,"影响行数":11}
{"level":"INFO","time":"2025-06-13T11:29:23.765+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `template_free` longtext COMMENT '暂无会议模板'","耗时":188,"影响行数":11}
{"level":"INFO","time":"2025-06-13T11:29:23.964+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `template_idle` longtext COMMENT '会议空闲模板'","耗时":198,"影响行数":11}
{"level":"INFO","time":"2025-06-13T11:29:24.145+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `template_using` longtext COMMENT '会议使用中模板'","耗时":180,"影响行数":11}
{"level":"INFO","time":"2025-06-13T11:29:24.343+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `related_door` bigint DEFAULT 0 COMMENT '是否关联门牌'","耗时":198,"影响行数":11}
{"level":"INFO","time":"2025-06-13T11:29:24.573+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `system_mode` varchar(191) DEFAULT 'standard' COMMENT '会议系统模式'","耗时":228,"影响行数":11}
{"level":"INFO","time":"2025-06-13T11:29:24.754+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `auto_power` bigint DEFAULT 0 COMMENT '是否启用自动开关机'","耗时":181,"影响行数":11}
{"level":"INFO","time":"2025-06-13T11:29:24.919+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `group_id` bigint COMMENT '分组id'","耗时":164,"影响行数":11}
{"level":"INFO","time":"2025-06-13T11:29:25.103+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `status` bigint DEFAULT 1 COMMENT '设备状态'","耗时":182,"影响行数":11}
{"level":"ERROR","time":"2025-06-13T11:29:25.131+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'","耗时":27,"error":"Error 1067 (42000): Invalid default value for 'created_at'"}
{"level":"FATAL","time":"2025-06-13T11:29:25.131+0800","caller":"eyc3_meeting/main.go:46","msg":"数据库迁移失败","error":"Error 1067 (42000): Invalid default value for 'created_at'"}
{"level":"INFO","time":"2025-06-13T11:29:43.760+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:43.935+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":153,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:44.061+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:44.095+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":34,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:44.225+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:44.378+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":152,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:44.578+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:44.658+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:44.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":178,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:44.995+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":158,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:29:45.079+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:45.240+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":161,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:45.416+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":176,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:45.461+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":44,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:45.576+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:45.713+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":136,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:45.789+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":75,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:45.905+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:46.013+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":107,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:29:46.085+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:46.188+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":103,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:46.326+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:46.425+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:46.581+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":156,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:46.719+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:46.765+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":45,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:46.936+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":170,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:47.121+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":184,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:47.184+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:47.318+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":133,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:47.506+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":187,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:47.507+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T11:29:47.618+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:47.776+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:47.925+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 'eyc_meeting_device_group' AND table_type = 'BASE TABLE'","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:47.981+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:48.108+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":126,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:48.233+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` LIMIT 1","耗时":125,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:48.405+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 'eyc_meeting_device_group' ORDER BY ORDINAL_POSITION","耗时":170,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:48.478+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":72,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:48.604+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":126,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:48.721+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 'eyc_meeting_device' AND table_type = 'BASE TABLE'","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:48.803+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":81,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:48.901+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":97,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:29:49.044+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` LIMIT 1","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:29:49.201+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 'eyc_meeting_device' ORDER BY ORDINAL_POSITION","耗时":156,"影响行数":-1}
{"level":"ERROR","time":"2025-06-13T11:29:49.281+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'","耗时":79,"error":"Error 1067 (42000): Invalid default value for 'created_at'"}
{"level":"FATAL","time":"2025-06-13T11:29:49.281+0800","caller":"eyc3_meeting/main.go:46","msg":"数据库迁移失败","error":"Error 1067 (42000): Invalid default value for 'created_at'"}
{"level":"INFO","time":"2025-06-13T11:31:54.906+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:55.119+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:31:55.306+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":187,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:55.347+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":40,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:55.468+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":121,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:31:55.616+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":147,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:55.738+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":121,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:55.805+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:55.922+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":116,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:31:56.058+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":136,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:31:56.098+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":40,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:56.239+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:31:56.382+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":143,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:56.481+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:56.618+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:31:56.736+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:56.811+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:56.948+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:31:57.077+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":129,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:31:57.165+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:57.301+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:31:57.486+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":184,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:57.530+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":44,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:57.658+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:31:57.787+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":127,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:57.853+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:57.956+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":102,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:31:58.113+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":156,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:58.177+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:58.313+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:31:58.436+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:58.437+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T11:31:58.508+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:58.592+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":83,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:31:58.682+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 'eyc_meeting_device_group' AND table_type = 'BASE TABLE'","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:58.734+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":50,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:58.883+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":148,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:31:59.018+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` LIMIT 1","耗时":135,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:59.179+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 'eyc_meeting_device_group' ORDER BY ORDINAL_POSITION","耗时":160,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:59.236+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":55,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:59.323+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":86,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:31:59.430+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 'eyc_meeting_device' AND table_type = 'BASE TABLE'","耗时":107,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:59.483+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":51,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:59.614+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:31:59.719+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` LIMIT 1","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:31:59.819+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 'eyc_meeting_device' ORDER BY ORDINAL_POSITION","耗时":99,"影响行数":-1}
{"level":"ERROR","time":"2025-06-13T11:31:59.886+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"ALTER TABLE `eyc_meeting_device` MODIFY COLUMN `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'","耗时":64,"error":"Error 1067 (42000): Invalid default value for 'created_at'"}
{"level":"FATAL","time":"2025-06-13T11:31:59.886+0800","caller":"eyc3_meeting/main.go:46","msg":"数据库迁移失败","error":"Error 1067 (42000): Invalid default value for 'created_at'"}
{"level":"INFO","time":"2025-06-13T11:33:03.610+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":45,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:03.764+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:33:03.879+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:03.932+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":52,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:04.043+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:33:04.140+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:04.256+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:04.324+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:04.438+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":113,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:33:04.548+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":110,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:33:04.583+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":34,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:04.729+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:33:04.833+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":103,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:04.892+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:05.008+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:33:05.129+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:05.187+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:05.273+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":86,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:33:05.447+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":174,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:33:05.499+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":51,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:05.635+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:33:05.739+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":102,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:05.807+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:05.929+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":121,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:33:06.059+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:06.109+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":48,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:06.224+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:33:06.342+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:06.427+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:06.527+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:33:06.654+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:33:06.654+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T11:33:06.813+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T11:33:06.816+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T11:33:06.816+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T11:33:06.818+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T11:33:12.244+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌006' OR sn = '测试门牌006')","耗时":124,"影响行数":1}
{"level":"ERROR","time":"2025-06-13T11:33:12.244+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称已被用作现有设备的名称或SN号"}
{"level":"INFO","time":"2025-06-13T11:34:40.070+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌007' OR sn = '测试门牌007')","耗时":177,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:34:40.237+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = 'SN00123456788' OR sn = 'SN00123456788')","耗时":166,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:34:40.474+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌007','SN00123456788','D3','door',90,80,1,'#1678FF','#1678FF','#F7885C',0,'split_screen',1,'08:00:00','22:00:00',1,1,0,'2025-06-13 11:34:40.302','2025-06-13 11:34:40.302')","耗时":171,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:34:40.577+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:34:40.687+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,7,8,9,10,11,12,13,18',`updated_at`='2025-06-13 11:34:40.578' WHERE `id` = 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:35:26.226+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T11:35:26.226+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T11:35:26.226+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T11:35:30.795+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:30.921+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:35:31.070+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":148,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:31.125+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:31.222+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":97,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:35:31.347+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:31.503+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":154,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:31.561+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:31.677+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":116,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:35:31.805+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":127,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:35:31.860+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:31.972+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":111,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:35:32.096+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":123,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:32.190+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:32.342+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":152,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:35:32.462+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:32.524+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:32.657+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:35:32.838+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":180,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:35:32.890+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":51,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:33.026+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:35:33.142+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:33.192+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:33.298+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":105,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:35:33.397+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:33.458+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:33.547+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":89,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:35:33.625+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:33.665+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":39,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:33.793+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":128,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:35:33.917+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":123,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:35:33.917+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T11:35:34.065+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T11:35:34.067+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T11:35:34.067+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T11:35:34.068+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T11:36:03.423+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌007' OR sn = '测试门牌007')","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:36:03.524+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = 'SN00123456788' OR sn = 'SN00123456788')","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:36:03.686+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌007','SN00123456788','D3','0',90,80,1,'#1678FF','#1678FF','#F7885C',0,'split_screen',1,'08:00:00','22:00:00',1,1,0,'2025-06-13 11:36:03.572','2025-06-13 11:36:03.572')","耗时":113,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:36:03.850+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:36:03.952+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,7,8,9,10,11,12,13,18,19',`updated_at`='2025-06-13 11:36:03.851' WHERE `id` = 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:39:44.084+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T11:39:44.084+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T11:39:44.085+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T11:39:49.321+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:49.481+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":147,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:39:49.677+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":195,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:49.765+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":88,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:49.907+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":141,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:39:50.018+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:50.116+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:50.176+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:50.287+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":111,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:39:50.415+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":127,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:39:50.487+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:50.646+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:39:50.839+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":193,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:50.916+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:51.095+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":178,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:39:51.220+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:51.281+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":60,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:51.375+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":93,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:39:51.532+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":157,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:39:51.634+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":101,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:51.802+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":167,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:39:51.989+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":186,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:52.057+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:52.232+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":174,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:39:52.430+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":197,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:52.523+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:52.713+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":190,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:39:52.897+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":184,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:52.972+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:53.132+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":160,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:39:53.226+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:39:53.227+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T11:39:53.383+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T11:39:53.384+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T11:39:53.384+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T11:39:53.385+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T11:40:06.228+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌004' OR sn = '测试门牌004')","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:40:06.363+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = 'SN00123456784' OR sn = 'SN00123456784')","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:40:06.538+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌004','SN00123456784','D3','0',90,80,1,'#1678FF','#1678FF','#F7885C',0,'2',1,'08:00:00','22:00:00',1,1,0,'2025-06-13 11:40:06.438','2025-06-13 11:40:06.438')","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:40:06.683+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:40:06.816+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,7,8,9,10,11,12,13,18,19,20',`updated_at`='2025-06-13 11:40:06.684' WHERE `id` = 1","耗时":132,"影响行数":1}
{"level":"ERROR","time":"2025-06-13T11:41:24.324+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 20 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":82,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T11:41:24.324+0800","caller":"controller/base.go:466","msg":"修改记录失败","error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T11:42:35.736+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T11:42:35.736+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T11:42:35.736+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T11:42:40.983+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:41.111+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":108,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:42:41.244+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":132,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:41.315+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:41.438+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":123,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:42:41.549+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:41.671+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":121,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:41.738+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:41.831+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":92,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:42:41.948+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":117,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:42:42.009+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":60,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:42.104+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":94,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:42:42.219+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":114,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:42.282+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:42.438+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":155,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:42:42.524+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:42.598+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:42.761+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:42:42.920+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":158,"影响行数":3}
{"level":"INFO","time":"2025-06-13T11:42:43.005+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:43.166+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":160,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:42:43.330+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:43.414+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:43.498+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":83,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:42:43.613+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:43.681+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:43.809+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":128,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:42:43.951+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:43.993+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":41,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:44.189+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":196,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:42:44.370+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":180,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T11:42:44.370+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T11:42:44.473+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T11:42:44.474+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T11:42:44.474+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T11:42:44.476+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T11:43:40.394+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 20 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":139,"影响行数":1}
{"level":"ERROR","time":"2025-06-13T11:43:40.395+0800","caller":"controller/base.go:466","msg":"修改记录失败","error":"设备名称不能为空"}
{"level":"INFO","time":"2025-06-13T11:43:55.971+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 20 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:43:56.107+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND id != 20 AND (title = '测试门牌001' OR sn = '测试门牌001')","耗时":135,"影响行数":1}
{"level":"ERROR","time":"2025-06-13T11:43:56.107+0800","caller":"controller/base.go:466","msg":"修改记录失败","error":"设备名称已被用作现有设备的名称或SN号"}
{"level":"INFO","time":"2025-06-13T11:44:17.708+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 20 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":113,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:44:17.820+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND id != 20 AND (title = '测试门牌004' OR sn = '测试门牌004')","耗时":111,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:44:17.937+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND id != 20 AND (title = 'SN00123456784' OR sn = 'SN00123456784')","耗时":116,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:44:18.097+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device` SET `corp_id`='ding424e63f5c9ac81e1ffe93478753d9884',`title`='测试门牌004',`sn`='SN00123456784',`model`='D3',`device_type`='1',`light_brightness`=90,`volume`=80,`custom_template`=1,`template_free`='#1678FF',`template_idle`='#1678FF',`template_using`='#F7885C',`related_door`=0,`system_mode`='2',`auto_power`=1,`power_on_time`='08:00:00',`power_off_time`='21:00:00',`group_id`=1,`status`=0,`created_at`='2025-06-13 11:40:06',`updated_at`='2025-06-13 11:44:18.002',`template_id`=0 WHERE `id` = 20","耗时":94,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:44:45.547+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 20 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":92,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:44:45.673+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND id != 20 AND (title = '测试门牌004' OR sn = '测试门牌004')","耗时":126,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:44:45.833+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND id != 20 AND (title = 'SN00123456784' OR sn = 'SN00123456784')","耗时":160,"影响行数":1}
{"level":"INFO","time":"2025-06-13T11:44:46.002+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device` SET `corp_id`='ding424e63f5c9ac81e1ffe93478753d9884',`title`='测试门牌004',`sn`='SN00123456784',`model`='D3',`device_type`='1',`light_brightness`=90,`volume`=80,`custom_template`=1,`template_free`='#1678FF',`template_idle`='#1678FF',`template_using`='#F7885C',`related_door`=0,`system_mode`='2',`auto_power`=1,`power_on_time`='08:00:00',`power_off_time`='20:00:00',`group_id`=1,`status`=0,`created_at`='2025-06-13 11:40:06',`updated_at`='2025-06-13 11:44:45.884',`template_id`=0 WHERE `id` = 20","耗时":118,"影响行数":1}
{"level":"ERROR","time":"2025-06-13T11:45:19.258+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 5 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":44,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T11:45:19.258+0800","caller":"controller/base.go:494","msg":"删除记录失败","id":5,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T11:45:19.258+0800","caller":"response/response.go:130","msg":"服务器内部错误","error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T13:44:05.814+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T13:44:05.824+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T13:44:05.824+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T13:44:10.718+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:10.974+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":243,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:44:11.130+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":155,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:11.203+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:11.334+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:44:11.549+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":214,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:11.689+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:11.804+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:11.962+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:44:12.153+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":191,"影响行数":3}
{"level":"INFO","time":"2025-06-13T13:44:12.249+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:12.409+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":160,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:44:12.618+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":209,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:12.729+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:12.899+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":170,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:44:13.093+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":193,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:13.174+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:13.310+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:44:13.523+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":212,"影响行数":3}
{"level":"INFO","time":"2025-06-13T13:44:13.597+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:13.782+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":184,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:44:13.899+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:13.947+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":47,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:14.082+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:44:14.249+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":167,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:14.352+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":102,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:14.510+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:44:14.719+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":208,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:14.813+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:14.989+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":176,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:44:15.118+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:44:15.119+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T13:44:15.254+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T13:44:15.255+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T13:44:15.255+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T13:44:15.256+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T13:48:14.101+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T13:48:14.101+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T13:48:14.101+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T13:48:17.297+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:17.420+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:17.552+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:17.590+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":37,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:17.698+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":108,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:17.802+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":103,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:17.883+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:17.921+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":37,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:18.030+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":108,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:18.140+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":109,"影响行数":3}
{"level":"INFO","time":"2025-06-13T13:48:18.223+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:18.380+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":155,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:18.499+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:18.570+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:18.699+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:18.825+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:18.892+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:19.139+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":247,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:19.267+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":128,"影响行数":3}
{"level":"INFO","time":"2025-06-13T13:48:19.341+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:19.513+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":172,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:19.644+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":131,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:19.707+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:19.863+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":155,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:20.023+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:20.117+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:20.300+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":182,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:20.427+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:20.492+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:20.603+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":111,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:20.898+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":294,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:48:20.898+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T13:48:21.002+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T13:48:21.004+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T13:48:21.004+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T13:48:21.005+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T13:48:29.152+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 5 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":102,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:29.259+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 5","耗时":107,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:35.729+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 6 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":323,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:35.901+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 6","耗时":171,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:41.109+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 7 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":141,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:41.240+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:41.398+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,8,9,10,11,12,13,18,19,20',`updated_at`='2025-06-13 13:48:41.241' WHERE `id` = 1","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:41.529+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 7","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:47.050+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 8 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:47.209+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:47.340+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,9,10,11,12,13,18,19,20',`updated_at`='2025-06-13 13:48:47.21' WHERE `id` = 1","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:47.447+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 8","耗时":107,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:53.192+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 9 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":137,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:53.340+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":147,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:53.505+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,10,11,12,13,18,19,20',`updated_at`='2025-06-13 13:48:53.34' WHERE `id` = 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:53.648+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 9","耗时":142,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:58.678+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 10 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":113,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:58.793+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:58.955+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,11,12,13,18,19,20',`updated_at`='2025-06-13 13:48:58.794' WHERE `id` = 1","耗时":161,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:48:59.102+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 10","耗时":147,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:49:04.519+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 11 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":123,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:49:04.658+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:49:04.824+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,12,13,18,19,20',`updated_at`='2025-06-13 13:49:04.658' WHERE `id` = 1","耗时":166,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:49:04.969+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 11","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:49:10.540+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 12 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":150,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:49:10.645+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":105,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:49:10.821+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,13,18,19,20',`updated_at`='2025-06-13 13:49:10.646' WHERE `id` = 1","耗时":175,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:49:10.994+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 12","耗时":173,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:49:16.515+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 13 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:49:16.646+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:49:16.816+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,18,19,20',`updated_at`='2025-06-13 13:49:16.647' WHERE `id` = 1","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:49:16.941+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 13","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:49:29.291+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE id = 14 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":133,"影响行数":0}
{"level":"ERROR","time":"2025-06-13T13:49:29.353+0800","caller":"controller/base.go:494","msg":"删除记录失败","id":14,"error":"设备不存在或无权访问"}
{"level":"ERROR","time":"2025-06-13T13:49:29.353+0800","caller":"response/response.go:130","msg":"服务器内部错误","error":"设备不存在或无权访问"}
{"level":"INFO","time":"2025-06-13T13:50:40.767+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌008' OR sn = '测试门牌008')","耗时":109,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:50:40.891+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = 'SN00123456781' OR sn = 'SN00123456781')","耗时":123,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:50:41.064+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌008','SN00123456781','D3','0',90,80,1,'#1678FF','#1678FF','#F7885C',0,'2',1,'08:00:00','22:00:00',1,1,0,'2025-06-13 13:50:40.965','2025-06-13 13:50:40.965')","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:50:41.167+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:50:41.291+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,18,19,20,21',`updated_at`='2025-06-13 13:50:41.167' WHERE `id` = 1","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:53:19.299+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T13:53:19.300+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T13:53:19.301+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T13:53:24.123+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":50,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:24.295+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:53:24.431+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":135,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:24.494+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:24.598+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":103,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:53:24.727+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:24.849+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:24.944+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:25.080+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:53:25.266+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":185,"影响行数":3}
{"level":"INFO","time":"2025-06-13T13:53:25.309+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":43,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:25.425+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":116,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:53:25.514+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":88,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:25.594+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:25.730+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:53:25.858+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":127,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:25.939+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:26.094+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":155,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:53:26.235+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":140,"影响行数":3}
{"level":"INFO","time":"2025-06-13T13:53:26.305+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:26.408+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:53:26.518+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:26.583+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:26.734+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":150,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:53:26.945+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":210,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:27.044+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:27.174+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:53:27.305+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:27.345+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":40,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:27.543+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":196,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:53:27.707+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":164,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:53:27.708+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T13:53:27.930+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T13:53:27.931+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T13:53:27.931+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T13:53:27.932+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T13:53:54.598+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌003' OR sn = '测试门牌003')","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:53:54.707+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = 'SN00123456783' OR sn = 'SN00123456783')","耗时":109,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:53:55.072+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`group_id`,`status`,`template_id`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌003','SN00123456783','D3','0',90,80,1,'#1678FF','#1678FF','#F7885C',0,'1',1,'08:00:00','22:00:00',1,1,0,'2025-06-13 13:53:54.766','2025-06-13 13:53:54.766')","耗时":305,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:53:55.141+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":69,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:53:55.242+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='4,18,19,20,21,22',`updated_at`='2025-06-13 13:53:55.142' WHERE `id` = 1","耗时":100,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:54:26.660+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":97,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:54:33.999+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 19 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":80,"影响行数":1}
{"level":"ERROR","time":"2025-06-13T13:55:14.001+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":53,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T13:55:14.001+0800","caller":"controller/base.go:297","msg":"获取列表失败","error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T13:55:14.002+0800","caller":"response/response.go:130","msg":"服务器内部错误","error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T13:55:20.125+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":66,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T13:55:20.125+0800","caller":"controller/base.go:297","msg":"获取列表失败","error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T13:55:20.125+0800","caller":"response/response.go:130","msg":"服务器内部错误","error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T13:55:23.512+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":45,"error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T13:55:23.512+0800","caller":"controller/base.go:297","msg":"获取列表失败","error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T13:55:23.512+0800","caller":"response/response.go:130","msg":"服务器内部错误","error":"Error 1054 (42S22): Unknown column 'corpid' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T13:56:00.842+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 19 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:57:10.897+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T13:57:10.898+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T13:57:10.899+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T13:57:14.322+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":55,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:14.520+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":185,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:57:14.700+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":178,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:14.747+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":47,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:14.915+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":167,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:57:15.075+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:15.172+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:15.272+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:15.400+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:57:15.534+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":133,"影响行数":3}
{"level":"INFO","time":"2025-06-13T13:57:15.603+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:15.718+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:57:15.862+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":143,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:15.909+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:16.039+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":128,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:57:16.150+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":111,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:16.214+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:16.348+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:57:16.507+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":159,"影响行数":3}
{"level":"INFO","time":"2025-06-13T13:57:16.582+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:16.734+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":152,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:57:16.842+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":107,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:16.949+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:17.149+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:57:17.327+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":178,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:17.403+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":75,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:17.589+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":186,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:57:17.769+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":179,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:17.864+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:18.050+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":186,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:57:18.230+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":180,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T13:57:18.230+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T13:57:18.497+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T13:57:18.498+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T13:57:18.498+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T13:57:18.499+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T13:57:30.505+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:57:30.690+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY id DESC LIMIT 10","耗时":183,"影响行数":6}
{"level":"INFO","time":"2025-06-13T13:58:02.563+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title LIKE '%3%'","耗时":181,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:58:02.731+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title LIKE '%3%' ORDER BY id DESC LIMIT 10","耗时":168,"影响行数":1}
{"level":"INFO","time":"2025-06-13T13:58:23.683+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":183,"影响行数":2}
{"level":"INFO","time":"2025-06-13T14:18:32.144+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T14:18:32.145+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T14:18:32.145+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T14:18:35.503+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":43,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:35.634+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":118,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:18:35.766+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":132,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:35.847+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:35.975+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:18:36.174+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":198,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:36.375+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":200,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:36.487+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:36.694+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":207,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:18:36.832+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":137,"影响行数":3}
{"level":"INFO","time":"2025-06-13T14:18:36.920+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":88,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:37.055+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:18:37.184+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:37.293+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":108,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:37.433+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":139,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:18:37.575+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:37.666+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":91,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:37.804+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:18:37.995+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":191,"影响行数":3}
{"level":"INFO","time":"2025-06-13T14:18:38.037+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":41,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:38.221+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":184,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:18:38.383+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":160,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:38.479+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:38.679+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:18:38.883+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":204,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:38.943+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:39.129+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":186,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:18:39.292+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:39.346+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:39.469+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":122,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:18:39.577+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":107,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:18:39.577+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T14:18:39.673+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T14:18:39.674+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T14:18:39.674+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T14:18:39.674+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T14:23:21.567+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T14:23:21.569+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T14:23:21.569+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T14:23:24.805+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:24.947+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:23:25.035+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:25.100+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:25.268+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":166,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:23:25.446+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":178,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:25.588+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:25.667+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:25.820+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":153,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:23:25.942+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":121,"影响行数":3}
{"level":"INFO","time":"2025-06-13T14:23:26.039+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:26.229+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":190,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:23:26.479+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":249,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:26.540+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:26.678+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":137,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:23:26.811+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":132,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:26.868+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:26.998+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:23:27.130+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":132,"影响行数":3}
{"level":"INFO","time":"2025-06-13T14:23:27.188+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:27.387+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:23:27.499+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":111,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:27.568+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:27.803+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":233,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:23:27.999+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":194,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:28.090+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:28.255+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":165,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:23:28.397+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:28.460+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:28.608+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":148,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:23:28.749+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:23:28.749+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T14:23:28.867+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T14:23:28.869+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T14:23:28.875+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T14:23:28.880+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T14:28:08.571+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:28:08.835+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device` SET `group_id`=0,`updated_at`='2025-06-13 14:28:08.686' WHERE id IN (4,18,19,20,21,22)","耗时":149,"影响行数":5}
{"level":"INFO","time":"2025-06-13T14:28:08.968+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:28:14.931+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 2 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":115,"影响行数":0}
{"level":"ERROR","time":"2025-06-13T14:28:14.931+0800","caller":"controller/base.go:494","msg":"删除记录失败","id":2,"error":"设备分组不存在"}
{"level":"ERROR","time":"2025-06-13T14:28:14.931+0800","caller":"response/response.go:130","msg":"服务器内部错误","error":"设备分组不存在"}
{"level":"INFO","time":"2025-06-13T14:28:21.363+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 3 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":150,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:28:21.626+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 3","耗时":181,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:28:29.005+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 4 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":216,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:28:29.315+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 4","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:28:35.315+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 5 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:28:35.520+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device` SET `group_id`=0,`updated_at`='2025-06-13 14:28:35.366' WHERE id IN (1)","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:28:35.653+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 5","耗时":133,"影响行数":1}
{"level":"ERROR","time":"2025-06-13T14:29:47.007+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_name = 'D3组'","耗时":89,"error":"Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T14:29:47.079+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"检查分组名称唯一性失败: Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T14:36:16.601+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T14:36:16.602+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T14:36:16.603+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T14:36:18.804+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":91,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:18.964+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":147,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:36:19.077+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":113,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:19.133+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":55,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:19.269+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:36:19.362+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:19.459+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:19.503+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":42,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:19.622+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":119,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:36:19.750+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":128,"影响行数":3}
{"level":"INFO","time":"2025-06-13T14:36:19.817+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:19.919+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:36:20.047+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:20.115+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:20.227+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":111,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:36:20.372+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:20.521+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:20.671+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":148,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:36:20.797+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":124,"影响行数":3}
{"level":"INFO","time":"2025-06-13T14:36:20.862+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:20.955+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":92,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:36:21.087+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":132,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:21.161+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":72,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:21.269+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":108,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:36:21.354+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:21.400+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":45,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:21.512+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":112,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:36:21.639+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:21.716+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:21.847+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:36:21.940+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T14:36:21.940+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T14:36:22.057+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T14:36:22.057+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T14:36:22.057+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T14:36:22.058+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T14:36:39.739+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_name = 'D3组'","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:36:39.884+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device_group` (`corp_id`,`group_name`,`device_type`,`device_ids`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','D3组','0','1,4','2025-06-13 14:36:39.739','2025-06-13 14:36:39.739')","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:36:40.063+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE id IN (1,4) AND group_id != 0","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:36:40.172+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device` SET `group_id`=6,`updated_at`='2025-06-13 14:36:40.064' WHERE id IN (1,4)","耗时":108,"影响行数":2}
{"level":"INFO","time":"2025-06-13T14:37:25.690+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_name = 'D3组'","耗时":155,"影响行数":1}
{"level":"ERROR","time":"2025-06-13T14:37:25.796+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"分组名称已存在"}
{"level":"INFO","time":"2025-06-13T14:37:48.764+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_name = 'D4组'","耗时":107,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:37:48.905+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device_group` (`corp_id`,`group_name`,`device_type`,`device_ids`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','D4组','1','20','2025-06-13 14:37:48.764','2025-06-13 14:37:48.764')","耗时":141,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:37:49.007+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE id IN (20) AND group_id != 0","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:37:49.132+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device` SET `group_id`=7,`updated_at`='2025-06-13 14:37:49.007' WHERE id IN (20)","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:39:28.362+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 6 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:39:38.246+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 6 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:42:09.161+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 6 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":195,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:42:09.436+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_name = 'D3组更新' AND id != 6","耗时":170,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:42:09.604+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='1,4',`device_type`='1',`group_name`='D3组更新',`updated_at`='2025-06-13 14:42:09.436' WHERE id = 6","耗时":167,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:42:09.740+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device` SET `group_id`=0,`updated_at`='2025-06-13 14:42:09.604' WHERE group_id = 6","耗时":136,"影响行数":2}
{"level":"INFO","time":"2025-06-13T14:42:09.902+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE id IN (1,4) AND group_id != 0","耗时":161,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:42:10.009+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device` SET `group_id`=6,`updated_at`='2025-06-13 14:42:09.903' WHERE id IN (1,4)","耗时":106,"影响行数":2}
{"level":"INFO","time":"2025-06-13T14:42:28.775+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 6 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:42:29.034+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_name = 'D3组更新' AND id != 6","耗时":192,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:42:29.254+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='1,4',`device_type`='0',`group_name`='D3组更新',`updated_at`='2025-06-13 14:42:29.034' WHERE id = 6","耗时":220,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:42:29.387+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device` SET `group_id`=0,`updated_at`='2025-06-13 14:42:29.255' WHERE group_id = 6","耗时":133,"影响行数":2}
{"level":"INFO","time":"2025-06-13T14:42:29.522+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE id IN (1,4) AND group_id != 0","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:42:29.660+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device` SET `group_id`=6,`updated_at`='2025-06-13 14:42:29.523' WHERE id IN (1,4)","耗时":137,"影响行数":2}
{"level":"INFO","time":"2025-06-13T14:42:40.334+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE `eyc_meeting_device_group`.`id` = 6 ORDER BY `eyc_meeting_device_group`.`id` LIMIT 1","耗时":113,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:42:40.524+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_name = 'D3组更新' AND id != 6","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:42:40.610+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device_group` SET `device_ids`='1,4',`device_type`='0',`group_name`='D3组更新',`updated_at`='2025-06-13 14:42:40.524' WHERE id = 6","耗时":85,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:42:40.717+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device` SET `group_id`=0,`updated_at`='2025-06-13 14:42:40.61' WHERE group_id = 6","耗时":107,"影响行数":2}
{"level":"INFO","time":"2025-06-13T14:42:40.802+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE id IN (1,4) AND group_id != 0","耗时":84,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:42:40.891+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_device` SET `group_id`=6,`updated_at`='2025-06-13 14:42:40.803' WHERE id IN (1,4)","耗时":88,"影响行数":2}
{"level":"INFO","time":"2025-06-13T14:42:56.487+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND device_type = '0'","耗时":167,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:42:56.616+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND device_type = '0' ORDER BY id DESC LIMIT 10","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:43:05.753+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-06-13T14:43:05.867+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY id DESC LIMIT 10","耗时":113,"影响行数":2}
{"level":"INFO","time":"2025-06-13T15:06:56.632+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T15:06:56.632+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T15:06:56.633+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T15:08:01.949+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:02.152+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":188,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:08:02.280+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:02.329+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":48,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:02.429+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:08:02.508+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:02.601+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:02.638+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":36,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:02.762+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:08:02.896+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":134,"影响行数":3}
{"level":"INFO","time":"2025-06-13T15:08:02.951+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:03.036+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":84,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:08:03.157+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":121,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:03.236+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:03.352+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":116,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:08:03.456+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":103,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:03.501+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":44,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:03.610+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":108,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:08:03.712+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":102,"影响行数":3}
{"level":"INFO","time":"2025-06-13T15:08:03.762+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:03.870+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":108,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:08:03.966+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:04.036+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:04.139+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":103,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:08:04.239+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:04.293+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:04.426+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:08:04.529+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":102,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:04.592+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:04.692+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:08:04.799+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":105,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:08:04.799+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T15:08:04.919+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T15:08:04.921+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T15:08:04.921+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T15:08:04.922+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T15:09:08.047+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T15:09:08.048+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T15:09:08.048+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T15:09:11.217+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:11.357+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:11.594+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":236,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:11.650+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:11.796+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:11.913+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:12.075+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":161,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:12.108+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":32,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:12.238+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:12.373+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":134,"影响行数":3}
{"level":"INFO","time":"2025-06-13T15:09:12.473+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:12.615+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":142,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:12.757+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:12.827+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:12.953+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:13.087+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:13.151+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:13.325+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":173,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:13.466+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":141,"影响行数":3}
{"level":"INFO","time":"2025-06-13T15:09:13.537+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:13.646+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":109,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:13.758+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":111,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:13.797+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":38,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:13.937+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:14.053+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:14.120+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:14.262+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":142,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:14.362+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:14.411+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:14.507+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":95,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:14.666+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:14.666+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T15:09:14.751+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T15:09:14.753+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T15:09:14.753+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T15:09:14.754+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T15:09:25.492+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T15:09:25.493+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T15:09:25.493+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-13T15:09:27.171+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:27.285+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":100,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:27.392+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":105,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:27.458+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:27.581+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":122,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:27.690+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":108,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:27.820+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:27.883+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:28.048+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:28.213+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":165,"影响行数":3}
{"level":"INFO","time":"2025-06-13T15:09:28.275+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:28.413+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:28.541+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:28.606+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:28.714+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":108,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:28.874+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:28.933+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:29.113+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:29.197+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":83,"影响行数":3}
{"level":"INFO","time":"2025-06-13T15:09:29.256+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:29.358+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":102,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:29.461+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":103,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:29.537+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":75,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:29.699+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":161,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:29.848+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":148,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:29.893+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":43,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:30.028+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:30.210+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":182,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:30.289+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:30.443+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:30.591+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":147,"影响行数":-1}
{"level":"INFO","time":"2025-06-13T15:09:30.591+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-13T15:09:30.760+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-13T15:09:30.761+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-13T15:09:30.761+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-13T15:09:30.761+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-13T15:09:34.184+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_facility` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND name = '11'","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:34.354+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_facility` (`name`,`corpid`,`created_at`,`updated_at`) VALUES ('11','ding424e63f5c9ac81e1ffe93478753d9884','2025-06-13 15:09:34.184','2025-06-13 15:09:34.184')","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:54.746+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_facility` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND name = '12'","耗时":96,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:09:54.875+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_facility` (`name`,`corpid`,`created_at`,`updated_at`) VALUES ('12','ding424e63f5c9ac81e1ffe93478753d9884','2025-06-13 15:09:54.746','2025-06-13 15:09:54.746')","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:10:10.911+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_facility` WHERE `eyc_meeting_facility`.`id` = 7","耗时":238,"影响行数":1}
{"level":"ERROR","time":"2025-06-13T15:10:19.414+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT count(*) FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":72,"error":"Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T15:10:19.414+0800","caller":"controller/base.go:297","msg":"获取列表失败","error":"Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"ERROR","time":"2025-06-13T15:10:19.415+0800","caller":"response/response.go:130","msg":"服务器内部错误","error":"Error 1054 (42S22): Unknown column 'corp_id' in 'where clause'"}
{"level":"INFO","time":"2025-06-13T15:15:52.766+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:15:52.880+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' LIMIT 10","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:16:01.551+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":222,"影响行数":1}
{"level":"INFO","time":"2025-06-13T15:34:37.701+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-13T15:34:37.702+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-13T15:34:37.702+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
