package service

import (
	"errors"
	"eyc3_meeting/internal/model"
	"fmt"
	"time"

	"github.com/cloudwego/hertz/pkg/app"
	"gorm.io/gorm"
)

// EycMeetingFacilityService 会议设施服务
// 负责会议设施的增删改查及相关业务逻辑
type EycMeetingFacilityService struct {
	BaseServiceImpl[model.EycMeetingFacility]
}

// AddService 新增设施
func (s *EycMeetingFacilityService) AddService(ac *app.RequestContext, data interface{}) (uint, error) {
	m, ok := data.(map[string]interface{}) //类型断言
	if !ok {
		return 0, fmt.Errorf("参数类型错误")
	}

	facility := &model.EycMeetingFacility{}

	// 解析参数
	if name, ok := m["name"].(string); ok && name != "" {
		facility.Name = name
	} else {
		return 0, errors.New("设施名称不能为空")
	}

	// 从JWT上下文中获取企业ID
	corpIDValue, exists := ac.Get("corp_id")
	if !exists {
		return 0, errors.New("无法获取企业ID，请检查登录状态")
	}
	corpID, ok := corpIDValue.(string)
	if !ok || corpID == "" {
		return 0, errors.New("无效的企业ID")
	}
	facility.Corpid = corpID

	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查重名
	var count int64
	if err := tx.Model(&model.EycMeetingFacility{}).
		Where("corpid = ? AND name = ?", facility.Corpid, facility.Name).
		Count(&count).Error; err != nil {
		tx.Rollback()
		return 0, fmt.Errorf("检查设施名称唯一性失败: %w", err)
	}
	if count > 0 {
		tx.Rollback()
		return 0, errors.New("设施名称已存在")
	}

	// 设置时间
	now := time.Now()
	facility.CreatedAt = now
	facility.UpdatedAt = now

	// 创建记录
	if err := tx.Create(facility).Error; err != nil {
		tx.Rollback()
		return 0, err
	}

	if err := tx.Commit().Error; err != nil {
		return 0, err
	}

	return uint(facility.ID), nil
}

// ModifyService 修改设施
func (s *EycMeetingFacilityService) ModifyService(ac *app.RequestContext, id uint, data interface{}) error {
	m, ok := data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("参数类型错误")
	}

	// 检查记录是否存在
	var existingFacility model.EycMeetingFacility
	if err := model.DB.First(&existingFacility, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("设施不存在")
		}
		return err
	}

	// 解析参数
	if name, ok := m["name"].(string); ok && name != "" {
		existingFacility.Name = name
	} else {
		return errors.New("设施名称不能为空")
	}

	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查重名（排除自身）
	var count int64
	if err := tx.Model(&model.EycMeetingFacility{}).
		Where("corpid = ? AND name = ? AND id != ?", existingFacility.Corpid, existingFacility.Name, id).
		Count(&count).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("检查设施名称唯一性失败: %w", err)
	}
	if count > 0 {
		tx.Rollback()
		return errors.New("设施名称已存在")
	}

	// 更新记录
	existingFacility.UpdatedAt = time.Now()
	if err := tx.Save(&existingFacility).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// DeleteService 删除设施
func (s *EycMeetingFacilityService) DeleteService(ac *app.RequestContext, id uint) error {
	return s.BaseServiceImpl.DeleteService(ac, id)
}

// GetInfoService 获取单个设施详情
func (s *EycMeetingFacilityService) GetInfoService(ac *app.RequestContext, id uint) (model.EycMeetingFacility, bool, error) {
	return s.BaseServiceImpl.GetInfoService(ac, id)
}

// GetListService 获取设施列表
func (s *EycMeetingFacilityService) GetListService(ac *app.RequestContext, page, pageSize int, fields map[string]string, orderBy string) (model.PageResult[model.EycMeetingFacility], error) {
	return s.BaseServiceImpl.GetListService(ac, page, pageSize, fields, orderBy)
}

// GetAllService 查询全部设施
func (s *EycMeetingFacilityService) GetAllService(ac *app.RequestContext, fields map[string]string, orderBy string) ([]model.EycMeetingFacility, error) {
	return s.BaseServiceImpl.GetAllService(ac, fields, orderBy)
}
