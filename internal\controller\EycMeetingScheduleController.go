package controller

import (
	"context"
	"eyc3_meeting/internal/pkg/response"
	"eyc3_meeting/internal/service"

	"github.com/cloudwego/hertz/pkg/app"
)

var eycMeetingScheduleService = new(service.EycMeetingScheduleService)

// GetListTimeView 获取会议室列表时间视图
func GetListTimeView(c context.Context, ac *app.RequestContext) {
	var req service.ScheduleRequest
	if err := ac.BindAndValidate(&req); err != nil {
		response.BadRequest(ac, "参数错误: "+err.Error())
		return
	}

	if req.Date == "" {
		response.BadRequest(ac, "日期不能为空")
		return
	}

	data, err := eycMeetingScheduleService.GetListTimeView(ac, &req)
	if err != nil {
		response.Fail(ac, response.StatusBadRequest, err.Error())
		return
	}

	response.Success(ac, data)
}
