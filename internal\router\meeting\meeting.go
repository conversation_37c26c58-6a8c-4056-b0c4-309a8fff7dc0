package meeting

import (
	"eyc3_meeting/internal/controller"
	"eyc3_meeting/internal/middleware"

	"github.com/cloudwego/hertz/pkg/app/server"
)

// RegisterMeetingRoutes 注册会议相关接口
func RegisterMeetingRoutes(r *server.Hertz) {
	// 会议室分组相关接口
	meeting_group := r.Group("/group", middleware.JWT())
	meeting_group.POST("/post_add", controller.AddEycMeetingGroup)
	meeting_group.GET("/get_info", controller.GetEycMeetingGroupInfo)
	meeting_group.POST("/post_modify", controller.ModifyEycMeetingGroup)
	meeting_group.POST("/post_del", controller.DeleteEycMeetingGroup)
	meeting_group.POST("/get_ls", controller.GetEycMeetingGroupList)
	meeting_group.GET("/get_all", controller.GetAllEycMeetingGroups)

	// 会议室相关接口
	meeting_room := r.Group("/room", middleware.JWT())
	meeting_room.POST("/post_add", controller.AddEycMeetingRoom)
	meeting_room.POST("/post_modify", controller.ModifyEycMeetingRoom)
	meeting_room.POST("/post_del", controller.DeleteEycMeetingRoom)
	meeting_room.GET("/get_info", controller.GetEycMeetingRoomInfo)
	meeting_room.POST("/get_ls", controller.GetEycMeetingRoomList)
	meeting_room.GET("/get_all", controller.GetAllEycMeetingRooms)
	meeting_room.POST("/post_advanced_settings", controller.UpdateMeetingRoomAdvancedSettings)
	meeting_room.POST("/post_batch_sort", controller.UpdateMeetingRoomSort)
	meeting_room.POST("/post_update_sort", controller.MoveMeetingRoomSort)
	meeting_room.POST("/post_batch_add", controller.BatchAddEycMeetingRoomFromExcel)
	// meeting_room.GET("/get_batch_add_template", controller.DownloadBatchAddTemplate)//前端做的,后端不用管

	// 会议日程相关接口
	//r.GET("/schedule/day", controller.GetDayScheduleHandler)
	//r.GET("/schedule/week", controller.GetWeekScheduleHandler)
	//r.GET("/schedule/month", controller.GetMonthScheduleHandler)

	booking := r.Group("/booking", middleware.JWT())

	// 会议预订相关接口 - 只保留核心CRUD和基础功能
	booking.POST("/post_add", controller.AddEycMeetingBooking)
	booking.POST("/post_modify", controller.ModifyEycMeetingBooking)
	booking.POST("/cancel", controller.ModifyEycMeetingBooking)
	booking.GET("/get_info", controller.GetEycMeetingBookingInfo)
	booking.POST("/get_ls", controller.GetEycMeetingBookingList)
	booking.POST("/post_del", controller.DeleteEycMeetingBooking)

	// 快速预约相关接口
	r.POST("/booking/post_booking_ls", controller.GetAvailableRooms)
	r.POST("/booking/post_booking_quick", controller.QuickBookMeeting)

	// 注册设备管理相关接口
	RegisterDeviceRoutes(r)

	// 注册设施管理相关接口
	RegisterFacilityRoutes(r)

	// 注册日程视图相关接口
	schedule := r.Group("/schedule", middleware.JWT())
	schedule.POST("/get_list_view", controller.GetListTimeView)
}
