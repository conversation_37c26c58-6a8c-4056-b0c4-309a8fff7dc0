package model

import (
	"fmt"
	"time"

	"eyc3_meeting/config"
	"eyc3_meeting/internal/pkg/logger"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

var DB *gorm.DB

// Init 初始化数据库连接
func Init() {
	var err error

	// 数据库配置
	dbConfig := config.AppConfig.Database

	// 创建连接
	DB, err = gorm.Open(mysql.Open(dbConfig.DSN), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   "t_", // 表名前缀
			SingularTable: true, // 单数表名
		},
		Logger: NewGormLogger(),
	})

	if err != nil {
		logger.Error("数据库连接失败", logger.Error2(err))
		panic(fmt.Sprintf("数据库连接失败: %v", err))
	}

	// 配置连接池
	sqlDB, err := DB.DB()
	if err != nil {
		logger.Error("数据库连接池配置失败", logger.Error2(err))
		panic(fmt.Sprintf("数据库连接池配置失败: %v", err))
	}

	// 设置连接池
	sqlDB.SetMaxIdleConns(10)           // 最大空闲连接数
	sqlDB.SetMaxOpenConns(100)          // 最大打开连接数
	sqlDB.SetConnMaxLifetime(time.Hour) // 连接可复用的最大时间

	// 检查连接是否成功
	if err := sqlDB.Ping(); err != nil {
		logger.Error("数据库连接检查失败", logger.Error2(err))
		panic(fmt.Sprintf("数据库连接检查失败: %v", err))
	}

	// 自动迁移表结构
	autoMigrate()

	logger.Info("数据库初始化成功")
}

// 自动迁移表结构
func autoMigrate() {
	if err := DB.AutoMigrate(
		&User{},
		// 添加其他模型...
	); err != nil {
		logger.Error("数据库迁移失败", logger.Error2(err))
		panic(fmt.Sprintf("数据库迁移失败: %v", err))
	}
}

// Close 关闭数据库连接
func Close() {
	sqlDB, err := DB.DB()
	if err != nil {
		logger.Error("获取数据库连接失败", logger.Error2(err))
		return
	}

	if err := sqlDB.Close(); err != nil {
		logger.Error("关闭数据库连接失败", logger.Error2(err))
	} else {
		logger.Info("数据库连接已关闭")
	}
}
