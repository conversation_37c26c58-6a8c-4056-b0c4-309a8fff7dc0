package model

import (
	"eyc3_meeting/internal/pkg/logger"
	"time"
)

// TableNameEycMeetingGroup ...
const TableNameEycMeetingGroup = "eyc_meeting_groups"

// EycMeetingGroup 会议室分组表
// 会议分组信息，包括钉钉分组ID、企业ID、分组名称、会议室ID列表等
// RoomIDArray/Rooms/RoomCount 仅用于业务处理，不入库
type EycMeetingGroup struct {
	ID        int       `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Corpid    string    `gorm:"column:corp_id;not null;comment:企业ID" json:"corpid"` // 企业ID
	Title     string    `gorm:"column:title;not null;comment:分组名称" json:"title"`    // 分组名称
	DGroupID  int       `gorm:"column:d_group_id;comment:分组id(钉钉上的分组id)" json:"d_group_id"`
	CreatedAt time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP;comment:添加时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;comment:最后修改时间" json:"updated_at"`
	Sort      int       `gorm:"column:sort;comment:排序" json:"sort"`
	Lock      int       `gorm:"column:lock;comment:锁定" json:"lock"`
	Type      int       `gorm:"column:type;comment:类型" json:"type"`
	Status    int       `gorm:"column:status;comment:状态" json:"status"`
	RoomIds   string    `gorm:"column:room_id;comment:会议室id列表" json:"room_id"` // 注意，数据库列名为 room_id

	// 仅用于内部处理和API响应，不存入数据库
	ParentId    int              `gorm:"-" json:"-"`               // 该字段在数据库中不存在,仅用于程序逻辑
	RoomIDArray []int            `gorm:"-" json:"-"`               // 解析后的会议室ID数组(内部使用)
	Rooms       []EycMeetingRoom `gorm:"-" json:"rooms,omitempty"` // 关联的会议室信息
	RoomCount   int              `gorm:"-" json:"room_count"`      // 会议室数量
}

// TableName 返回表名
func (EycMeetingGroup) TableName() string {
	return "eyc_meeting_groups"
}

// IsGroupTitleExists 检查同企业下分组名称是否已存在
func IsGroupTitleExists(corpid string, title string) (bool, error) {
	var count int64
	err := DB.Model(&EycMeetingGroup{}).
		Where("corp_id = ? AND title = ?", corpid, title).
		Count(&count).Error

	if err != nil {
		logger.Error("检查分组名称是否存在时出错",
			logger.String("corpid", corpid),
			logger.String("title", title),
			logger.Error2(err))
		return false, err
	}

	return count > 0, nil
}
