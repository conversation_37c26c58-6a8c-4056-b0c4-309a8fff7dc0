package cron

import (
	"time"

	"eyc3_meeting/internal/pkg/logger"

	"github.com/robfig/cron/v3"
)

var cronScheduler *cron.Cron

// Init 初始化定时任务
func Init() {
	// 创建带秒级别精度的cron实例
	cronScheduler = cron.New(cron.WithSeconds())

	// 添加任务
	addJobs()

	// 启动定时任务
	cronScheduler.Start()
	logger.Info("定时任务已启动")
}

// 添加定时任务
func addJobs() {
	// 每分钟执行一次
	_, err := cronScheduler.AddFunc("0 * * * * *", func() {
		minuteJob()
	})
	if err != nil {
		logger.Error("添加分钟级任务失败", logger.Error2(err))
	}

	// 每小时执行一次
	_, err = cronScheduler.AddFunc("0 0 * * * *", func() {
		hourlyJob()
	})
	if err != nil {
		logger.Error("添加小时级任务失败", logger.Error2(err))
	}

	// 每天凌晨1点执行
	_, err = cronScheduler.AddFunc("0 0 1 * * *", func() {
		dailyJob()
	})
	if err != nil {
		logger.Error("添加每日任务失败", logger.Error2(err))
	}
}

// 分钟级任务
func minuteJob() {
	now := time.Now().Format("15:04:05")
	logger.Info("执行分钟级任务", logger.String("time", now))
	// 执行业务逻辑
}

// 小时级任务
func hourlyJob() {
	now := time.Now().Format("15:04:05")
	logger.Info("执行小时级任务", logger.String("time", now))
	// 执行业务逻辑
}

// 每日任务
func dailyJob() {
	date := time.Now().Format("2006-01-02")
	logger.Info("执行每日任务", logger.String("date", date))
	// 执行业务逻辑
}

// Stop 停止定时任务
func Stop() {
	if cronScheduler != nil {
		cronScheduler.Stop()
		logger.Info("定时任务已停止")
	}
}
