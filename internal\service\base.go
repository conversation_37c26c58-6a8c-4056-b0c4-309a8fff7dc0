package service

import (
	"eyc3_meeting/internal/model"
	"fmt"
	"github.com/cloudwego/hertz/pkg/app"
)

// BaseService 基础服务接口，定义通用的CRUD操作
type BaseService[T any] interface {
	// GetListService 获取分页列表
	GetListService(ac *app.RequestContext, page, pageSize int, fields map[string]string, orderBy string) (model.PageResult[T], error)

	// GetAllService 获取所有记录
	GetAllService(ac *app.RequestContext, fields map[string]string, orderBy string) ([]T, error)

	// GetInfoService 获取单个记录
	GetInfoService(ac *app.RequestContext, id uint) (T, bool, error)

	// AddService 添加记录，支持结构体指针或map
	AddService(ac *app.RequestContext, data interface{}) (uint, error)

	// ModifyService 修改记录
	ModifyService(ac *app.RequestContext, id uint, data interface{}) error

	// DeleteService 删除记录
	DeleteService(ac *app.RequestContext, id uint) error
}

// BaseServiceImpl 基础服务实现，所有具体服务都应嵌入此结构
type BaseServiceImpl[T any] struct{}

// GetListService 获取分页列表
func (s *BaseServiceImpl[T]) GetListService(ac *app.RequestContext, page, pageSize int, fields map[string]string, orderBy string) (model.PageResult[T], error) {
	return model.GetListModel[T](page, pageSize, fields, orderBy)
}

// GetAllService 获取所有记录
func (s *BaseServiceImpl[T]) GetAllService(ac *app.RequestContext, fields map[string]string, orderBy string) ([]T, error) {
	return model.GetAllModel[T](fields, orderBy)
}

// GetInfoService 获取单个记录
func (s *BaseServiceImpl[T]) GetInfoService(ac *app.RequestContext, id uint) (T, bool, error) {
	return model.GetInfoModel[T](id)
}

// AddService 添加记录，支持结构体指针或map
func (s *BaseServiceImpl[T]) AddService(ac *app.RequestContext, data interface{}) (uint, error) {
	switch v := data.(type) {
	case *T:
		// 使用结构体指针
		return model.AddModel(v)
	case map[string]interface{}:
		// 使用map
		return model.AddModelWithMap[T](v)
	default:
		return 0, fmt.Errorf("不支持的数据类型")
	}
}

// ModifyService 修改记录
func (s *BaseServiceImpl[T]) ModifyService(ac *app.RequestContext, id uint, data interface{}) error {
	switch v := data.(type) {
	case *T:
		// 使用结构体指针
		return model.ModifyModel(v)
	case map[string]interface{}:
		// 使用map
		return model.ModifyModelWithMap[T](id, v)
	default:
		return fmt.Errorf("不支持的数据类型")
	}
}

// DeleteService 删除记录
func (s *BaseServiceImpl[T]) DeleteService(ac *app.RequestContext, id uint) error {
	return model.DeleteModel[T](id)
}
