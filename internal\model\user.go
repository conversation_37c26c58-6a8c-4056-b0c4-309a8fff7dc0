package model

import (
	"errors"
	"time"

	"eyc3_meeting/internal/pkg/logger"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// User 用户表
type User struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	Username  string    `gorm:"size:50;not null;uniqueIndex" json:"username"`
	Password  string    `gorm:"size:100;not null" json:"-"` // 不输出到JSON
	Nickname  string    `gorm:"size:50" json:"nickname"`
	Email     string    `gorm:"size:100;uniqueIndex" json:"email"`
	Avatar    string    `gorm:"size:255" json:"avatar"`  // 用户头像
	Status    int       `gorm:"default:1" json:"status"` // 1-正常 0-禁用
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// BeforeSave 密码加密
func (u *User) BeforeSave(tx *gorm.DB) error {
	if u.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(u.Password), bcrypt.DefaultCost)
		if err != nil {
			return err
		}
		u.Password = string(hashedPassword)
	}
	return nil
}

// CheckPassword 验证密码
func (u *User) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password))
	return err == nil
}

// GetUserByID 根据ID获取用户
func GetUserByID(id string) (*User, error) {
	var user User
	result := DB.First(&user, id)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return &user, result.Error
}

// GetUserByUsername 根据用户名获取用户
func GetUserByUsername(username string) (*User, error) {
	var user User
	result := DB.Where("username = ?", username).First(&user)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return &user, result.Error
}

// CreateUser 创建用户
func CreateUser(user *User) error {
	result := DB.Create(user)
	if result.Error != nil {
		logger.Error("创建用户失败", logger.Error2(result.Error))
		return result.Error
	}
	return nil
}

// UpdateUser 更新用户
func UpdateUser(user *User) error {
	result := DB.Save(user)
	if result.Error != nil {
		logger.Error("更新用户失败", logger.Error2(result.Error))
		return result.Error
	}
	return nil
}

// DeleteUser 删除用户
func DeleteUser(id uint) error {
	result := DB.Delete(&User{}, id)
	if result.Error != nil {
		logger.Error("删除用户失败", logger.Error2(result.Error))
		return result.Error
	}
	return nil
}
