package controller

import (
	"context"
	"eyc3_meeting/internal/middleware"
	"eyc3_meeting/internal/model"
	"eyc3_meeting/internal/pkg/logger"
	"eyc3_meeting/internal/pkg/request"
	"eyc3_meeting/internal/pkg/response"
	"eyc3_meeting/internal/service"
	"strconv"

	"github.com/cloudwego/hertz/pkg/app"
)

// Login 登录请求参数
type LoginRequest struct {
	Username string `json:"username" form:"username" binding:"required"`
	Password string `json:"password" form:"password" binding:"required"`
}

// 登录接口
func Login(c context.Context, ctx *app.RequestContext) {
	var req LoginRequest

	// 使用统一参数绑定方法
	if err := request.Bind(ctx, &req); err != nil {
		logger.Error("绑定登录参数失败", logger.Error2(err))
		response.BadRequest(ctx, "无效的请求参数")
		return
	}

	// 直接写死用户信息
	user := struct {
		ID       string
		Username string
	}{
		ID:       "01343110312430306840",
		Username: "石志远",
	}

	// 将字符串ID转换为uint
	userID, _ := strconv.ParseUint(user.ID, 10, 64)

	// 生成token
	tokenString, err := middleware.GenerateToken(uint(userID), user.Username, "ding424e63f5c9ac81e1ffe93478753d9884", "")
	if err != nil {
		logger.Error("生成Token失败", logger.Error2(err))
		response.ServerError(ctx, err)
		return
	}

	// 返回token
	response.Success(ctx, map[string]interface{}{
		"token": tokenString,
		"user": map[string]interface{}{
			"id":       user.ID,
			"username": user.Username,
		},
	})
}

// Register 注册请求参数
type RegisterRequest struct {
	Username string `json:"username" form:"username" binding:"required"`
	Password string `json:"password" form:"password" binding:"required"`
	Nickname string `json:"nickname" form:"nickname"`
	Email    string `json:"email" form:"email" binding:"required,email"`
}

// 注册接口
func Register(c context.Context, ctx *app.RequestContext) {
	var req RegisterRequest

	// 使用统一参数绑定方法
	if err := request.Bind(ctx, &req); err != nil {
		logger.Error("绑定注册参数失败", logger.Error2(err))
		response.BadRequest(ctx, "无效的请求参数")
		return
	}

	// 创建用户
	user := &model.User{
		Username: req.Username,
		Password: req.Password,
		Nickname: req.Nickname,
		Email:    req.Email,
		Status:   1,
	}

	// 调用service层进行注册
	if err := service.Register(user); err != nil {
		response.Fail(ctx, response.StatusBadRequest, err.Error())
		return
	}

	// 生成token
	tokenString, err := middleware.GenerateToken(user.ID, user.Username, "", "")
	if err != nil {
		logger.Error("生成Token失败", logger.Error2(err))
		response.ServerError(ctx, err)
		return
	}

	// 返回token
	response.Success(ctx, map[string]interface{}{
		"token": tokenString,
		"user": map[string]interface{}{
			"id":       user.ID,
			"username": user.Username,
			"nickname": user.Nickname,
			"email":    user.Email,
		},
	})
}

// 刷新Token接口
func RefreshToken(c context.Context, ctx *app.RequestContext) {
	middleware.RefreshToken(c, ctx)
}

// 获取当前用户信息
func GetUserInfo(c context.Context, ctx *app.RequestContext) {
	// 从上下文中获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "未登录")
		return
	}

	// 获取用户信息
	user, err := model.GetUserByID(strconv.Itoa(int(userID.(uint))))
	if err != nil {
		logger.Error("获取用户信息失败", logger.Error2(err))
		response.ServerError(ctx, err)
		return
	}

	if user == nil {
		response.NotFound(ctx, "用户不存在")
		return
	}

	// 返回用户信息
	response.Success(ctx, map[string]interface{}{
		"id":       user.ID,
		"username": user.Username,
		"nickname": user.Nickname,
		"email":    user.Email,
	})
}
