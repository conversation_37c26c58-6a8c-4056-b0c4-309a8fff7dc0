package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strings"

	"database/sql/driver"

	"gorm.io/gorm"
)

// BaseModel 基础模型，所有模型都应该嵌入此结构
type BaseModel struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	CreatedAt int64          `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt int64          `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// RawJSON 自定义类型，用于处理数据库中的原生JSON数据
type RawJSON []byte

// MarshalJSON 序列化时直接返回原始字节，避免被Base64编码
func (r RawJSON) MarshalJSON() ([]byte, error) {
	if len(r) == 0 || string(r) == "null" {
		return []byte("null"), nil
	}
	// 安全检查：确保返回的是有效的JSON，如果不是，则返回null，避免程序崩溃
	if !json.Valid(r) {
		return []byte("null"), nil
	}
	return r, nil
}

// UnmarshalJSON 反序列化时直接接收字节
func (r *RawJSON) UnmarshalJSON(data []byte) error {
	*r = data
	return nil
}

// Value GORM写入数据库时的转换
func (r RawJSON) Value() (driver.Value, error) {
	if len(r) == 0 {
		return nil, nil
	}
	return string(r), nil
}

// Scan GORM从数据库读取时的转换
func (r *RawJSON) Scan(value interface{}) error {
	if value == nil {
		*r = nil
		return nil
	}
	switch v := value.(type) {
	case []byte:
		*r = v
	case string:
		*r = []byte(v)
	default:
		return errors.New("不支持的Scan类型，无法转换为RawJSON")
	}
	return nil
}

// PageResult 分页结果
type PageResult[T any] struct {
	Items []T   `json:"items"`
	Total int64 `json:"total"`
}

// GetListModel 获取分页列表
func GetListModel[T any](page, pageSize int, fields map[string]string, orderBy string) (PageResult[T], error) {
	var items []T
	var total int64

	// 构建查询
	query := DB
	query = buildQueryWithFields(query, fields)

	// 获取总数
	if err := query.Model(new(T)).Count(&total).Error; err != nil {
		return PageResult[T]{}, err
	}

	// 排序
	if orderBy != "" {
		query = query.Order(orderBy)
	}

	// 分页
	offset := (page - 1) * pageSize
	if err := query.Limit(pageSize).Offset(offset).Find(&items).Error; err != nil {
		return PageResult[T]{}, err
	}

	return PageResult[T]{
		Items: items,
		Total: total,
	}, nil
}

// GetAllModel 获取所有记录
func GetAllModel[T any](fields map[string]string, orderBy string) ([]T, error) {
	var items []T

	// 构建查询
	query := DB
	query = buildQueryWithFields(query, fields)

	// 排序
	if orderBy != "" {
		query = query.Order(orderBy)
	}

	// 执行查询
	if err := query.Find(&items).Error; err != nil {
		return nil, err
	}

	return items, nil
}

// buildQueryWithFields 构建查询条件
// 支持 LIKE 查询：字段值包含 % 符号时自动使用 LIKE 查询
func buildQueryWithFields(query *gorm.DB, fields map[string]string) *gorm.DB {
	for k, v := range fields {
		if strings.HasPrefix(k, "like_") {
			// 兼容旧的 like_ 前缀方式
			fieldName := strings.TrimPrefix(k, "like_")
			query = query.Where(fieldName+" LIKE ?", v)
		} else if strings.Contains(v, "%") {
			// 新方式：值中包含 % 符号时自动使用 LIKE 查询
			query = query.Where(k+" LIKE ?", v)
		} else {
			// 普通等值查询
			query = query.Where(k+" = ?", v)
		}
	}
	return query
}

// GetInfoModel 获取单个记录
func GetInfoModel[T any](id uint) (T, bool, error) {
	var item T
	result := DB.First(&item, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			var zero T
			return zero, false, nil
		}
		return item, false, result.Error
	}
	return item, true, nil
}

// AddModel 添加记录
func AddModel[T any](item *T) (uint, error) {
	if err := DB.Create(item).Error; err != nil {
		return 0, err
	}

	// 反射获取ID
	value := reflect.ValueOf(item).Elem()
	idField := value.FieldByName("ID")
	if !idField.IsValid() {
		return 0, fmt.Errorf("模型缺少ID字段")
	}

	return uint(idField.Uint()), nil
}

// ModifyModel 修改记录
func ModifyModel[T any](item *T) error {
	return DB.Save(item).Error
}

// DeleteModel 删除记录
func DeleteModel[T any](id uint) error {
	return DB.Delete(new(T), id).Error
}

// AddModelWithMap 使用map添加记录
// 这样可以只保存map中包含的字段，避免结构体零值污染
func AddModelWithMap[T any](data map[string]interface{}) (uint, error) {
	// 获取表名
	table := getTableName(new(T))

	// 执行创建操作
	result := DB.Table(table).Create(data)
	if result.Error != nil {
		return 0, result.Error
	}

	// 尝试获取ID
	if idVal, exists := data["id"]; exists {
		switch v := idVal.(type) {
		case uint:
			return v, nil
		case int:
			return uint(v), nil
		case int64:
			return uint(v), nil
		case float64:
			return uint(v), nil
		default:
			return 0, fmt.Errorf("无法转换ID类型")
		}
	}

	return 0, nil
}

// getTableName 获取模型对应的表名
func getTableName(model interface{}) string {
	// 尝试调用TableName方法
	if tabler, ok := model.(interface{ TableName() string }); ok {
		return tabler.TableName()
	}

	// 获取结构体名称并转换为snake_case
	t := reflect.TypeOf(model)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	return toSnakeCase(t.Name())
}

// toSnakeCase 将驼峰命名转换为snake_case
func toSnakeCase(s string) string {
	var result strings.Builder
	for i, r := range s {
		if i > 0 && 'A' <= r && r <= 'Z' {
			result.WriteByte('_')
		}
		result.WriteRune(r)
	}
	return strings.ToLower(result.String())
}

// ModifyModelWithMap 使用map修改记录，只更新提供的字段
func ModifyModelWithMap[T any](id uint, data map[string]interface{}) error {
	var model T
	table := getTableName(model)
	result := DB.Table(table).Where("id = ?", id).Updates(data)
	return result.Error
}
