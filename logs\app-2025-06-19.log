{"level":"INFO","time":"2025-06-19T08:49:27.502+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":52,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:27.618+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":103,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:49:27.725+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:27.804+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:27.940+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:49:28.049+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:28.147+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:28.180+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":31,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:28.357+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":176,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:49:28.512+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":154,"影响行数":3}
{"level":"INFO","time":"2025-06-19T08:49:28.597+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:28.772+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":175,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:49:28.935+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":161,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:29.010+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":75,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:29.138+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:49:29.253+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:29.312+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:29.417+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":104,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:49:29.521+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":104,"影响行数":3}
{"level":"INFO","time":"2025-06-19T08:49:29.570+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":48,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:29.687+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":116,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:49:29.830+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:29.882+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":51,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:30.055+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":173,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:49:30.207+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":151,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:30.278+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:30.436+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:49:30.587+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":151,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:30.637+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:30.812+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":174,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:49:30.965+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":152,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T08:49:30.965+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-19T08:49:31.096+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-19T08:49:31.097+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-19T08:49:31.097+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-19T08:49:31.098+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-19T08:53:38.112+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE id = 6 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_bookings`.`id` LIMIT 1","耗时":97,"影响行数":0}
{"level":"ERROR","time":"2025-06-19T08:53:38.145+0800","caller":"controller/base.go:494","msg":"删除记录失败","id":6,"error":"会议预订不存在或无权访问"}
{"level":"INFO","time":"2025-06-19T08:53:43.964+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE id = 9 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_bookings`.`id` LIMIT 1","耗时":106,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:53:44.092+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_bookings` WHERE `eyc_meeting_bookings`.`id` = 9","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:53:51.543+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE id = 10 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_bookings`.`id` LIMIT 1","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:53:51.657+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_bookings` WHERE `eyc_meeting_bookings`.`id` = 10","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:54:01.394+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE id = 11 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_bookings`.`id` LIMIT 1","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:54:01.514+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_bookings` WHERE `eyc_meeting_bookings`.`id` = 11","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:54:06.891+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE id = 12 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_bookings`.`id` LIMIT 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:54:07.008+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_bookings` WHERE `eyc_meeting_bookings`.`id` = 12","耗时":116,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:54:11.706+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE id = 13 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_bookings`.`id` LIMIT 1","耗时":95,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:54:11.823+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_bookings` WHERE `eyc_meeting_bookings`.`id` = 13","耗时":116,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:54:17.209+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE id = 14 AND corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY `eyc_meeting_bookings`.`id` LIMIT 1","耗时":93,"影响行数":1}
{"level":"INFO","time":"2025-06-19T08:54:17.313+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_bookings` WHERE `eyc_meeting_bookings`.`id` = 14","耗时":103,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:11:17.685+0800","caller":"service/EycMeetingBookingService.go:310","msg":"处理参会人信息开始","type":"string","raw_value":"map[departments:[map[dept_id:299939017 name:软件事业部]] users:[map[avatar:https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg name:张菲菲 userid:135933524724483168] map[avatar:https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png name:王佳文 userid:2865524504-1710204866]]]"}
{"level":"INFO","time":"2025-06-19T09:11:17.687+0800","caller":"service/EycMeetingBookingService.go:324","msg":"参会人信息为字符串类型","value":"map[departments:[map[dept_id:299939017 name:软件事业部]] users:[map[avatar:https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg name:张菲菲 userid:135933524724483168] map[avatar:https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png name:王佳文 userid:2865524504-1710204866]]]"}
{"level":"ERROR","time":"2025-06-19T09:11:17.687+0800","caller":"service/EycMeetingBookingService.go:334","msg":"参会人信息格式错误: 接收到Go map字符串，请客户端修改为发送标准JSON格式。","value":"map[departments:[map[dept_id:299939017 name:软件事业部]] users:[map[avatar:https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg name:张菲菲 userid:135933524724483168] map[avatar:https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png name:王佳文 userid:2865524504-1710204866]]]"}
{"level":"ERROR","time":"2025-06-19T09:11:17.687+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"参会人信息格式错误: 接收到Go map字符串，请客户端修改为发送标准JSON格式。"}
{"level":"INFO","time":"2025-06-19T09:14:48.584+0800","caller":"service/EycMeetingBookingService.go:310","msg":"处理参会人信息开始","type":"string","raw_value":"{\"departments\":[{\"dept_id\":\"278542050\",\"name\":\"部门2\"},{\"dept_id\":\"299939017\",\"name\":\"软件事业部\"}],\"users\":[{\"userid\":\"135933524724483168\",\"name\":\"张菲菲\",\"avatar\":\"https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg\"},{\"userid\":\"2865524504-1710204866\",\"name\":\"王佳文\",\"avatar\":\"https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png\"}]}"}
{"level":"INFO","time":"2025-06-19T09:14:48.584+0800","caller":"service/EycMeetingBookingService.go:324","msg":"参会人信息为字符串类型","value":"{\"departments\":[{\"dept_id\":\"278542050\",\"name\":\"部门2\"},{\"dept_id\":\"299939017\",\"name\":\"软件事业部\"}],\"users\":[{\"userid\":\"135933524724483168\",\"name\":\"张菲菲\",\"avatar\":\"https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg\"},{\"userid\":\"2865524504-1710204866\",\"name\":\"王佳文\",\"avatar\":\"https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png\"}]}"}
{"level":"INFO","time":"2025-06-19T09:14:48.584+0800","caller":"service/EycMeetingBookingService.go:414","msg":"参会人信息处理完成","final_json":"{\"departments\":[{\"dept_id\":\"278542050\",\"name\":\"部门2\"},{\"dept_id\":\"299939017\",\"name\":\"软件事业部\"}],\"users\":[{\"avatar\":\"https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg\",\"name\":\"张菲菲\",\"userid\":\"135933524724483168\"},{\"avatar\":\"https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png\",\"name\":\"王佳文\",\"userid\":\"2865524504-1710204866\"}]}"}
{"level":"ERROR","time":"2025-06-19T09:14:48.584+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"重复结束日期格式错误: parsing time \"<nil>\" as \"2006-01-02T15:04:05Z07:00\": cannot parse \"<nil>\" as \"2006\""}
{"level":"INFO","time":"2025-06-19T09:24:44.439+0800","caller":"service/EycMeetingBookingService.go:310","msg":"处理参会人信息开始","type":"string","raw_value":"{\"departments\":[{\"dept_id\":\"278542050\",\"name\":\"部门2\"},{\"dept_id\":\"299939017\",\"name\":\"软件事业部\"}],\"users\":[{\"userid\":\"135933524724483168\",\"name\":\"张菲菲\",\"avatar\":\"https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg\"},{\"userid\":\"2865524504-1710204866\",\"name\":\"王佳文\",\"avatar\":\"https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png\"}]}"}
{"level":"INFO","time":"2025-06-19T09:24:44.440+0800","caller":"service/EycMeetingBookingService.go:324","msg":"参会人信息为字符串类型","value":"{\"departments\":[{\"dept_id\":\"278542050\",\"name\":\"部门2\"},{\"dept_id\":\"299939017\",\"name\":\"软件事业部\"}],\"users\":[{\"userid\":\"135933524724483168\",\"name\":\"张菲菲\",\"avatar\":\"https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg\"},{\"userid\":\"2865524504-1710204866\",\"name\":\"王佳文\",\"avatar\":\"https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png\"}]}"}
{"level":"INFO","time":"2025-06-19T09:24:44.441+0800","caller":"service/EycMeetingBookingService.go:414","msg":"参会人信息处理完成","final_json":"{\"departments\":[{\"dept_id\":\"278542050\",\"name\":\"部门2\"},{\"dept_id\":\"299939017\",\"name\":\"软件事业部\"}],\"users\":[{\"avatar\":\"https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg\",\"name\":\"张菲菲\",\"userid\":\"135933524724483168\"},{\"avatar\":\"https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png\",\"name\":\"王佳文\",\"userid\":\"2865524504-1710204866\"}]}"}
{"level":"INFO","time":"2025-06-19T09:24:44.678+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_bookings` (`corp_id`,`title`,`room_ids`,`room_names`,`location`,`start_time`,`end_time`,`is_full_day`,`participants`,`allow_invite_others`,`notify_types`,`enable_notify`,`reminders`,`repeat_type`,`repeat_end_date`,`description`,`attachments`,`status`,`created_by`,`created_by_name`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','第三季度技术分享会','301','未来之光会议室','总部A座三楼','2024-08-01 10:00:00','2024-08-01 12:00:00',0,'{\"departments\":[{\"dept_id\":\"278542050\",\"name\":\"部门2\"},{\"dept_id\":\"299939017\",\"name\":\"软件事业部\"}],\"users\":[{\"avatar\":\"https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg\",\"name\":\"张菲菲\",\"userid\":\"135933524724483168\"},{\"avatar\":\"https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png\",\"name\":\"王佳文\",\"userid\":\"2865524504-1710204866\"}]}',1,'app,sms',1,'\"[map[label:会议前30分钟 minutes_before:30] map[label:会议前1天 minutes_before:1440]]\"',0,NULL,'本次分享会主要内容：\n1. AI在项目中的应用\n2. Go语言性能优化实践\n3. Q&A环节','\"[map[name:AI应用分享.pptx size:5120 type:pptx url:https://example.com/files/ai_share.pptx]]\"',1,'','','2025-06-19 09:24:44.514','2025-06-19 09:24:44.514')","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:24:44.783+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_bookings` SET `reminders`='\"[map[label:会议前30分钟 minutes_before:30] map[label:会议前1天 minutes_before:1440]]\"',`updated_at`='2025-06-19 09:24:44.678' WHERE `id` = 26","耗时":105,"影响行数":0}
{"level":"INFO","time":"2025-06-19T09:24:45.018+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_bookings` SET `attachments`='\"[map[name:AI应用分享.pptx size:5120 type:pptx url:https://example.com/files/ai_share.pptx]]\"',`updated_at`='2025-06-19 09:24:44.784' WHERE `id` = 26","耗时":235,"影响行数":0}
{"level":"INFO","time":"2025-06-19T09:26:07.189+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":112,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:26:07.344+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":154,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:31:42.937+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":208,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:31:43.136+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":198,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:31:51.558+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":168,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:31:51.822+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":263,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:32:00.038+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":197,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:32:00.245+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":206,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:32:07.383+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":148,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:32:07.579+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":194,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:33:08.653+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":100,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:33:08.768+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":114,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:33:15.044+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":212,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:33:15.319+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":274,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:33:21.087+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":135,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:33:21.280+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":192,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:33:41.016+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":101,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:33:41.150+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":134,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:35:25.794+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":238,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:35:25.936+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":141,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:36:06.683+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":213,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:36:06.855+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":171,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:43:16.163+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-19T09:43:16.163+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-19T09:43:16.163+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-19T09:44:40.003+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":136,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:40.225+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":207,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:44:40.345+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:40.423+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:40.579+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":156,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:44:40.684+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:40.839+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":154,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:40.884+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":44,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:41.024+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:44:41.184+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":159,"影响行数":3}
{"level":"INFO","time":"2025-06-19T09:44:41.256+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:41.386+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:44:41.539+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":152,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:41.615+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":75,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:41.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":220,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:44:42.019+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":183,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:42.105+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:42.265+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":160,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:44:42.539+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":273,"影响行数":3}
{"level":"INFO","time":"2025-06-19T09:44:42.654+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:42.819+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":165,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:44:42.969+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":150,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:43.029+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:43.189+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":160,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:44:43.325+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":135,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:43.385+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:43.539+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":153,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:44:43.703+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:43.760+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:43.964+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":204,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:44:44.119+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":154,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:44:44.120+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-19T09:44:44.354+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-19T09:44:44.355+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-19T09:44:44.356+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-19T09:44:44.357+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-19T09:44:53.542+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":153,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:44:53.983+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":593,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:44:54.168+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":184,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:45:01.442+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":97,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:45:01.625+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":182,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:45:27.708+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":166,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:45:27.868+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":159,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:45:37.871+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":146,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:45:38.031+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":158,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:45:45.373+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":186,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:45:45.531+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":157,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:46:39.567+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND device_type = '0'","耗时":181,"影响行数":7}
{"level":"INFO","time":"2025-06-19T09:46:39.748+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":180,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:47:18.012+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND device_type = '1'","耗时":190,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:47:18.192+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":180,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:51:53.082+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-19T09:51:53.083+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-19T09:51:53.083+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-19T09:51:56.969+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":50,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:51:57.119+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":137,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:51:57.252+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:51:57.298+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":45,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:51:57.422+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:51:57.654+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":231,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:51:57.874+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":220,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:51:57.993+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:51:58.213+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":219,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:51:58.383+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":170,"影响行数":3}
{"level":"INFO","time":"2025-06-19T09:51:58.439+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":55,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:51:58.601+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":161,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:51:58.795+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":193,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:51:58.888+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:51:59.070+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":181,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:51:59.187+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:51:59.225+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":37,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:51:59.384+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:51:59.552+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":167,"影响行数":3}
{"level":"INFO","time":"2025-06-19T09:51:59.619+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:51:59.713+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":93,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:51:59.853+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:51:59.914+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:52:00.038+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":123,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:52:00.139+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:52:00.199+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:52:00.305+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":106,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:52:00.423+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:52:00.489+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:52:00.623+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":133,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:52:00.781+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:52:00.781+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-19T09:52:00.915+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-19T09:52:00.916+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-19T09:52:00.916+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-19T09:52:00.917+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-19T09:52:06.668+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":148,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:52:06.840+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":171,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:52:15.130+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":209,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:52:15.278+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":147,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:52:26.168+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":88,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:52:26.264+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":95,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:54:37.443+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":95,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:54:37.842+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":492,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:54:37.975+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":132,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:55:19.621+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":178,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:55:28.235+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":202,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:55:28.318+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":82,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:56:07.097+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":201,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:56:07.097+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":210,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:56:07.242+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":144,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:56:18.197+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":136,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:56:18.329+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":131,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:56:29.960+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":189,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:56:30.186+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":225,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:56:34.044+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":132,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:56:34.152+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":108,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:56:48.941+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":117,"影响行数":9}
{"level":"INFO","time":"2025-06-19T09:56:49.090+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":149,"影响行数":2}
{"level":"INFO","time":"2025-06-19T09:58:15.683+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-19T09:58:15.683+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-19T09:58:15.684+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-19T09:58:19.450+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":50,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:19.571+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":107,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:58:19.720+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":148,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:19.765+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":44,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:19.885+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:58:20.020+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":134,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:20.155+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:20.201+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":45,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:20.415+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":213,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:58:20.565+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":150,"影响行数":3}
{"level":"INFO","time":"2025-06-19T09:58:20.625+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":60,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:20.737+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":111,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:58:20.885+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":147,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:20.945+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:21.040+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":94,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:58:21.177+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:21.225+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":48,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:21.355+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:58:21.467+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":111,"影响行数":3}
{"level":"INFO","time":"2025-06-19T09:58:21.547+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:21.708+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:58:21.912+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":203,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:22.014+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":101,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:22.145+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:58:22.273+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:22.336+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:22.500+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":163,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:58:22.653+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":152,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:22.702+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:22.845+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":141,"影响行数":1}
{"level":"INFO","time":"2025-06-19T09:58:23.001+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":155,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T09:58:23.001+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-19T09:58:23.175+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-19T09:58:23.175+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-19T09:58:23.175+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-19T09:58:23.177+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-19T09:58:58.045+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, COUNT(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":148,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:04:10.884+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, COUNT(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":119,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:04:11.209+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":443,"影响行数":9}
{"level":"INFO","time":"2025-06-19T10:04:11.308+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":98,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:07:15.872+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":133,"影响行数":9}
{"level":"INFO","time":"2025-06-19T10:07:15.988+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":115,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:10:26.230+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, COUNT(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":94,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:10:26.246+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":112,"影响行数":9}
{"level":"INFO","time":"2025-06-19T10:10:26.364+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":117,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:11:28.377+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-19T10:11:28.379+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-19T10:11:28.381+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-19T10:11:30.985+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":105,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:31.159+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:11:31.375+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":215,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:31.515+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:31.666+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":151,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:11:31.866+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:32.123+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":256,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:32.215+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:32.372+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":156,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:11:32.620+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":248,"影响行数":3}
{"level":"INFO","time":"2025-06-19T10:11:32.676+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":55,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:32.849+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":172,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:11:32.993+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":143,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:33.170+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":177,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:33.412+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":241,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:11:33.567+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":154,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:33.615+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":48,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:33.885+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":269,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:11:34.025+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":139,"影响行数":3}
{"level":"INFO","time":"2025-06-19T10:11:34.122+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:34.316+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":194,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:11:34.542+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":225,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:34.632+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:34.903+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":269,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:11:35.103+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:35.206+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":102,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:35.432+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":225,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:11:35.684+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":251,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:35.742+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:35.983+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":240,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:11:36.202+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":219,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:11:36.203+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-19T10:11:36.350+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-19T10:11:36.350+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-19T10:11:36.350+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-19T10:11:36.351+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-19T10:11:43.345+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, COUNT(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":171,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:16:47.230+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-19T10:16:47.231+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-19T10:16:47.231+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-19T10:16:51.893+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:52.070+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":160,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:16:52.231+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":160,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:52.311+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:52.421+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":109,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:16:52.553+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":132,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:52.651+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:52.696+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":45,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:52.812+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:16:52.991+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":179,"影响行数":3}
{"level":"INFO","time":"2025-06-19T10:16:53.073+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":82,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:53.211+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":137,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:16:53.341+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:53.401+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":60,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:53.513+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:16:53.595+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":81,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:53.652+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:53.774+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:16:53.943+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":169,"影响行数":3}
{"level":"INFO","time":"2025-06-19T10:16:53.988+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":45,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:54.079+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":89,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:16:54.228+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":148,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:54.298+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:54.440+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":141,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:16:54.583+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:54.636+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":52,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:54.762+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:16:54.902+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:54.946+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":43,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:55.072+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":126,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:16:55.180+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":107,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:16:55.181+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-19T10:16:55.318+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-19T10:16:55.320+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-19T10:16:55.320+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-19T10:16:55.321+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-19T10:17:35.679+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":135,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:21:14.981+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-19T10:21:14.982+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-19T10:21:14.982+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-19T10:21:20.027+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":45,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:20.191+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:20.356+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:20.467+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:20.636+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":168,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:20.784+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":147,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:20.880+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:20.996+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":114,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:21.111+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:21.211+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":99,"影响行数":3}
{"level":"INFO","time":"2025-06-19T10:21:21.279+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:21.436+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:21.599+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":162,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:21.663+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:21.831+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":167,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:21.987+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":155,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:22.076+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:22.237+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:22.387+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":150,"影响行数":3}
{"level":"INFO","time":"2025-06-19T10:21:22.462+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:22.601+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":139,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:22.786+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":185,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:22.847+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":60,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:22.996+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":148,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:23.187+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":190,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:23.256+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:23.382+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:23.581+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":198,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:23.717+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":135,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:23.856+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:24.018+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":162,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:24.018+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-19T10:21:24.156+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-19T10:21:24.157+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-19T10:21:24.157+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-19T10:21:24.159+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-19T10:21:27.346+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":82,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:21:29.614+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":101,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:21:45.998+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-19T10:21:45.999+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-19T10:21:45.999+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-19T10:21:50.861+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":38,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:51.018+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:51.182+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:51.242+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:51.341+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":98,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:51.441+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:51.544+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":102,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:51.588+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":42,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:51.698+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:51.793+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":94,"影响行数":3}
{"level":"INFO","time":"2025-06-19T10:21:51.849+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":55,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:51.944+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":94,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:52.074+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:52.121+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:52.238+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":117,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:52.398+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":160,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:52.454+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:52.576+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":121,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:52.682+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":105,"影响行数":3}
{"level":"INFO","time":"2025-06-19T10:21:52.734+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":51,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:52.827+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":92,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:52.921+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:52.968+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:53.080+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:53.201+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:53.278+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:53.428+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":148,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:53.586+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:53.667+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:53.837+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:21:53.982+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:21:53.983+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-19T10:21:54.116+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-19T10:21:54.118+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-19T10:21:54.118+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-19T10:21:54.120+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-19T10:22:01.830+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":160,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:23:22.107+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-19T10:23:22.107+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-19T10:23:22.107+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-19T10:23:24.972+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":47,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:25.091+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":92,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:23:25.226+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":134,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:25.279+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":51,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:25.361+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":81,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:23:25.464+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":102,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:25.645+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":180,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:25.695+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:25.810+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:23:25.911+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":100,"影响行数":3}
{"level":"INFO","time":"2025-06-19T10:23:25.948+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":36,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:26.131+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":182,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:23:26.231+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:26.304+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:26.453+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":148,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:23:26.610+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":157,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:26.653+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":41,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:26.746+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":91,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:23:26.851+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":105,"影响行数":3}
{"level":"INFO","time":"2025-06-19T10:23:26.901+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:26.979+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":78,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:23:27.083+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:27.143+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:27.240+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":97,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:23:27.353+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":112,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:27.413+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:27.524+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:23:27.623+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:27.666+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":42,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:27.775+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":108,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:23:27.923+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":148,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:23:27.924+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-19T10:23:28.022+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-19T10:23:28.024+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-19T10:23:28.024+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-19T10:23:28.024+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-19T10:23:30.999+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":103,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:25:41.689+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-19T10:25:41.690+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-19T10:25:41.690+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-19T10:25:45.923+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:46.091+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:25:46.223+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":131,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:46.284+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:46.404+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:25:46.518+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":113,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:46.617+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:46.671+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":52,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:46.742+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":71,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:25:46.852+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":108,"影响行数":3}
{"level":"INFO","time":"2025-06-19T10:25:46.911+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:47.019+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":107,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:25:47.114+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:47.153+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":39,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:47.239+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":85,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:25:47.341+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":101,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:47.398+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:47.468+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":69,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:25:47.586+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":117,"影响行数":3}
{"level":"INFO","time":"2025-06-19T10:25:47.660+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:47.752+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":90,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:25:47.883+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":131,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:47.943+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:48.086+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":142,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:25:48.205+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:48.268+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:48.414+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:25:48.541+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:48.588+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:48.707+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":119,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:25:48.838+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:25:48.838+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-19T10:25:48.967+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-19T10:25:48.969+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-19T10:25:48.969+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-19T10:25:48.970+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-19T10:25:56.487+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":99,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:40:12.101+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-19T10:40:12.101+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-19T10:40:12.101+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-19T10:40:17.107+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:17.289+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":153,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:40:17.518+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":229,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:17.603+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:17.832+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":228,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:40:18.012+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":179,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:18.187+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":174,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:18.329+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:18.580+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":251,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:40:18.751+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":170,"影响行数":3}
{"level":"INFO","time":"2025-06-19T10:40:18.895+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":143,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:19.120+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":225,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:40:19.375+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":255,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:19.423+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":47,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:19.672+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":249,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:40:19.926+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":252,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:20.003+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:20.167+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":163,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:40:20.343+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":175,"影响行数":3}
{"level":"INFO","time":"2025-06-19T10:40:20.487+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:20.702+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":214,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:40:21.002+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":299,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:21.120+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:21.503+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":381,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:40:21.803+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":299,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:21.963+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:22.240+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":277,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:40:22.523+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":282,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:22.703+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":178,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:23.002+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":299,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:40:23.300+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":297,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:40:23.301+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-19T10:40:23.604+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-19T10:40:23.606+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-19T10:40:23.606+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-19T10:40:23.607+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-19T10:43:19.011+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":133,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:43:19.524+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":637,"影响行数":9}
{"level":"INFO","time":"2025-06-19T10:43:19.696+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":171,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:43:23.939+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":265,"影响行数":9}
{"level":"INFO","time":"2025-06-19T10:43:24.137+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":197,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:45:46.587+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":78,"影响行数":9}
{"level":"INFO","time":"2025-06-19T10:45:46.627+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":121,"影响行数":9}
{"level":"INFO","time":"2025-06-19T10:45:46.699+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":111,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:45:46.719+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":92,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:48:31.484+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":119,"影响行数":9}
{"level":"INFO","time":"2025-06-19T10:48:31.494+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":135,"影响行数":9}
{"level":"INFO","time":"2025-06-19T10:48:31.659+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":164,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:48:31.659+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":174,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:56:50.793+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-19T10:56:50.794+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-19T10:56:50.794+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-19T10:56:53.274+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:53.539+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":252,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:56:53.840+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":300,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:53.963+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:54.286+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":323,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:56:54.486+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:54.729+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":242,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:54.831+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":101,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:55.026+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":195,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:56:55.286+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":259,"影响行数":3}
{"level":"INFO","time":"2025-06-19T10:56:55.406+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:55.667+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":260,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:56:55.961+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":294,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:56.127+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":165,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:56.366+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":239,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:56:56.606+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":239,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:56.707+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:56.906+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:56:57.114+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":207,"影响行数":3}
{"level":"INFO","time":"2025-06-19T10:56:57.327+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":212,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:57.466+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:56:57.726+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":259,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:57.850+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:58.127+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":276,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:56:58.404+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":277,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:58.488+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":82,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:58.704+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":216,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:56:58.864+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:58.926+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:59.060+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":133,"影响行数":1}
{"level":"INFO","time":"2025-06-19T10:56:59.168+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":108,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T10:56:59.169+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-19T10:56:59.306+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-19T10:56:59.308+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-19T10:56:59.308+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-19T10:56:59.310+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-19T10:57:08.305+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":161,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:57:22.280+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":224,"影响行数":2}
{"level":"INFO","time":"2025-06-19T10:57:22.807+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":751,"影响行数":9}
{"level":"INFO","time":"2025-06-19T10:57:23.050+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":242,"影响行数":2}
{"level":"ERROR","time":"2025-06-19T10:57:32.181+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"INFO","time":"2025-06-19T10:57:36.356+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 3 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":105,"影响行数":0}
{"level":"INFO","time":"2025-06-19T10:57:43.145+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 13 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":115,"影响行数":0}
{"level":"INFO","time":"2025-06-19T11:02:02.769+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":211,"影响行数":1}
{"level":"INFO","time":"2025-06-19T11:02:46.329+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":101,"影响行数":2}
{"level":"INFO","time":"2025-06-19T11:02:46.330+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":102,"影响行数":9}
{"level":"INFO","time":"2025-06-19T11:02:46.475+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":144,"影响行数":2}
{"level":"INFO","time":"2025-06-19T11:02:48.899+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0","耗时":137,"影响行数":1}
{"level":"INFO","time":"2025-06-19T11:02:49.016+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":116,"影响行数":10}
{"level":"INFO","time":"2025-06-19T11:04:31.058+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 1 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":141,"影响行数":1}
{"level":"INFO","time":"2025-06-19T11:10:17.405+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌003' OR sn = '测试门牌003')","耗时":129,"影响行数":1}
{"level":"ERROR","time":"2025-06-19T11:10:17.405+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称已被占用"}
{"level":"INFO","time":"2025-06-19T11:10:31.587+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌002' OR sn = '测试门牌002')","耗时":121,"影响行数":1}
{"level":"INFO","time":"2025-06-19T11:10:31.713+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = 'SN00123456781' OR sn = 'SN00123456781')","耗时":125,"影响行数":1}
{"level":"ERROR","time":"2025-06-19T11:10:31.713+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备SN号已被占用"}
{"level":"INFO","time":"2025-06-19T11:10:39.087+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌002' OR sn = '测试门牌002')","耗时":92,"影响行数":1}
{"level":"INFO","time":"2025-06-19T11:10:39.187+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = 'SN00123456786' OR sn = 'SN00123456786')","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-19T11:10:39.342+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`status`,`template_image`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌002','SN00123456786','D3','0',90,80,1,'#1678FF','#1678FF','#F7885C',0,'1',1,'08:00:00','22:00:00',1,'0','2025-06-19 11:10:39.247','2025-06-19 11:10:39.247')","耗时":95,"影响行数":1}
{"level":"INFO","time":"2025-06-19T11:16:10.851+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试门牌002' OR sn = '测试门牌002')","耗时":172,"影响行数":1}
{"level":"INFO","time":"2025-06-19T11:16:11.014+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = 'SN00123456786' OR sn = 'SN00123456786')","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-06-19T11:16:11.219+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`power_on_time`,`power_off_time`,`status`,`template_image`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试门牌002','SN00123456786','D3','0',90,80,1,'#1678FF','#1678FF','#F7885C',0,'1',1,'2025-06-19 08:00:00','2025-06-19 22:00:00',1,'0','2025-06-19 11:16:11.064','2025-06-19 11:16:11.064')","耗时":155,"影响行数":1}
{"level":"INFO","time":"2025-06-19T11:17:03.715+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":143,"影响行数":2}
{"level":"INFO","time":"2025-06-19T11:17:03.716+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":144,"影响行数":10}
{"level":"INFO","time":"2025-06-19T11:17:03.861+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":145,"影响行数":2}
{"level":"ERROR","time":"2025-06-19T11:17:16.267+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"ERROR","time":"2025-06-19T11:18:09.029+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"INFO","time":"2025-06-19T11:19:59.573+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":110,"影响行数":2}
{"level":"INFO","time":"2025-06-19T11:19:59.574+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":102,"影响行数":10}
{"level":"INFO","time":"2025-06-19T11:19:59.679+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":105,"影响行数":2}
{"level":"INFO","time":"2025-06-19T11:20:14.115+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 30 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-19T11:20:40.372+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":170,"影响行数":10}
{"level":"INFO","time":"2025-06-19T11:20:40.609+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":236,"影响行数":2}
{"level":"INFO","time":"2025-06-19T11:20:43.977+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":139,"影响行数":2}
{"level":"INFO","time":"2025-06-19T11:20:44.074+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":228,"影响行数":10}
{"level":"INFO","time":"2025-06-19T11:20:44.209+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":134,"影响行数":2}
{"level":"INFO","time":"2025-06-19T11:20:46.913+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":195,"影响行数":2}
{"level":"INFO","time":"2025-06-19T11:20:46.929+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":210,"影响行数":10}
{"level":"INFO","time":"2025-06-19T11:20:47.156+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":226,"影响行数":2}
{"level":"INFO","time":"2025-06-19T11:38:51.451+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":118,"影响行数":2}
{"level":"INFO","time":"2025-06-19T11:38:51.467+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":134,"影响行数":10}
{"level":"INFO","time":"2025-06-19T11:38:51.644+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":176,"影响行数":2}
{"level":"INFO","time":"2025-06-19T13:37:22.525+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":695,"影响行数":10}
{"level":"INFO","time":"2025-06-19T13:37:22.557+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":728,"影响行数":2}
{"level":"INFO","time":"2025-06-19T13:37:22.761+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":235,"影响行数":2}
{"level":"ERROR","time":"2025-06-19T13:37:46.333+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"INFO","time":"2025-06-19T13:42:10.308+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":167,"影响行数":2}
{"level":"INFO","time":"2025-06-19T13:42:10.361+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":217,"影响行数":10}
{"level":"INFO","time":"2025-06-19T13:42:10.523+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":161,"影响行数":2}
{"level":"ERROR","time":"2025-06-19T13:42:23.724+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"ERROR","time":"2025-06-19T13:46:53.339+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"INFO","time":"2025-06-19T13:47:00.187+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":164,"影响行数":2}
{"level":"INFO","time":"2025-06-19T13:47:00.222+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":196,"影响行数":10}
{"level":"INFO","time":"2025-06-19T13:47:00.485+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":262,"影响行数":2}
{"level":"ERROR","time":"2025-06-19T13:47:10.869+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"INFO","time":"2025-06-19T13:56:00.638+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-19T13:56:00.638+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-19T13:56:00.639+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-19T13:56:06.145+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:06.270+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":112,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:56:06.464+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":194,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:06.536+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:06.747+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":211,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:56:06.921+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":173,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:07.206+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":284,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:07.312+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":105,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:07.485+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":173,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:56:07.749+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":264,"影响行数":3}
{"level":"INFO","time":"2025-06-19T13:56:07.902+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":152,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:08.142+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":239,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:56:08.391+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":248,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:08.519+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":127,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:08.802+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":282,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:56:08.942+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:09.017+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":75,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:09.187+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:56:09.377+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":190,"影响行数":3}
{"level":"INFO","time":"2025-06-19T13:56:09.472+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:09.642+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":170,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:56:09.805+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":162,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:09.869+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:10.021+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":151,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:56:10.146+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:10.196+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:10.327+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:56:10.442+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":114,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:10.509+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:10.624+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:56:10.720+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:56:10.720+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-19T13:56:10.824+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-19T13:56:10.825+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-19T13:56:10.825+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-19T13:56:10.826+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-19T13:56:23.780+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 30 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":255,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:56:56.869+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-19T13:56:56.870+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-19T13:56:56.870+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-19T13:59:40.541+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":189,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:40.736+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":182,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:59:40.941+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":205,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:41.013+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:41.153+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:59:41.424+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":270,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:41.631+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":206,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:41.705+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":72,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:41.876+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":171,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:59:42.033+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":156,"影响行数":3}
{"level":"INFO","time":"2025-06-19T13:59:42.101+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:42.254+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":152,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:59:42.386+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":131,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:42.451+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:42.650+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":198,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:59:42.813+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":162,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:42.904+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:43.106+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":202,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:59:43.308+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":201,"影响行数":3}
{"level":"INFO","time":"2025-06-19T13:59:43.403+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:43.501+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":97,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:59:43.652+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:43.696+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":44,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:43.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:59:44.041+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":204,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:44.151+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:44.368+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":216,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:59:44.600+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":232,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:44.675+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:44.941+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":265,"影响行数":1}
{"level":"INFO","time":"2025-06-19T13:59:45.161+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":220,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T13:59:45.161+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-19T13:59:45.331+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-19T13:59:45.332+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-19T13:59:45.333+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-19T13:59:45.334+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-19T13:59:50.797+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE `eyc_meeting_device`.`id` = 30 ORDER BY `eyc_meeting_device`.`id` LIMIT 1","耗时":171,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:00:25.899+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":105,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:00:26.301+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":503,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:00:26.556+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":255,"影响行数":2}
{"level":"ERROR","time":"2025-06-19T14:00:46.869+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"INFO","time":"2025-06-19T14:01:51.177+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":184,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:01:51.356+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY id DESC","耗时":178,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:02:21.272+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":445,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:02:21.273+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":448,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:02:21.519+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":246,"影响行数":2}
{"level":"ERROR","time":"2025-06-19T14:02:34.760+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"INFO","time":"2025-06-19T14:09:12.167+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":135,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:09:12.184+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":148,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:09:12.442+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":257,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:09:17.014+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":113,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:09:17.016+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":113,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:09:17.152+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":135,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:11:52.422+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":89,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:11:52.467+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":135,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:11:52.655+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":232,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:11:56.658+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":308,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:11:56.658+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":303,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:11:56.813+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":153,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:13:26.717+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":106,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:13:26.744+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":132,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:13:26.867+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":122,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:13:28.692+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":141,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:13:28.692+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":143,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:13:28.860+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":168,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:13:41.656+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":328,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:13:41.671+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":340,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:13:41.894+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":223,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:14:30.193+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":89,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:14:30.238+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":132,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:14:30.375+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":137,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:16:32.694+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":353,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:16:32.700+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":360,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:16:32.759+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":58,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:16:36.811+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":236,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:16:36.849+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":273,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:16:37.344+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":494,"影响行数":2}
{"level":"ERROR","time":"2025-06-19T14:17:55.567+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"INFO","time":"2025-06-19T14:21:14.066+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":120,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:21:14.078+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":131,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:21:14.245+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":166,"影响行数":2}
{"level":"ERROR","time":"2025-06-19T14:22:10.504+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"INFO","time":"2025-06-19T14:26:12.498+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":144,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:26:12.500+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":143,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:26:12.640+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":140,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:26:15.282+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":163,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:26:15.476+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 158","耗时":193,"影响行数":3}
{"level":"INFO","time":"2025-06-19T14:26:15.476+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0","耗时":247,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:26:15.579+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 161","耗时":102,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:26:15.630+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":153,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:26:16.806+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":88,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:26:16.815+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":110,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:26:16.919+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 158","耗时":103,"影响行数":3}
{"level":"INFO","time":"2025-06-19T14:26:17.008+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 161","耗时":88,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:26:17.196+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":467,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:26:26.982+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0","耗时":539,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:26:26.982+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":642,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:26:27.052+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 158","耗时":69,"影响行数":3}
{"level":"INFO","time":"2025-06-19T14:26:27.067+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":84,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:26:27.182+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 161","耗时":129,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:26:27.808+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":95,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:26:27.847+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":131,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:26:27.992+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":144,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:28:13.366+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":280,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:28:13.376+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":285,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:28:13.683+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":307,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:30:42.450+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":250,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:30:42.450+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":249,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:30:42.692+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":240,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:32:50.609+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":105,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:32:50.617+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":108,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:32:50.731+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":114,"影响行数":2}
{"level":"WARN","time":"2025-06-19T14:33:00.317+0800","caller":"model/logger.go:81","msg":"GORM慢查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":1981,"影响行数":2}
{"level":"WARN","time":"2025-06-19T14:33:00.319+0800","caller":"model/logger.go:81","msg":"GORM慢查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":1984,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:33:00.903+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":583,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:34:49.883+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":93,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:34:49.905+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":116,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:34:50.002+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":96,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:34:54.045+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":172,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:34:54.046+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":174,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:34:54.243+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":197,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:41:01.838+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":69,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:41:01.852+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":81,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:41:01.958+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":105,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:41:13.732+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":77,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:41:13.754+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":100,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:41:13.861+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":107,"影响行数":2}
{"level":"ERROR","time":"2025-06-19T14:43:47.077+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"ERROR","time":"2025-06-19T14:44:58.324+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"ERROR","time":"2025-06-19T14:45:29.237+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"INFO","time":"2025-06-19T14:46:13.622+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-19T14:46:13.623+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-19T14:46:13.623+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-19T14:46:15.727+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:15.881+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":143,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:46:16.091+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":209,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:16.252+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":160,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:16.526+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":274,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:46:16.864+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":337,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:17.255+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":389,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:17.476+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":220,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:17.752+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":275,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:46:17.966+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":214,"影响行数":3}
{"level":"INFO","time":"2025-06-19T14:46:18.071+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:18.307+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":235,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:46:18.527+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":220,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:18.595+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:18.802+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":206,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:46:19.041+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":239,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:19.167+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":125,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:19.325+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:46:19.472+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":147,"影响行数":3}
{"level":"INFO","time":"2025-06-19T14:46:19.526+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:19.677+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":150,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:46:19.900+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":222,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:20.010+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:20.227+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":216,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:46:20.386+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:20.496+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:20.740+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":243,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:46:20.970+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":229,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:21.071+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":101,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:21.217+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:46:21.479+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":261,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:46:21.479+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-19T14:46:21.681+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-19T14:46:21.682+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-19T14:46:21.682+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-19T14:46:21.683+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-06-19T14:46:45.304+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"设备名称不能为空"}
{"level":"INFO","time":"2025-06-19T14:47:04.973+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":203,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:47:05.668+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":893,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:47:05.930+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":262,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:47:42.903+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试1' OR sn = '测试1')","耗时":119,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:47:43.125+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = 'sn001' OR sn = 'sn001')","耗时":221,"影响行数":1}
{"level":"ERROR","time":"2025-06-19T14:47:43.191+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`status`,`template_image`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试1','sn001','D3','0',100,100,0,'','','',0,'0',0,1,'https://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/canyin/2025-06-19/6a401af1aae19315ecda31924662a703.svg','2025-06-19 14:47:43.191','2025-06-19 14:47:43.191')","耗时":0,"error":"sql: converting argument $17 type: unsupported type model.LocalTime, a struct"}
{"level":"ERROR","time":"2025-06-19T14:47:43.305+0800","caller":"controller/base.go:556","msg":"新增记录失败","error":"sql: converting argument $17 type: unsupported type model.LocalTime, a struct"}
{"level":"INFO","time":"2025-06-19T14:52:02.098+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":160,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:52:02.169+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":228,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:52:02.410+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":241,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:53:17.956+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-19T14:53:17.956+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-19T14:53:17.956+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-19T14:53:25.527+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:25.670+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":119,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:53:25.798+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:25.843+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":45,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:25.949+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":106,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:53:26.067+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:26.191+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":123,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:26.232+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":39,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:26.337+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":104,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:53:26.503+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":165,"影响行数":3}
{"level":"INFO","time":"2025-06-19T14:53:26.613+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:26.710+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":96,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:53:26.890+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":179,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:26.983+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:27.190+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":206,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:53:27.430+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":239,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:27.470+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":38,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:28.422+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":952,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:53:29.002+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":579,"影响行数":3}
{"level":"INFO","time":"2025-06-19T14:53:29.099+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:29.247+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":147,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:53:29.362+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":114,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:29.428+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:29.563+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:53:29.690+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:29.772+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:29.981+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":208,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:53:30.257+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":275,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:30.328+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:30.493+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:53:30.642+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-06-19T14:53:30.643+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-19T14:53:30.763+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-19T14:53:30.764+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-19T14:53:30.764+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-19T14:53:30.765+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-19T14:53:34.601+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":113,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:53:35.058+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":565,"影响行数":10}
{"level":"INFO","time":"2025-06-19T14:53:35.265+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":206,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:53:59.694+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = '测试1' OR sn = '测试1')","耗时":264,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:53:59.860+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND (title = 'sn001' OR sn = 'sn001')","耗时":166,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:54:00.090+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_device` (`corp_id`,`title`,`sn`,`model`,`device_type`,`light_brightness`,`volume`,`custom_template`,`template_free`,`template_idle`,`template_using`,`related_door`,`system_mode`,`auto_power`,`status`,`template_image`,`created_at`,`updated_at`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','测试1','sn001','D3','0',25,100,0,'','','',0,'0',0,1,'','2025-06-19 14:53:59.965','2025-06-19 14:53:59.965')","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-06-19T14:54:02.531+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":213,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:54:02.531+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":212,"影响行数":11}
{"level":"INFO","time":"2025-06-19T14:54:02.731+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":198,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:54:14.001+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":225,"影响行数":11}
{"level":"INFO","time":"2025-06-19T14:54:14.183+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":181,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:54:23.201+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":109,"影响行数":11}
{"level":"INFO","time":"2025-06-19T14:54:23.333+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":131,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:54:37.984+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":200,"影响行数":11}
{"level":"INFO","time":"2025-06-19T14:54:38.132+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":148,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:54:51.555+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT device_type, count(*) as count FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' GROUP BY `device_type`","耗时":123,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:54:51.555+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":122,"影响行数":11}
{"level":"INFO","time":"2025-06-19T14:54:51.652+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device_group` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":97,"影响行数":2}
{"level":"INFO","time":"2025-06-19T14:58:08.873+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":210,"影响行数":11}
{"level":"INFO","time":"2025-06-19T15:05:58.264+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":370,"影响行数":11}
{"level":"INFO","time":"2025-06-19T15:06:29.127+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":330,"影响行数":11}
{"level":"INFO","time":"2025-06-19T15:07:11.064+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":274,"影响行数":11}
{"level":"INFO","time":"2025-06-19T15:07:25.751+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":150,"影响行数":11}
{"level":"INFO","time":"2025-06-19T15:08:26.637+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":294,"影响行数":11}
{"level":"INFO","time":"2025-06-19T15:08:31.522+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":216,"影响行数":11}
{"level":"INFO","time":"2025-06-19T15:08:35.822+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":211,"影响行数":11}
{"level":"INFO","time":"2025-06-19T15:09:04.943+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":251,"影响行数":11}
{"level":"INFO","time":"2025-06-19T15:15:46.735+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":272,"影响行数":11}
{"level":"INFO","time":"2025-06-19T15:16:43.801+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-19T15:16:43.801+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-19T15:16:43.801+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
