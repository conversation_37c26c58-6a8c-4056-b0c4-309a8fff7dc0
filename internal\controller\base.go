package controller

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"reflect"
	"strconv"
	"strings"

	"eyc3_meeting/internal/pkg/logger"
	"eyc3_meeting/internal/pkg/request"
	"eyc3_meeting/internal/pkg/response"
	"eyc3_meeting/internal/service"

	"github.com/cloudwego/hertz/pkg/app"
)

// BaseController 基础控制器
type BaseController[T any, S service.BaseService[T]] struct {
	Service S
}

// extractPaginationParams 提取分页参数
func extractPaginationParams(ac *app.RequestContext) (page, pageSize int, orderBy string) {
	allParams := make(map[string]interface{})

	// 1. 从查询参数提取
	ac.QueryArgs().VisitAll(func(key, value []byte) {
		allParams[string(key)] = string(value)
	})

	// 2. 如果是POST/PUT/PATCH, 从JSON Body提取 (会覆盖同名查询参数)
	method := string(ac.Request.Method())
	if method == "POST" || method == "PUT" || method == "PATCH" {
		contentType := string(ac.Request.Header.ContentType())
		if strings.Contains(contentType, "application/json") {
			var bodyData map[string]interface{}
			if err := ac.BindJSON(&bodyData); err == nil {
				for k, v := range bodyData {
					allParams[k] = v
				}
			} else if err != io.EOF {
				logger.Warn("绑定JSON Body失败", logger.Error2(err))
			}
		}
	}

	// 3. 设置默认值
	page = 1
	pageSize = 10
	orderBy = ""

	// 4. 处理page参数
	if pageVal, ok := allParams["page"]; ok {
		switch v := pageVal.(type) {
		case int:
			if v > 0 {
				page = v
			}
		case float64:
			// JSON中的数字默认解析为float64
			intVal := int(v)
			if intVal > 0 {
				page = intVal
			}
		}
	}

	// 5. 处理pagesize和perPage参数
	perPageVal, hasPerPage := allParams["perPage"]

	if hasPerPage {
		// 如果没有pagesize但有perPage
		switch v := perPageVal.(type) {
		case float64:
			if p := int(v); p > 0 {
				pageSize = p
			}
		case int:
			if p := v; p > 0 {
				pageSize = p
			}
		}
	}

	// 限制pageSize的最大值
	if pageSize > 100 {
		pageSize = 100
	}

	// 6. 处理orderby参数
	if orderByVal, ok := allParams["orderby"]; ok {
		if s, ok := orderByVal.(string); ok {
			orderBy = s
		} else {
			orderBy = fmt.Sprint(orderByVal)
		}
	}

	return page, pageSize, orderBy
}

// extractQueryFields 提取查询字段(支持从Query和JSON Body获取)
func extractQueryFields(ac *app.RequestContext, fieldsStr string, excludeFields ...string) map[string]string {
	fields := make(map[string]string)
	allParams := make(map[string]interface{})

	// 1. 从查询参数提取
	ac.QueryArgs().VisitAll(func(key, value []byte) {
		allParams[string(key)] = string(value)
	})

	// 2. 如果是POST/PUT/PATCH, 从JSON Body提取 (会覆盖同名查询参数)
	method := string(ac.Request.Method())
	if method == "POST" || method == "PUT" || method == "PATCH" {
		contentType := string(ac.Request.Header.ContentType())
		if strings.Contains(contentType, "application/json") {
			var bodyData map[string]interface{}
			if err := ac.BindJSON(&bodyData); err == nil {
				for k, v := range bodyData {
					allParams[k] = v
				}
			} else if err != io.EOF {
				logger.Warn("绑定JSON Body失败", logger.Error2(err))
			}
		}
	}

	// 创建排除字段集合
	excludeMap := make(map[string]bool)
	for _, field := range excludeFields {
		excludeMap[field] = true
	}

	if fieldsStr != "" {
		// 使用指定字段
		fieldsList := strings.Split(fieldsStr, ",")
		for _, field := range fieldsList {
			fieldName := field
			fieldType := ""
			// 去除类型标记
			if strings.Contains(field, ":") {
				fieldName = strings.Split(field, ":")[0]
				fieldType = strings.Split(field, ":")[1]
			}
			// 从合并后的参数中取值
			if val, ok := allParams[fieldName]; ok {
				strVal := fmt.Sprint(val)
				if strVal != "" {
					fields[fieldName] = strVal
					if fieldType == "%" {
						fields[fieldName] = "%" + strVal + "%"
					}
				}
			}
		}
	} else {
		// 提取所有参数
		for k, v := range allParams {
			strVal := fmt.Sprint(v)
			if !excludeMap[k] && strVal != "" {
				fields[k] = strVal
			}
		}
	}

	return fields
}

// setStructFields 设置结构体字段值
func setStructFields[T any](item *T, data map[string]interface{}) {
	objValue := reflect.ValueOf(item).Elem()
	objType := objValue.Type()

	for i := 0; i < objType.NumField(); i++ {
		field := objType.Field(i)

		// 处理嵌入字段
		if field.Anonymous {
			continue // 简化版忽略嵌入字段处理
		}

		// 获取json标签
		jsonTag := field.Tag.Get("json")
		if jsonTag == "" || jsonTag == "-" {
			continue
		}
		jsonName := strings.Split(jsonTag, ",")[0]

		// 从map中获取值
		val, ok := data[jsonName]
		if !ok {
			continue
		}

		// 如果值为字符串且为空，则跳过
		if s, ok := val.(string); ok && s == "" {
			continue
		}

		// 获取字段值
		fieldValue := objValue.Field(i)
		if !fieldValue.CanSet() {
			continue
		}

		// 根据字段类型设置值
		switch fieldValue.Kind() {
		case reflect.String:
			str := fmt.Sprint(val)
			fieldValue.SetString(str)
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			var intVal int64
			switch v := val.(type) {
			case int:
				intVal = int64(v)
			case float64:
				intVal = int64(v)
			case string:
				if iv, err := strconv.ParseInt(v, 10, 64); err == nil {
					intVal = iv
				}
			}
			fieldValue.SetInt(intVal)
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			var uintVal uint64
			switch v := val.(type) {
			case int:
				uintVal = uint64(v)
			case float64:
				uintVal = uint64(v)
			case string:
				if uv, err := strconv.ParseUint(v, 10, 64); err == nil {
					uintVal = uv
				}
			}
			fieldValue.SetUint(uintVal)
		case reflect.Bool:
			var boolVal bool
			switch v := val.(type) {
			case bool:
				boolVal = v
			case string:
				boolVal = v == "true" || v == "1"
			case int, float64:
				boolVal = v != 0
			}
			fieldValue.SetBool(boolVal)
		}
	}
}

// GetList 获取分页列表
// 用法：controller.GetList() 或 controller.GetList("name,status:i,type")
// 不传参数则获取所有字段，传参数则只获取指定字段
// 支持两种模糊查询方式：
// 1. 参数值使用：%string（前缀匹配）、string%（后缀匹配）或 %string%（包含匹配）
// 2. 字段定义时添加:%标记，如 "car_number:%,name:%"，自动对这些字段进行包含匹配（相当于%value%）
func (c *BaseController[T, S]) GetList(isCorpID bool, fieldsStr ...string) func(context.Context, *app.RequestContext) {
	return func(ctx context.Context, ac *app.RequestContext) {
		page, pageSize, orderBy := extractPaginationParams(ac)

		// 从上下文中获取corpid（如果需要）
		var corpidStr string
		if isCorpID {
			corpid, exists := ac.Get("corp_id")
			if !exists {
				response.Unauthorized(ac, "未登录")
				return
			}

			var ok bool
			corpidStr, ok = corpid.(string)
			if !ok {
				logger.Error("corp_id类型错误", logger.String("corp_id", fmt.Sprintf("%v", corpid)))
				response.ServerError(ac, fmt.Errorf("corp_id类型错误"))
				return
			}
		}

		var fields map[string]string
		if len(fieldsStr) > 0 && fieldsStr[0] != "" {
			fields = extractQueryFields(ac, fieldsStr[0], "page", "perPage", "orderby")
		} else {
			fields = extractQueryFields(ac, "", "page", "perPage", "orderby")
		}

		// 如果需要，添加corpid条件
		if isCorpID && corpidStr != "" {
			fields["corp_id"] = corpidStr
		}

		// 调用服务层
		result, err := c.Service.GetListService(ac, page, pageSize, fields, orderBy)
		if err != nil {
			logger.Error("获取列表失败", logger.Error2(err))
			response.ServerError(ac, err)
			return
		}

		response.Success(ac, result)
	}
}

// GetAll 获取所有记录
// 用法：controller.GetAll() 或 controller.GetAll("category,status:b")
// 不传参数则获取所有字段，传参数则只获取指定字段
// 支持两种模糊查询方式：
// 1. 参数值使用：%string（前缀匹配）、string%（后缀匹配）或 %string%（包含匹配）
// 2. 字段定义时添加:%标记，如 "car_number:%,name:%"，自动对这些字段进行包含匹配（相当于%value%）
func (c *BaseController[T, S]) GetAll(isCorpID bool, fieldsStr ...string) func(context.Context, *app.RequestContext) {
	return func(ctx context.Context, ac *app.RequestContext) {
		orderBy := ac.DefaultQuery("orderby", "")

		// 从上下文中获取corpid（如果需要）
		var corpidStr string
		if isCorpID {
			corpid, exists := ac.Get("corp_id")
			if !exists {
				response.Unauthorized(ac, "未登录")
				return
			}

			var ok bool
			corpidStr, ok = corpid.(string)
			if !ok {
				logger.Error("corp_id类型错误", logger.String("corp_id", fmt.Sprintf("%v", corpid)))
				response.ServerError(ac, fmt.Errorf("corp_id类型错误"))
				return
			}
		}

		var fields map[string]string
		if len(fieldsStr) > 0 && fieldsStr[0] != "" {
			fields = extractQueryFields(ac, fieldsStr[0], "orderby", "page", "perPage")
		} else {
			fields = extractQueryFields(ac, "", "orderby", "page", "perPage")
		}

		// 如果需要，添加corpid条件
		if isCorpID && corpidStr != "" {
			fields["corp_id"] = corpidStr
		}

		// 调用服务层
		items, err := c.Service.GetAllService(ac, fields, orderBy)
		if err != nil {
			logger.Error("获取所有记录失败", logger.Error2(err))
			response.ServerError(ac, err)
			return
		}

		response.Success(ac, items)
	}
}

// GetInfo 获取单个记录
func (c *BaseController[T, S]) GetInfo() func(context.Context, *app.RequestContext) {
	return func(ctx context.Context, ac *app.RequestContext) {
		// 获取ID参数
		idStr := ac.Query("id")
		if idStr == "" {
			response.BadRequest(ac, "ID参数不能为空")
			return
		}

		id, err := strconv.ParseUint(idStr, 10, 32)
		if err != nil {
			logger.Error("解析ID失败", logger.String("id", idStr), logger.Error2(err))
			response.BadRequest(ac, "无效的ID参数")
			return
		}

		// 调用服务层
		item, found, err := c.Service.GetInfoService(ac, uint(id))
		if err != nil {
			logger.Error("获取记录失败", logger.Int("id", int(id)), logger.Error2(err))
			response.ServerError(ac, err)
			return
		}

		if !found {
			response.NotFound(ac, "记录不存在")
			return
		}

		response.Success(ac, item)
	}
}

// Modify 修改记录
// 用法：controller.Modify() 或 controller.Modify("id:i,name,status:i")
// 不传参数则使用标准的请求绑定，传参数则只绑定指定字段
// isCorpID: 是否需要从上下文中获取企业ID并验证权限
func (c *BaseController[T, S]) Modify(isCorpID bool, fieldsStr ...string) func(context.Context, *app.RequestContext) {
	return func(ctx context.Context, ac *app.RequestContext) {
		var err error

		// 初始化data
		var data map[string]interface{}

		// 获取请求数据
		if len(fieldsStr) > 0 && fieldsStr[0] != "" {
			// 使用字段绑定
			fields := strings.Split(fieldsStr[0], ",")
			data, err = request.BindFields(ac, fields...)
			if err != nil {
				logger.Error("绑定参数失败", logger.Error2(err))
				response.BadRequest(ac, "无效的请求参数")
				return
			}
		} else {
			// 使用标准绑定到map
			data = make(map[string]interface{})
			if err = ac.Bind(&data); err != nil {
				logger.Error("绑定参数失败", logger.Error2(err))
				response.BadRequest(ac, "无效的请求参数")
				return
			}
		}

		// 从data中提取ID
		var id uint
		if idVal, exists := data["id"]; exists {
			switch v := idVal.(type) {
			case uint:
				id = v
			case int:
				id = uint(v)
			case float64: // JSON numbers are often float64
				id = uint(v)
			case string:
				if idInt, err := strconv.ParseUint(v, 10, 32); err == nil {
					id = uint(idInt)
				}
			}
		}

		if id == 0 {
			response.BadRequest(ac, "缺少或无效的ID参数")
			return
		}

		// 从更新数据中移除ID
		delete(data, "id")

		// 从上下文中获取corpid（如果需要, 用于权限验证）
		if isCorpID {
			corpid, exists := ac.Get("corp_id")
			if !exists {
				response.Unauthorized(ac, "未登录")
				return
			}

			if _, ok := corpid.(string); !ok {
				logger.Error("corp_id类型错误", logger.String("corp_id", fmt.Sprintf("%v", corpid)))
				response.ServerError(ac, fmt.Errorf("corp_id类型错误"))
				return
			}
			// 服务层应使用上下文中的corp_id进行验证
		}

		// 调用服务层
		if err = c.Service.ModifyService(ac, id, data); err != nil {
			logger.Error("修改记录失败", logger.Error2(err))
			response.Fail(ac, 400, err.Error())
			return
		}

		response.SuccessWithMsg(ac, "修改成功")
	}
}

// Delete 删除记录
func (c *BaseController[T, S]) Delete() func(context.Context, *app.RequestContext) {
	return func(ctx context.Context, ac *app.RequestContext) {
		// 获取ID参数
		idStr := ac.Query("id")
		if idStr == "" {
			response.BadRequest(ac, "ID参数不能为空")
			return
		}

		id, err := strconv.ParseUint(idStr, 10, 32)
		if err != nil {
			logger.Error("解析ID失败", logger.String("id", idStr), logger.Error2(err))
			response.BadRequest(ac, "无效的ID参数")
			return
		}

		// 调用服务层
		if err := c.Service.DeleteService(ac, uint(id)); err != nil {
			logger.Error("删除记录失败", logger.Int("id", int(id)), logger.Error2(err))
			response.Fail(ac, 400, err.Error())
			return
		}

		response.Success(ac, "删除成功")
	}
}

// Add 新增记录
// 用法：controller.Add() 或 controller.Add("name,status:i")
// 不传参数则使用标准的请求绑定，传参数则只绑定指定字段
// isCorpID: 是否需要从上下文中获取企业ID
func (c *BaseController[T, S]) Add(isCorpID bool, fieldsStr ...string) func(context.Context, *app.RequestContext) {
	return func(ctx context.Context, ac *app.RequestContext) {
		// 最终修正：放弃BindFields，直接从请求的原始Body中解析JSON
		var item T
		if err := json.Unmarshal(ac.Request.Body(), &item); err != nil {
			response.Fail(ac, response.StatusBadRequest, "JSON解析失败: "+err.Error())
			return
		}

		// 检查并设置corp_id
		corpID, exists := ac.Get("corp_id")
		if isCorpID {
			if !exists || corpID == "" {
				response.Fail(ac, response.StatusForbidden, "缺少corp_id")
				return
			}
			// 使用反射为模型设置 Corpid
			v := reflect.ValueOf(&item).Elem()
			f := v.FieldByName("Corpid")
			if f.IsValid() && f.CanSet() && f.Kind() == reflect.String {
				f.SetString(corpID.(string))
			}
		}

		// 调用服务层
		id, err := c.Service.AddService(ac, &item)
		if err != nil {
			logger.Error("新增记录失败", logger.Error2(err))
			response.Fail(ac, response.StatusBadRequest, "新增记录失败: "+err.Error())
			return
		}
		response.SuccessWithData(ac, "新增记录成功", map[string]uint{"id": id})
	}
}
