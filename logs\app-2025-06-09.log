{"level":"INFO","time":"2025-06-09T08:58:54.895+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:55.022+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-06-09T08:58:55.174+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":151,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:55.267+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:55.434+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":166,"影响行数":1}
{"level":"INFO","time":"2025-06-09T08:58:55.617+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":182,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:55.740+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":121,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:55.788+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":47,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:55.879+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":90,"影响行数":1}
{"level":"INFO","time":"2025-06-09T08:58:56.001+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":122,"影响行数":3}
{"level":"INFO","time":"2025-06-09T08:58:56.079+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:56.192+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":112,"影响行数":1}
{"level":"INFO","time":"2025-06-09T08:58:56.340+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":147,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:56.387+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:56.516+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-09T08:58:56.663+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":146,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:56.722+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:56.853+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-06-09T08:58:57.012+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":158,"影响行数":3}
{"level":"INFO","time":"2025-06-09T08:58:57.072+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:57.186+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":113,"影响行数":1}
{"level":"INFO","time":"2025-06-09T08:58:57.279+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:57.327+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:57.506+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-06-09T08:58:57.683+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":176,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:57.747+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:57.849+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":102,"影响行数":1}
{"level":"INFO","time":"2025-06-09T08:58:57.999+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:58.087+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":86,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:58.269+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":181,"影响行数":1}
{"level":"INFO","time":"2025-06-09T08:58:58.419+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":150,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T08:58:58.419+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-09T08:58:58.531+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-09T08:58:58.532+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-09T08:58:58.532+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-09T08:58:58.533+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-09T10:00:48.085+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:48.212+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:00:48.329+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:48.367+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":37,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:48.529+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":161,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:00:48.684+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":154,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:48.778+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:48.832+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:48.947+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:00:49.067+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":120,"影响行数":3}
{"level":"INFO","time":"2025-06-09T10:00:49.107+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":39,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:49.267+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":160,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:00:49.467+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:49.558+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:49.712+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:00:49.872+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:49.965+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:50.107+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:00:50.302+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":195,"影响行数":3}
{"level":"INFO","time":"2025-06-09T10:00:50.412+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:50.512+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:00:50.657+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":143,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:50.742+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:50.887+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:00:51.010+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:51.072+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:51.209+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:00:51.389+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":179,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:51.470+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:51.677+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":207,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:00:51.870+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":192,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:00:51.871+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-09T10:00:52.014+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-09T10:00:52.017+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-09T10:00:52.017+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-09T10:00:52.018+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-09T10:28:27.410+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-09T10:28:27.411+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-09T10:28:27.411+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-09T10:28:45.089+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:45.260+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:28:45.616+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":355,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:45.684+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:45.978+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":293,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:28:46.114+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":136,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:46.255+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:46.374+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:46.494+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":119,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:28:46.645+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":149,"影响行数":3}
{"level":"INFO","time":"2025-06-09T10:28:46.750+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:46.850+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:28:46.996+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":145,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:47.054+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:47.202+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":147,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:28:47.379+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":176,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:47.460+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:47.655+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":195,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:28:47.861+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":206,"影响行数":3}
{"level":"INFO","time":"2025-06-09T10:28:47.933+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:48.094+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":161,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:28:48.255+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:48.337+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":81,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:48.525+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":187,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:28:48.698+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":172,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:48.771+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:49.010+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":236,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:28:49.208+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":197,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:49.333+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:49.515+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":181,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:28:49.692+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":177,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:28:49.693+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-09T10:28:49.929+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-09T10:28:49.930+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-09T10:28:49.931+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-09T10:28:49.932+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-09T10:29:01.292+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-09T10:29:01.293+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-09T10:29:01.293+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-09T10:36:49.142+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:49.299+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:36:49.410+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:49.462+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:49.566+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":104,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:36:49.696+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:49.792+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:49.921+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:50.074+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":153,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:36:50.260+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":185,"影响行数":3}
{"level":"INFO","time":"2025-06-09T10:36:50.334+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:50.432+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":97,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:36:50.532+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:50.608+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:50.762+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":153,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:36:50.903+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:50.991+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:51.135+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:36:51.294+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":158,"影响行数":3}
{"level":"INFO","time":"2025-06-09T10:36:51.411+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:51.570+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:36:51.735+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":164,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:51.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:52.014+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":177,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:36:52.124+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:52.174+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:52.341+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":167,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:36:52.449+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":107,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:52.509+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:52.761+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":251,"影响行数":1}
{"level":"INFO","time":"2025-06-09T10:36:52.937+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":175,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T10:36:52.937+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-09T10:36:53.134+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-09T10:36:53.135+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-09T10:36:53.135+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-09T10:36:53.137+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-09T11:01:11.181+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` ORDER BY sort ASC, id DESC","耗时":49,"影响行数":8}
{"level":"INFO","time":"2025-06-09T11:01:11.334+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 155","耗时":152,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:11.514+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 128","耗时":179,"影响行数":2}
{"level":"INFO","time":"2025-06-09T11:01:11.664+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 123","耗时":150,"影响行数":3}
{"level":"INFO","time":"2025-06-09T11:01:11.735+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":498,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:11.797+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 112","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:11.899+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 111","耗时":101,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:11.932+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":197,"影响行数":5}
{"level":"INFO","time":"2025-06-09T11:01:12.097+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 83","耗时":198,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:12.206+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":204,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:12.245+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 75","耗时":148,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:12.353+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 10","耗时":147,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:12.412+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 74","耗时":167,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:12.574+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":187,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:12.710+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 20","耗时":136,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:12.947+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":197,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:13.068+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 30","耗时":120,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:13.264+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":143,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:13.437+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 40","耗时":173,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:13.633+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:13.833+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 50","耗时":199,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:14.000+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":117,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:14.184+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 60","耗时":183,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:14.737+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":167,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:14.884+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 70","耗时":146,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:15.080+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:15.244+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 80","耗时":163,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:15.612+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:15.786+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 90","耗时":173,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:15.963+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":147,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:16.158+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 100","耗时":195,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:16.347+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:16.468+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 110","耗时":120,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:16.832+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":208,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:16.991+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 120","耗时":158,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:17.178+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":142,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:17.333+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 130","耗时":154,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:17.515+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":153,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:17.653+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 140","耗时":138,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:18.000+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:18.114+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 150","耗时":113,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:18.284+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":133,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:18.464+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 160","耗时":180,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:18.663+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:18.824+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 170","耗时":160,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:19.013+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":151,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:19.197+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 180","耗时":183,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:19.413+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":184,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:19.532+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 190","耗时":119,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:19.738+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":174,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:19.897+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 200","耗时":159,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:20.071+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:20.249+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 210","耗时":178,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:20.663+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:20.766+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 220","耗时":103,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:20.981+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":177,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:21.213+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 230","耗时":232,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:21.402+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":156,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:21.565+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 240","耗时":162,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:21.713+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":118,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:21.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 250","耗时":122,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:22.030+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":142,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:22.186+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 260","耗时":155,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:22.373+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:22.564+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 270","耗时":191,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:22.730+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:22.865+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 280","耗时":134,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:23.037+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":150,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:23.231+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 290","耗时":193,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:23.448+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":183,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:23.631+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 300","耗时":183,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:23.823+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":156,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:24.023+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 310","耗时":199,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:24.221+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":174,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:24.399+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 320","耗时":177,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:24.553+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":128,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:24.734+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 330","耗时":181,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:24.913+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":139,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:25.101+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 340","耗时":188,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:25.293+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":147,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:25.452+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 350","耗时":159,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:25.689+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":191,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:25.884+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 360","耗时":194,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:26.053+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:26.215+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 370","耗时":161,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:26.422+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":168,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:26.535+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 380","耗时":113,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:01:26.993+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:01:27.213+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 390","耗时":219,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:02:08.688+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room`","耗时":68,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:02:08.870+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` ORDER BY sort ASC, id DESC LIMIT 10","耗时":181,"影响行数":10}
{"level":"INFO","time":"2025-06-09T11:03:00.068+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 3 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:09:02.716+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:02.854+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":119,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:09:03.012+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:03.097+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:03.224+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:09:03.358+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:03.534+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":176,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:03.719+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":184,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:03.895+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":175,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:09:04.118+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":222,"影响行数":3}
{"level":"INFO","time":"2025-06-09T11:09:04.215+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:04.448+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":232,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:09:04.756+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":306,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:04.886+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:05.055+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":167,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:09:05.244+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":189,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:05.335+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:05.525+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":190,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:09:05.680+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":155,"影响行数":3}
{"level":"INFO","time":"2025-06-09T11:09:05.759+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:05.933+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":172,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:09:06.090+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":156,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:06.147+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:06.293+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":146,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:09:06.661+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":367,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:06.770+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":108,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:07.025+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":254,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:09:07.195+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":169,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:07.325+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:07.456+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:09:07.660+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":203,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:09:07.661+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-09T11:09:07.818+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-09T11:09:07.818+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-09T11:09:07.819+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-09T11:09:07.820+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-09T11:21:28.423+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` ORDER BY sort ASC, id DESC","耗时":38,"影响行数":13}
{"level":"INFO","time":"2025-06-09T11:23:00.970+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 21 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":116,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:29:03.797+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` ORDER BY sort ASC, id DESC","耗时":67,"影响行数":8}
{"level":"INFO","time":"2025-06-09T11:29:03.938+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 155","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:29:04.051+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 128","耗时":112,"影响行数":2}
{"level":"INFO","time":"2025-06-09T11:29:04.146+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 123","耗时":94,"影响行数":3}
{"level":"INFO","time":"2025-06-09T11:29:04.239+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 112","耗时":92,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:29:04.239+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":397,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:29:04.328+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 111","耗时":89,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:29:04.334+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":94,"影响行数":5}
{"level":"INFO","time":"2025-06-09T11:29:04.434+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 83","耗时":106,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:29:04.498+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:29:04.544+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 75","耗时":109,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:29:04.595+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 10","耗时":97,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:29:04.641+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 74","耗时":96,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:29:04.731+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":105,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:29:04.911+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 20","耗时":179,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:29:05.018+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":86,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:29:05.126+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 30","耗时":107,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:29:05.236+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":83,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:29:05.334+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 40","耗时":98,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:29:05.486+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":117,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:29:05.614+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 50","耗时":127,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:29:05.767+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:29:05.879+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 60","耗时":111,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:29:06.037+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":128,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:29:06.138+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 70","耗时":100,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:29:06.273+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":107,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:29:06.341+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 80","耗时":67,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:29:06.493+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":121,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:29:06.594+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 90","耗时":100,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:29:06.729+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":106,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:29:06.791+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 100","耗时":61,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:31:18.469+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-09T11:31:18.469+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-09T11:31:18.470+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-09T11:31:22.544+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":55,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:22.651+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":80,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:31:22.779+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:22.845+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:22.941+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":96,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:31:23.039+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:23.137+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:23.199+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:23.301+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:31:23.368+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":66,"影响行数":3}
{"level":"INFO","time":"2025-06-09T11:31:23.405+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":37,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:23.502+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":95,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:31:23.598+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:23.635+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":36,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:23.742+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":106,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:31:23.860+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:23.895+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":35,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:23.992+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":95,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:31:24.092+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":100,"影响行数":3}
{"level":"INFO","time":"2025-06-09T11:31:24.155+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:24.278+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":123,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:31:24.365+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":86,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:24.404+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":37,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:24.481+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":77,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:31:24.582+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:24.621+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":39,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:24.727+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":105,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:31:24.824+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:24.868+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":44,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:24.955+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":85,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:31:25.053+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T11:31:25.054+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-09T11:31:25.141+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-09T11:31:25.142+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-09T11:31:25.142+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-09T11:31:25.143+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-09T11:33:28.340+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` ORDER BY sort ASC, id DESC","耗时":48,"影响行数":8}
{"level":"INFO","time":"2025-06-09T11:33:28.466+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 155","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:33:28.576+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 128","耗时":109,"影响行数":2}
{"level":"INFO","time":"2025-06-09T11:33:28.699+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 123","耗时":122,"影响行数":3}
{"level":"INFO","time":"2025-06-09T11:33:28.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":432,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:33:28.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 112","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:33:28.936+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 111","耗时":99,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:33:28.950+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":114,"影响行数":5}
{"level":"INFO","time":"2025-06-09T11:33:29.072+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 83","耗时":136,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:33:29.166+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 75","耗时":93,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:33:29.263+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 74","耗时":96,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:33:29.365+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:33:29.471+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10 OFFSET 10","耗时":104,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:33:31.650+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` ORDER BY sort ASC, id DESC","耗时":41,"影响行数":8}
{"level":"INFO","time":"2025-06-09T11:33:31.717+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corpid = ''","耗时":95,"影响行数":2}
{"level":"INFO","time":"2025-06-09T11:33:31.764+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 155","耗时":113,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:33:31.869+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 128","耗时":104,"影响行数":2}
{"level":"INFO","time":"2025-06-09T11:33:31.921+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` ORDER BY id DESC","耗时":296,"影响行数":4}
{"level":"INFO","time":"2025-06-09T11:33:31.970+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 123","耗时":101,"影响行数":3}
{"level":"INFO","time":"2025-06-09T11:33:32.104+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 112","耗时":133,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:33:32.241+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 111","耗时":137,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:33:32.520+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 83","耗时":279,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:33:32.616+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 75","耗时":95,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:33:32.721+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 74","耗时":104,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:33:48.184+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE corpid = '' AND room_name = '2568'","耗时":155,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:33:48.241+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT COALESCE(MAX(sort), 0) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":56,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:33:48.286+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT COALESCE(MAX(sort), 0) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":44,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:33:48.511+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`,`created_at`,`updated_at`) VALUES ('','2568',0,'','',1,'',1,'\"[\\\"https://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/canyin/2025-06-09/1ea429ebd582e709c1b27ccb4c9841ff.jpg\\\"]\"','',6,'',0,'','','','',0,'',0,0,1,0,0,0,0,0,0,'2025-06-09 11:33:48.286','2025-06-09 11:33:48.286')","耗时":224,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:33:50.639+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` ORDER BY sort ASC, id DESC","耗时":42,"影响行数":8}
{"level":"INFO","time":"2025-06-09T11:33:50.761+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 155","耗时":121,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:33:50.828+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":139,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:33:50.872+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 128","耗时":111,"影响行数":2}
{"level":"INFO","time":"2025-06-09T11:33:51.007+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 123","耗时":134,"影响行数":3}
{"level":"INFO","time":"2025-06-09T11:33:51.023+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":195,"影响行数":6}
{"level":"INFO","time":"2025-06-09T11:33:51.132+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 112","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-06-09T11:33:51.323+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 111","耗时":190,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:33:51.521+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 83","耗时":197,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:33:51.687+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 75","耗时":166,"影响行数":0}
{"level":"INFO","time":"2025-06-09T11:33:51.919+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT id, room_name FROM `eyc_meeting_room` WHERE group_id = 74","耗时":231,"影响行数":0}
{"level":"INFO","time":"2025-06-09T13:40:46.298+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 16 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":208,"影响行数":1}
{"level":"INFO","time":"2025-06-09T13:40:46.480+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=sort + 1,`updated_at`='2025-06-09 13:40:46.301' WHERE sort >= 1 AND sort < 3 AND group_id = 0 AND id != 16","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-06-09T13:40:46.681+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=1,`updated_at`='2025-06-09 13:40:46.48' WHERE id = 16","耗时":201,"影响行数":1}
{"level":"INFO","time":"2025-06-09T13:51:18.800+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":86,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:19.072+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":209,"影响行数":1}
{"level":"INFO","time":"2025-06-09T13:51:19.279+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":207,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:19.397+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:19.477+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":79,"影响行数":1}
{"level":"INFO","time":"2025-06-09T13:51:19.641+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":164,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:19.830+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":186,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:19.893+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:20.062+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":168,"影响行数":1}
{"level":"INFO","time":"2025-06-09T13:51:20.237+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":175,"影响行数":3}
{"level":"INFO","time":"2025-06-09T13:51:20.312+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:20.460+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":148,"影响行数":1}
{"level":"INFO","time":"2025-06-09T13:51:20.627+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":165,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:20.697+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:20.877+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-06-09T13:51:21.052+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":174,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:21.137+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:21.312+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":174,"影响行数":1}
{"level":"INFO","time":"2025-06-09T13:51:21.570+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":258,"影响行数":3}
{"level":"INFO","time":"2025-06-09T13:51:21.697+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:21.862+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-06-09T13:51:22.010+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":148,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:22.106+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:22.269+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-06-09T13:51:22.393+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:22.475+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:22.660+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":185,"影响行数":1}
{"level":"INFO","time":"2025-06-09T13:51:22.798+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:22.871+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:23.027+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-06-09T13:51:23.197+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":169,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T13:51:23.197+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-09T13:51:23.381+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-09T13:51:23.383+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-09T13:51:23.383+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-09T13:51:23.383+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-09T13:53:01.099+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 4 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":170,"影响行数":0}
{"level":"INFO","time":"2025-06-09T13:53:20.280+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 6 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":190,"影响行数":1}
{"level":"INFO","time":"2025-06-09T13:53:20.411+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=sort + 1,`updated_at`='2025-06-09 13:53:20.281' WHERE sort >= 2 AND sort < 4 AND group_id = 0 AND id != 6","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-09T13:53:20.660+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=2,`updated_at`='2025-06-09 13:53:20.412' WHERE id = 6","耗时":248,"影响行数":1}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              {"level":"INFO","time":"2025-06-09T14:07:46.440+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:47.316+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":104,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:07:47.428+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":111,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:47.482+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:47.586+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":103,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:07:47.681+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:47.810+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:47.855+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":44,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:47.970+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:07:48.072+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":102,"影响行数":3}
{"level":"INFO","time":"2025-06-09T14:07:48.113+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":40,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:48.195+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":81,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:07:48.295+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:48.360+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:48.466+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":105,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:07:48.602+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":135,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:48.640+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":37,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:48.758+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":116,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:07:48.875+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":116,"影响行数":3}
{"level":"INFO","time":"2025-06-09T14:07:48.923+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":47,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:49.020+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":96,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:07:49.152+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":132,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:49.185+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":32,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:49.317+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:07:49.449+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":131,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:49.511+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:49.635+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":123,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:07:49.750+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":114,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:49.831+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:49.935+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":104,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:07:50.066+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:07:50.067+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-09T14:07:50.168+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-09T14:07:50.169+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-09T14:07:50.169+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-09T14:07:50.171+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-09T14:12:57.057+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:12:57.161+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":91,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:12:57.286+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:12:57.341+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:12:57.450+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":108,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:12:57.613+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":162,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:12:57.727+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":114,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:12:57.784+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:12:57.926+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":142,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:12:58.056+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":130,"影响行数":3}
{"level":"INFO","time":"2025-06-09T14:12:58.104+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":47,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:12:58.256+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":151,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:12:58.419+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":162,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:12:58.501+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:12:58.608+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":106,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:12:58.762+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":153,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:12:58.817+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:12:58.946+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:12:59.061+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":115,"影响行数":3}
{"level":"INFO","time":"2025-06-09T14:12:59.139+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:12:59.291+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":151,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:12:59.479+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":187,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:12:59.548+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:12:59.686+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:12:59.812+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:12:59.881+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:00.009+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:13:00.138+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:00.234+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:00.384+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:13:00.517+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:00.519+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-09T14:13:00.629+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-09T14:13:00.630+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-09T14:13:00.630+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"FATAL","time":"2025-06-09T14:13:00.630+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"listen tcp :9505: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted."}
{"level":"INFO","time":"2025-06-09T14:13:30.432+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:30.602+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":143,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:13:30.785+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":182,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:30.850+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:30.962+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":111,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:13:31.049+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":86,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:31.147+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:31.187+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":40,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:31.307+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":118,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:13:31.387+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":80,"影响行数":3}
{"level":"INFO","time":"2025-06-09T14:13:31.441+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:31.552+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:13:31.682+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:31.771+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:31.882+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:13:31.977+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:32.052+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:32.159+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":106,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:13:32.329+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":170,"影响行数":3}
{"level":"INFO","time":"2025-06-09T14:13:32.372+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":41,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:32.472+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:13:32.570+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:32.603+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":33,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:32.737+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":133,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:13:32.834+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:32.882+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":47,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:32.992+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":109,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:13:33.098+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":105,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:33.128+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":28,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:33.272+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:13:33.360+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:13:33.360+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-09T14:13:33.522+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-09T14:13:33.523+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-09T14:13:33.523+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"FATAL","time":"2025-06-09T14:13:33.524+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"listen tcp :9505: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted."}
{"level":"INFO","time":"2025-06-09T14:19:40.811+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":41,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:40.926+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":87,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:19:41.079+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":152,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:41.144+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:41.236+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":91,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:19:41.320+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:41.429+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":108,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:41.480+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":50,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:41.594+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":113,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:19:41.699+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":104,"影响行数":3}
{"level":"INFO","time":"2025-06-09T14:19:41.754+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:41.840+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":85,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:19:41.961+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":121,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:42.015+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:42.102+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":87,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:19:42.212+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:42.252+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":38,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:42.344+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":92,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:19:42.526+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":182,"影响行数":3}
{"level":"INFO","time":"2025-06-09T14:19:42.661+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":134,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:42.747+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":85,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:19:42.851+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":103,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:42.907+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:43.015+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":108,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:19:43.099+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:43.146+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:43.234+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":87,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:19:43.334+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:43.369+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":35,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:43.444+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":74,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:19:43.528+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:19:43.529+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-09T14:19:43.634+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-09T14:19:43.635+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-09T14:19:43.636+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-09T14:19:43.636+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-09T14:28:26.128+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-09T14:28:26.128+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-09T14:28:26.128+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-09T14:28:30.556+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":91,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:30.762+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":194,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:28:30.957+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":195,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:31.047+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:31.238+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":190,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:28:31.397+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:31.558+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:31.679+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":121,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:31.826+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:28:31.988+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":162,"影响行数":3}
{"level":"INFO","time":"2025-06-09T14:28:32.150+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":162,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:32.491+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":340,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:28:32.660+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":168,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:32.748+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:32.878+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:28:33.060+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":181,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:33.177+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:33.266+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":88,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:28:33.455+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":188,"影响行数":3}
{"level":"INFO","time":"2025-06-09T14:28:33.583+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":127,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:33.790+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":207,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:28:33.925+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":134,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:34.038+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":112,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:34.140+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":102,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:28:34.345+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":205,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:34.433+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":86,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:34.590+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:28:34.780+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":190,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:34.878+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:35.085+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":206,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:28:35.229+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:28:35.230+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-09T14:28:35.465+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-09T14:28:35.465+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-09T14:28:35.465+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-09T14:28:35.466+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-09T14:29:46.925+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-09T14:29:46.925+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-09T14:29:46.925+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-09T14:29:49.159+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:49.273+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":102,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:29:49.421+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":146,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:49.490+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:49.597+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":107,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:29:49.718+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:49.831+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":112,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:49.898+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:50.022+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:29:50.226+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":203,"影响行数":3}
{"level":"INFO","time":"2025-06-09T14:29:50.306+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:50.587+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":280,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:29:50.740+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":152,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:50.819+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:51.013+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":193,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:29:51.181+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":168,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:51.326+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:51.487+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":160,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:29:51.556+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":68,"影响行数":3}
{"level":"INFO","time":"2025-06-09T14:29:51.638+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":82,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:51.816+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":178,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:29:51.937+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:51.996+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:52.136+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":139,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:29:52.311+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":175,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:52.406+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:52.592+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":185,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:29:52.731+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":138,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:52.777+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":45,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:52.890+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":111,"影响行数":1}
{"level":"INFO","time":"2025-06-09T14:29:53.038+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":147,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T14:29:53.039+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-09T14:29:53.152+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-09T14:29:53.153+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-09T14:29:53.153+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-09T14:29:53.154+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-09T14:36:41.678+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 21 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:25:33.407+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-09T15:25:33.408+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-09T15:25:33.408+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-09T15:25:38.490+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:38.675+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":156,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:25:38.822+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":146,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:38.885+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:39.010+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:25:39.140+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:39.342+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":201,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:39.575+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":231,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:39.735+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":160,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:25:39.838+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":102,"影响行数":3}
{"level":"INFO","time":"2025-06-09T15:25:39.926+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:40.067+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:25:40.201+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:40.253+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":51,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:40.390+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:25:40.505+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":114,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:40.637+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":132,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:40.767+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:25:40.895+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":127,"影响行数":3}
{"level":"INFO","time":"2025-06-09T15:25:40.983+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:41.135+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":151,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:25:41.267+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":131,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:41.322+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:41.400+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":77,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:25:41.501+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:41.533+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":32,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:41.632+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":98,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:25:41.763+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:41.830+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:41.987+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":156,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:25:42.106+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:25:42.106+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-09T15:25:42.231+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-09T15:25:42.234+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-09T15:25:42.234+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-09T15:25:42.236+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-09T15:33:31.818+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-09T15:33:31.819+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-09T15:33:31.819+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-09T15:33:35.508+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:35.670+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":146,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:33:35.830+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:35.925+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:36.066+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:33:36.215+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:36.370+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":154,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:36.448+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:36.566+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":118,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:33:36.695+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":128,"影响行数":3}
{"level":"INFO","time":"2025-06-09T15:33:36.730+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":34,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:36.863+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":133,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:33:37.020+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":156,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:37.090+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:37.235+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:33:37.384+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:37.490+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:37.615+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:33:37.748+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":131,"影响行数":3}
{"level":"INFO","time":"2025-06-09T15:33:37.850+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":102,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:37.975+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":123,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:33:38.135+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:38.174+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":38,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:38.295+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:33:38.425+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:38.485+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:38.648+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:33:38.834+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":185,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:38.905+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:39.032+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":126,"影响行数":1}
{"level":"INFO","time":"2025-06-09T15:33:39.228+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":195,"影响行数":-1}
{"level":"INFO","time":"2025-06-09T15:33:39.229+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-09T15:33:39.378+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-09T15:33:39.379+0800","caller":"eyc3_meeting/main.go:59","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-09T15:33:39.379+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-09T15:33:39.381+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-09T15:34:06.650+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 21 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-06-09T18:34:01.665+0800","caller":"eyc3_meeting/main.go:69","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-09T18:34:01.665+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-09T18:34:01.666+0800","caller":"eyc3_meeting/main.go:55","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
