package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"eyc3_meeting/config"
	"eyc3_meeting/internal/model"
	"eyc3_meeting/internal/pkg/cron"
	"eyc3_meeting/internal/pkg/logger"
	"eyc3_meeting/internal/pkg/mqtt"
	"eyc3_meeting/internal/pkg/redis"
	"eyc3_meeting/internal/router"
	"github.com/cloudwego/hertz/pkg/app/server"
	"github.com/cloudwego/hertz/pkg/common/hlog"
)

func main() {
	// 初始化配置
	config.Init()

	// 初始化日志
	logger.Init()

	// 替换hertz默认日志
	hlog.SetLogger(logger.HertzLog)

	// 初始化数据库
	model.Init()

	// 初始化Redis
	redis.Init()

	// 初始化MQTT
	// mqtt.Init()

	// 初始化定时任务
	//cron.Init()

	// 初始化设备定时任务
	//service.InitDeviceScheduler()

	// 创建Hertz服务器
	h := server.Default(
		server.WithHostPorts(fmt.Sprintf(":%d", config.AppConfig.Server.Port)),
	)

	// 注册路由
	router.Register(h)

	// 启动服务器（非阻塞）
	go func() {
		if err := h.Run(); err != nil {
			logger.Fatal("启动服务器失败", logger.Error2(err))
		}
	}()

	logger.Info("服务已启动",
		logger.Int("port", config.AppConfig.Server.Port),
		logger.String("mode", config.AppConfig.Server.Mode),
	)

	// 等待中断信号优雅关闭
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("正在关闭服务...")

	// 关闭服务器
	if err := h.Shutdown(context.Background()); err != nil {
		logger.Error("关闭服务器失败", logger.Error2(err))
	}

	// 关闭定时任务
	cron.Stop()

	// 关闭MQTT连接
	mqtt.Close()

	// 关闭数据库连接
	model.Close()

	logger.Info("服务已关闭")
}
