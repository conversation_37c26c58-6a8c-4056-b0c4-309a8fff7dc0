package meeting

import (
	"eyc3_meeting/internal/controller"
	"eyc3_meeting/internal/middleware"

	"github.com/cloudwego/hertz/pkg/app/server"
)

// RegisterFacilityRoutes 注册设施相关接口
func RegisterFacilityRoutes(r *server.Hertz) {
	facility := r.Group("/facility", middleware.JWT())
	// 设施相关接口
	facility.POST("/post_add", controller.AddEycMeetingFacility)
	facility.GET("/post_del", controller.DeleteEycMeetingFacility)
	facility.POST("/get_ls", controller.GetEycMeetingFacilityList)
	facility.GET("/get_all", controller.GetAllEycMeetingFacilities)
}
