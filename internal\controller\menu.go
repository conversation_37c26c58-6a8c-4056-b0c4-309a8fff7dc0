package controller

//
//import (
//	"eyc3_meeting/internal/model"
//	"eyc3_meeting/internal/service"
//)
//
//// MenuController 菜单控制器
//type MenuController struct {
//	BaseController[model.Menu, *service.MenuService]
//}
//
//// 全局菜单控制器实例
//var (
//	menuService    = new(service.MenuService)
//	menuController = MenuController{BaseController: BaseController[model.Menu, *service.MenuService]{Service: menuService}}
//)
//
//// 标准CRUD接口实现
//var (
//	// GetMenuList 获取菜单项列表
//	GetMenuList = menuController.GetList()
//
//	// GetAllMenus 获取所有菜单
//	GetAllMenus = menuController.GetAll()
//
//	// GetMenuinfo 获取单个菜单项
//	GetMenuinfo = menuController.GetInfo()
//
//	// AddMenu 添加菜单项
//	AddMenu = menuController.Add(true, "appid:u,name,path,component,title,icon,parent_id:u,is_home:i,hide:b,order:i")
//
//	// ModifyMenu 更新菜单项
//	ModifyMenu = menuController.Modify()
//
//	// DeleteMenu 删除菜单项
//	DeleteMenu = menuController.Delete()
//)
