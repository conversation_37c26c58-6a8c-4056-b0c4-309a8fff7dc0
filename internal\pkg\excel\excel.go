package excel

import (
	"bytes"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
)

// 创建Excel文件的临时目录
const TempDir = "./temp"

// 初始化临时目录
func init() {
	if _, err := os.Stat(TempDir); os.IsNotExist(err) {
		err := os.MkdirAll(TempDir, os.ModePerm)
		if err != nil {
			panic(fmt.Sprintf("创建Excel临时目录失败: %v", err))
		}
	}
}

// ExcelHeader 表头定义
type ExcelHeader struct {
	Title string // 显示标题
	Field string // 字段名
	Width int    // 列宽
	Type  string // 数据类型: string, number, boolean, date
}

// ExportToExcel 通用Excel导出函数
func ExportToExcel(headers []ExcelHeader, data []map[string]interface{}, fileNamePrefix string) (string, error) {
	// 创建新的Excel文件
	f := excelize.NewFile()
	defer f.Close()

	// 设置表头样式
	headerStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true, Size: 11},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"#FFFFCC"}, Pattern: 1},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
	})
	if err != nil {
		return "", fmt.Errorf("创建表头样式失败: %w", err)
	}

	// 写入表头
	sheetName := "Sheet1"
	f.SetSheetName("Sheet1", sheetName)
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue(sheetName, cell, header.Title)
		f.SetCellStyle(sheetName, cell, cell, headerStyle)
		if header.Width > 0 {
			f.SetColWidth(sheetName, string('A'+i), string('A'+i), float64(header.Width))
		}
	}

	// 写入数据
	for i, row := range data {
		rowNum := i + 2 // 数据从第2行开始
		for j, header := range headers {
			cell := fmt.Sprintf("%c%d", 'A'+j, rowNum)

			if value, exists := row[header.Field]; exists {
				switch header.Type {
				case "boolean":
					boolValue := false
					switch v := value.(type) {
					case bool:
						boolValue = v
					case int8:
						boolValue = v == 1
					case int:
						boolValue = v == 1
					case string:
						boolValue = v == "1" || v == "true" || v == "TRUE" || v == "是"
					}

					if boolValue {
						f.SetCellValue("Sheet1", cell, "是")
					} else {
						f.SetCellValue("Sheet1", cell, "否")
					}
				default:
					f.SetCellValue("Sheet1", cell, value)
				}
			}
		}
	}

	// 强制使用当前实际时间获取日期
	year, month, day := time.Now().Date()
	currentDate := fmt.Sprintf("%04d%02d%02d", year, int(month), day)

	// 确保fileNamePrefix不为空，避免文件名异常
	if fileNamePrefix == "" {
		fileNamePrefix = "数据导出"
	}

	// 使用中文文件名
	filename := fmt.Sprintf("%s%s.xlsx", currentDate, fileNamePrefix)
	filePath := filepath.Join(TempDir, filename)

	// 保存Excel文件
	if err := f.SaveAs(filePath); err != nil {
		return "", fmt.Errorf("保存Excel文件失败: %w", err)
	}

	return filePath, nil
}

// ValidatorFunc 验证器函数类型
type ValidatorFunc func(data map[string]interface{}) error

// GenericExcelService 通用Excel导入导出服务
type GenericExcelService struct {
	Headers      []ExcelHeader
	SheetName    string
	FileName     string
	Validator    ValidatorFunc
	ErrorBuilder *strings.Builder
}

// NewGenericExcelService 创建一个新的通用Excel服务
func NewGenericExcelService(headers []ExcelHeader, fileName string) *GenericExcelService {
	return &GenericExcelService{
		Headers:      headers,
		SheetName:    "Sheet1",
		FileName:     fileName,
		ErrorBuilder: &strings.Builder{},
	}
}

// SetValidator 设置验证器
func (s *GenericExcelService) SetValidator(validator ValidatorFunc) {
	s.Validator = validator
}

// Export 导出数据到Excel并保存为临时文件
func (s *GenericExcelService) Export(data []map[string]interface{}) (string, error) {
	return ExportToExcel(s.Headers, data, s.FileName)
}

// ExportToBuffer 导出数据到Excel并返回内存缓冲区
func (s *GenericExcelService) ExportToBuffer(data []map[string]interface{}) (*bytes.Buffer, error) {
	f := excelize.NewFile()
	sheetName := s.SheetName
	f.SetSheetName("Sheet1", sheetName)

	// 设置表头样式和列宽
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true},
	})
	for i, header := range s.Headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		f.SetCellValue(sheetName, cell, header.Title)
		f.SetCellStyle(sheetName, cell, cell, headerStyle)
		if header.Width > 0 {
			f.SetColWidth(sheetName, string(rune('A'+i)), string(rune('A'+i)), float64(header.Width))
		}
	}

	// 写入数据
	for i, row := range data {
		rowNum := i + 2
		for j, header := range s.Headers {
			if value, ok := row[header.Field]; ok {
				cell, _ := excelize.CoordinatesToCellName(j+1, rowNum)
				f.SetCellValue(sheetName, cell, value)
			}
		}
	}

	// 将文件写入缓冲区
	buf, err := f.WriteToBuffer()
	if err != nil {
		return nil, fmt.Errorf("写入excel缓冲区失败: %w", err)
	}
	return buf, nil
}

// Import 从Excel导入数据
func (s *GenericExcelService) Import(reader io.Reader) ([]map[string]interface{}, []string, error) {
	rows, err := ReadExcelFile(reader)
	if err != nil {
		return nil, nil, err
	}

	var data []map[string]interface{}
	var errorMessages []string
	headerMap := make(map[string]string)
	for _, h := range s.Headers {
		headerMap[h.Title] = h.Field
	}

	// Excel的表头行
	excelHeaders := rows[0]
	// 期望的表头字段名
	expectedFields := make(map[string]bool)
	for _, h := range s.Headers {
		expectedFields[h.Field] = true
	}

	for i, row := range rows {
		if i == 0 { // 跳过表头行
			continue
		}
		rowNum := i + 1
		rowData := make(map[string]interface{})
		for j, cellValue := range row {
			if j < len(excelHeaders) {
				headerTitle := excelHeaders[j]
				if fieldName, ok := headerMap[headerTitle]; ok {
					rowData[fieldName] = cellValue
				}
			}
		}
		// 补全excel中不存在的列
		for field := range expectedFields {
			if _, exists := rowData[field]; !exists {
				rowData[field] = "" // or some default value
			}
		}

		if s.Validator != nil {
			if err := s.Validator(rowData); err != nil {
				errorMessages = append(errorMessages, fmt.Sprintf("第%d行: %v", rowNum, err))
				continue
			}
		}
		data = append(data, rowData)
	}

	return data, errorMessages, nil
}

// ReadExcelFile 通用Excel读取函数
// 返回的数据是一个二维数组，包括表头和数据行
func ReadExcelFile(reader io.Reader) ([][]string, error) {
	f, err := excelize.OpenReader(reader)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文件失败: %w", err)
	}
	defer f.Close()

	// 假设只有一个工作表或使用第一个工作表
	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		return nil, fmt.Errorf("Excel文件中没有工作表")
	}

	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("读取工作表数据失败: %w", err)
	}

	if len(rows) < 2 {
		return nil, fmt.Errorf("Excel文件至少需要包含表头和一行数据")
	}

	return rows, nil
}
