package service

import (
	"eyc3_meeting/internal/model"
	"eyc3_meeting/internal/pkg/logger"
	"eyc3_meeting/internal/pkg/redis"
	"strconv"
)

// GetHelloMessage 获取hello消息
func GetHelloMessage() (string, error) {
	// 尝试从Redis获取
	message, err := redis.Get2("hello_message")
	if err == nil && message != "" {
		logger.Info("从Redis获取Hello信息")
		return message, nil
	}

	// 访问数据库示例
	user, err := model.GetUserByID(strconv.Itoa(1))
	if err != nil {
		logger.Error("查询用户失败", logger.Error2(err))
		return "", err
	}

	var username string
	if user != nil {
		username = user.Username
		logger.Info("从数据库获取用户", logger.String("username", username))
	} else {
		username = "Guest"
	}

	// 返回消息
	message = "hello world"

	// 缓存到Redis
	err = redis.Set("hello_message", message, 300) // 缓存5分钟
	if err != nil {
		logger.Error("缓存Hello信息失败", logger.Error2(err))
	}

	return message, nil
}
