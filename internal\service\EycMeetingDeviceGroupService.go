package service

import (
	"errors"
	"eyc3_meeting/internal/model"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/cloudwego/hertz/pkg/app"
	"gorm.io/gorm"
)

// DeviceGroupWithCount 是包含了设备数量的设备分组信息结构体
type DeviceGroupWithCount struct {
	model.EycMeetingDeviceGroup
	DeviceCount int `json:"device_count"`
}

// EycMeetingDeviceGroupService 设备分组服务
// 负责设备分组的增删改查及相关业务逻辑
type EycMeetingDeviceGroupService struct {
	BaseServiceImpl[model.EycMeetingDeviceGroup]
}

// DeviceGroupDetails 包含设备列表的分组详情模型
type DeviceGroupDetails struct {
	model.EycMeetingDeviceGroup
	Devices []model.EycMeetingDevice `json:"devices"`
}

// GroupWithDeviceCount 包含设备分组和设备数量的结构体
type GroupWithDeviceCount struct {
	model.EycMeetingDeviceGroup
	DeviceCount int `json:"device_count"`
}

// AddService 新增设备分组
func (s *EycMeetingDeviceGroupService) AddService(ac *app.RequestContext, data interface{}) (uint, error) {
	m, ok := data.(map[string]interface{})
	if !ok {
		return 0, fmt.Errorf("参数类型错误")
	}
	group := &model.EycMeetingDeviceGroup{}

	// 解析参数
	if title, ok := m["title"].(string); ok && title != "" {
		group.Title = title
	} else {
		return 0, errors.New("分组名称不能为空")
	}
	if deviceType, ok := m["device_type"].(string); ok && deviceType != "" {
		group.DeviceType = deviceType
	} else {
		return 0, errors.New("设备分组类型不能为空")
	}
	if deviceIds, ok := m["device_ids"].(string); ok {
		group.DeviceIds = deviceIds
	}

	// 从JWT上下文中获取企业ID
	corpIDValue, exists := ac.Get("corp_id")
	if !exists {
		return 0, errors.New("无法获取企业ID，请检查登录状态")
	}
	corpID, ok := corpIDValue.(string)
	if !ok || corpID == "" {
		return 0, errors.New("无效的企业ID")
	}
	group.Corpid = corpID

	// 开启事务
	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 新增逻辑：检查设备是否已在其他分组
	deviceIDsToCheck := s.parseDeviceIDs(group.DeviceIds)
	if len(deviceIDsToCheck) > 0 {
		for _, deviceID := range deviceIDsToCheck {
			var conflictingGroup model.EycMeetingDeviceGroup
			err := tx.Model(&model.EycMeetingDeviceGroup{}).
				Where("corp_id = ? AND FIND_IN_SET(?, device_ids)", corpID, deviceID).
				First(&conflictingGroup).Error

			if err == nil { // 找到了冲突的分组
				var conflictingDevice model.EycMeetingDevice
				if tx.First(&conflictingDevice, deviceID).Error == nil {
					tx.Rollback()
					return 0, fmt.Errorf("设备 '%s' 已存在于分组 '%s' 中，不能重复添加", conflictingDevice.Title, conflictingGroup.Title)
				}
				// 如果设备信息都找不到了，也返回一个明确的错误
				tx.Rollback()
				return 0, fmt.Errorf("设备ID '%d' 已被其他分组关联，但无法获取该设备信息", deviceID)
			}

			if !errors.Is(err, gorm.ErrRecordNotFound) {
				// 如果是除了"没找到"之外的其他数据库错误
				tx.Rollback()
				return 0, fmt.Errorf("检查设备归属时发生数据库错误: %w", err)
			}
		}
	}

	// 检查重名
	var count int64
	if err := tx.Model(&model.EycMeetingDeviceGroup{}).
		Where("corp_id = ? AND title = ?", group.Corpid, group.Title).
		Count(&count).Error; err != nil {
		tx.Rollback()
		return 0, fmt.Errorf("检查分组名称唯一性失败: %w", err)
	}
	if count > 0 {
		tx.Rollback()
		return 0, errors.New("分组名称已存在")
	}

	// 设置时间
	now := time.Now()
	group.CreatedAt = now
	group.UpdatedAt = now

	// 创建分组记录
	if err := tx.Create(group).Error; err != nil {
		tx.Rollback()
		return 0, err
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return 0, err
	}

	return uint(group.ID), nil
}

// ModifyGroupWithForce 修改设备分组，并支持强制移动设备
func (s *EycMeetingDeviceGroupService) ModifyGroupWithForce(ac *app.RequestContext, id uint, data interface{}, force bool) error {
	m, ok := data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("参数类型错误")
	}

	// 检查记录是否存在
	var existingGroup model.EycMeetingDeviceGroup
	if err := model.DB.First(&existingGroup, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("设备分组不存在")
		}
		return err
	}

	// 解析参数
	if title, ok := m["title"].(string); ok && title != "" {
		existingGroup.Title = title
	} else {
		return errors.New("分组名称不能为空")
	}
	if deviceType, ok := m["device_type"].(string); ok {
		existingGroup.DeviceType = deviceType
	}
	if deviceIds, ok := m["device_ids"].(string); ok {
		existingGroup.DeviceIds = deviceIds
	}

	// 开启事务
	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 新增逻辑：检查要关联的设备是否已在其他分组
	deviceIDsToCheck := s.parseDeviceIDs(existingGroup.DeviceIds)
	if len(deviceIDsToCheck) > 0 {
		for _, deviceID := range deviceIDsToCheck {
			var conflictingGroup model.EycMeetingDeviceGroup
			err := tx.Model(&model.EycMeetingDeviceGroup{}).
				Where("id != ? AND corp_id = ? AND FIND_IN_SET(?, device_ids)", id, existingGroup.Corpid, deviceID).
				First(&conflictingGroup).Error

			if err == nil { // 找到了冲突的分组
				if !force {
					// 非强制模式下，直接报错
					var conflictingDevice model.EycMeetingDevice
					if tx.First(&conflictingDevice, deviceID).Error == nil {
						tx.Rollback()
						return fmt.Errorf("设备 '%s' 已存在于分组 '%s' 中，不能重复添加", conflictingDevice.Title, conflictingGroup.Title)
					}
					tx.Rollback()
					return fmt.Errorf("设备ID '%d' 已被其他分组关联，但无法获取该设备信息", deviceID)
				}

				// 强制模式下，从旧分组中移除
				conflictingIdStr := strconv.Itoa(deviceID)
				oldIds := strings.Split(conflictingGroup.DeviceIds, ",")
				newOldIds := make([]string, 0, len(oldIds)-1)
				for _, oldId := range oldIds {
					if oldId != conflictingIdStr {
						newOldIds = append(newOldIds, oldId)
					}
				}
				conflictingGroup.DeviceIds = strings.Join(newOldIds, ",")
				if err := tx.Save(&conflictingGroup).Error; err != nil {
					tx.Rollback()
					return fmt.Errorf("从旧分组 '%s' 中移除设备失败: %w", conflictingGroup.Title, err)
				}
			} else if !errors.Is(err, gorm.ErrRecordNotFound) {
				// 如果是除了"没找到"之外的其他数据库错误
				tx.Rollback()
				return fmt.Errorf("检查设备归属时发生数据库错误: %w", err)
			}
		}
	}

	// 检查重名（排除自身）
	var count int64
	if err := tx.Model(&model.EycMeetingDeviceGroup{}).
		Where("corp_id = ? AND title = ? AND id != ?", existingGroup.Corpid, existingGroup.Title, id).
		Count(&count).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("检查分组名称唯一性失败: %w", err)
	}
	if count > 0 {
		tx.Rollback()
		return errors.New("分组名称已存在")
	}

	// 更新分组信息
	existingGroup.UpdatedAt = time.Now()
	updateData := map[string]interface{}{
		"title":       existingGroup.Title,
		"device_type": existingGroup.DeviceType,
		"device_ids":  existingGroup.DeviceIds,
		"updated_at":  existingGroup.UpdatedAt,
	}
	if err := tx.Model(&model.EycMeetingDeviceGroup{}).Where("id = ?", id).Updates(updateData).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// ModifyService 修改设备分组 - 遵守BaseService接口
func (s *EycMeetingDeviceGroupService) ModifyService(ac *app.RequestContext, id uint, data interface{}) error {
	// 对于设备分组，修改操作有复杂逻辑（强制移动），直接调用基础实现可能不安全。
	// 这里返回一个错误，强制要求调用 ModifyGroupWithForce。
	// 或者，我们可以实现一个不带force的默认行为。为了安全，先返回错误。
	return errors.New("请使用支持强制移动的专用接口进行修改")
}

// DeleteService 删除设备分组
func (s *EycMeetingDeviceGroupService) DeleteService(ac *app.RequestContext, id uint) error {
	// 检查记录是否存在
	var group model.EycMeetingDeviceGroup
	if err := model.DB.First(&group, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("设备分组不存在")
		}
		return err
	}

	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除分组
	if err := tx.Delete(&model.EycMeetingDeviceGroup{}, id).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// GetInfoService 获取单个设备分组详情
func (s *EycMeetingDeviceGroupService) GetInfoService(ac *app.RequestContext, id uint) (model.EycMeetingDeviceGroup, bool, error) {
	return s.BaseServiceImpl.GetInfoService(ac, id)
}

// GetGroupDetailsService 获取单个设备分组详情，包含设备列表并支持搜索和排序
// 该方法经过重构，将搜索和排序下推到数据库层面，以提高性能。
func (s *EycMeetingDeviceGroupService) GetGroupDetailsService(ac *app.RequestContext, id uint, keyword string, sortType string) (*DeviceGroupDetails, error) {
	// 1. 获取分组信息
	var group model.EycMeetingDeviceGroup
	if err := model.DB.First(&group, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("设备分组不存在")
		}
		return nil, err
	}

	details := &DeviceGroupDetails{
		EycMeetingDeviceGroup: group,
		Devices:               []model.EycMeetingDevice{}, // 初始化，避免返回null
	}

	// 2. 获取分组内的设备ID
	deviceIDs := s.parseDeviceIDs(group.DeviceIds)
	if len(deviceIDs) == 0 {
		return details, nil // 分组内没有设备，直接返回
	}

	// 3. 构建数据库查询
	query := model.DB.Model(&model.EycMeetingDevice{}).Where("id IN ?", deviceIDs)

	// 4. 应用搜索条件 (数据库层面)
	if keyword != "" {
		query = query.Where("title LIKE ?", "%"+keyword+"%")
	}

	// 5. 应用排序条件 (数据库层面)
	// 排序规则: name_asc, time_asc, time_desc
	switch sortType {
	case "time_asc":
		query = query.Order("created_at asc")
	case "time_desc":
		query = query.Order("created_at desc")
	case "name_asc":
		// 默认按名称升序 (考虑中文拼音排序)
		query = query.Order("CONVERT(title USING gbk) asc")
	default:
		// 默认按名称升序
		query = query.Order("CONVERT(title USING gbk) asc")
	}

	// 6. 执行查询
	var devices []model.EycMeetingDevice
	if err := query.Find(&devices).Error; err != nil {
		return nil, fmt.Errorf("查询分组内设备失败: %w", err)
	}

	details.Devices = devices
	return details, nil
}

// GetListService 获取设备分组列表
func (s *EycMeetingDeviceGroupService) GetListService(ac *app.RequestContext, page, pageSize int, fields map[string]string, orderBy string) (model.PageResult[model.EycMeetingDeviceGroup], error) {
	var groups []model.EycMeetingDeviceGroup
	var total int64
	db := model.DB.Model(&model.EycMeetingDeviceGroup{})

	// 从JWT上下文中获取企业ID
	corpID, exists := ac.Get("corp_id")
	if !exists {
		return model.PageResult[model.EycMeetingDeviceGroup]{}, errors.New("无法获取企业ID")
	}
	db = db.Where("corp_id = ?", corpID)

	// 构建查询条件
	if keyword, ok := fields["keyword"]; ok && keyword != "" {
		db = db.Where("title LIKE ?", "%"+keyword+"%")
	}
	if deviceType, ok := fields["device_type"]; ok && deviceType != "" {
		db = db.Where("device_type = ?", deviceType)
	}

	if err := db.Count(&total).Error; err != nil {
		return model.PageResult[model.EycMeetingDeviceGroup]{}, err
	}

	if orderBy == "" {
		orderBy = "id DESC"
	}
	db = db.Order(orderBy)

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	if err := db.Find(&groups).Error; err != nil {
		return model.PageResult[model.EycMeetingDeviceGroup]{}, err
	}

	return model.PageResult[model.EycMeetingDeviceGroup]{
		Items: groups,
		Total: total,
	}, nil
}

// GetAllService 查询全部分组
func (s *EycMeetingDeviceGroupService) GetAllService(ac *app.RequestContext, fields map[string]string, orderBy string) ([]model.EycMeetingDeviceGroup, error) {
	return s.BaseServiceImpl.GetAllService(ac, fields, orderBy)
}

// parseDeviceIDs 是一个辅助函数，用于将逗号分隔的ID字符串解析为整数切片。
func (s *EycMeetingDeviceGroupService) parseDeviceIDs(deviceIdsStr string) []int {
	if deviceIdsStr == "" {
		return []int{}
	}
	idStrs := strings.Split(deviceIdsStr, ",")
	ids := make([]int, 0, len(idStrs))
	for _, idStr := range idStrs {
		if id, err := strconv.Atoi(strings.TrimSpace(idStr)); err == nil {
			ids = append(ids, id)
		}
	}
	return ids
}

// GetListWithCount 获取带设备计数的设备分组列表
func (s *EycMeetingDeviceGroupService) GetListWithCount(page, pageSize int, fields map[string]string, orderBy string) (model.PageResult[DeviceGroupWithCount], error) {
	var result model.PageResult[DeviceGroupWithCount]

	// 调用基础服务获取原始的分组列表
	groupsPage, err := s.BaseServiceImpl.GetListService(nil, page, pageSize, fields, orderBy)
	if err != nil {
		return result, err
	}

	// 创建用于返回的结果切片
	itemsWithCount := make([]DeviceGroupWithCount, 0, len(groupsPage.Items))

	// 遍历分组，计算设备数量
	for _, group := range groupsPage.Items {
		var deviceCount int
		if group.DeviceIds != "" {
			ids := strings.Split(group.DeviceIds, ",")
			validIds := 0
			for _, id := range ids {
				if strings.TrimSpace(id) != "" {
					validIds++
				}
			}
			deviceCount = validIds
		}

		itemsWithCount = append(itemsWithCount, DeviceGroupWithCount{
			EycMeetingDeviceGroup: group,
			DeviceCount:           deviceCount,
		})
	}

	result.Total = groupsPage.Total
	result.Items = itemsWithCount

	return result, nil
}
