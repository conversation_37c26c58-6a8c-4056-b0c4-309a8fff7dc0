package model

import (
	"context"
	"errors"
	"fmt"
	"time"

	"eyc3_meeting/internal/pkg/logger"

	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"
)

// GormLogger 实现gorm的日志接口
type GormLogger struct {
	SlowThreshold        time.Duration
	IgnoreRecordNotFound bool
	LogLevel             gormlogger.LogLevel
}

// NewGormLogger 创建GORM日志适配器
func NewGormLogger() *GormLogger {
	return &GormLogger{
		SlowThreshold:        time.Second, // 慢查询阈值
		IgnoreRecordNotFound: true,        // 是否忽略记录未找到错误
		LogLevel:             gormlogger.Info,
	}
}

// LogMode 设置日志级别
func (l *GormLogger) LogMode(level gormlogger.LogLevel) gormlogger.Interface {
	newLogger := *l
	newLogger.LogLevel = level
	return &newLogger
}

// Info 实现Info级别日志
func (l *GormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= gormlogger.Info {
		logger.Info(fmt.Sprintf(msg, data...))
	}
}

// Warn 实现Warn级别日志
func (l *GormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= gormlogger.Warn {
		logger.Warn(fmt.Sprintf(msg, data...))
	}
}

// Error 实现Error级别日志
func (l *GormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= gormlogger.Error {
		logger.Error(fmt.Sprintf(msg, data...))
	}
}

// Trace 实现SQL跟踪日志
func (l *GormLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	if l.LogLevel <= gormlogger.Silent {
		return
	}

	// 计算耗时
	elapsed := time.Since(begin)

	// 获取SQL和受影响的行数
	sql, rows := fc()

	// 根据不同情况记录日志
	switch {
	case err != nil && l.LogLevel >= gormlogger.Error && (!errors.Is(err, gorm.ErrRecordNotFound) || !l.IgnoreRecordNotFound):
		// 错误日志
		logger.Error("GORM错误",
			logger.String("sql", sql),
			logger.Float64("耗时", float64(elapsed.Milliseconds())),
			logger.Error2(err))
	case elapsed > l.SlowThreshold && l.SlowThreshold != 0 && l.LogLevel >= gormlogger.Warn:
		// 慢查询警告
		logger.Warn("GORM慢查询",
			logger.String("sql", sql),
			logger.Float64("耗时", float64(elapsed.Milliseconds())),
			logger.Int("影响行数", int(rows)))
	case l.LogLevel == gormlogger.Info:
		// 普通信息
		logger.Info("GORM查询",
			logger.String("sql", sql),
			logger.Float64("耗时", float64(elapsed.Milliseconds())),
			logger.Int("影响行数", int(rows)))
	}
}
