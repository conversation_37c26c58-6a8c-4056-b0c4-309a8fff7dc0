package model

import (
	"eyc3_meeting/internal/pkg/logger"
	"fmt"
	"time"
)

const TableNameEycMeetingFacility = "eyc_meeting_facility"

// EycMeetingFacility 设施表
type EycMeetingFacility struct {
	ID        int       `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`                                   // 设施ID
	Corpid    string    `gorm:"column:corp_id;not null;comment:企业ID" json:"corp_id"`                                  // 企业ID
	Name      string    `gorm:"column:name;not null;comment:设施名称" json:"name"`                                       // 设施名称
	Status    int       `gorm:"column:status;not null;default:1;comment:状态" json:"status"`                           // 状态(1-正常 0-停用)
	CreatedAt time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;comment:修改时间" json:"updated_at"` // 修改时间
}

// TableName EycMeetingFacility的表名
func (*EycMeetingFacility) TableName() string {
	return TableNameEycMeetingFacility
}

// IsFacilityExists 检查同企业下分组名称是否已存在
func IsFacilityExists(corp_id string, title string) (bool, error) {
	var count int64
	err := DB.Model(&EycMeetingGroup{}).
		Where("corp_id = ? AND title = ?", corp_id, title).
		Count(&count).Error

	if err != nil {
		logger.Error("检查分组名称是否存在时出错",
			logger.String("corp_id", corp_id),
			logger.String("title", title),
			logger.Error2(err))
		return false, err
	}

	return count > 0, nil
}

// GetAllFacilities 获取指定企业的所有设施，并以map形式返回以便快速查找
func GetAllFacilities(corp_id string) (map[string]bool, error) {
	var facilities []EycMeetingFacility
	if err := DB.Where("corp_id = ?", corp_id).Find(&facilities).Error; err != nil {
		return nil, fmt.Errorf("从数据库查询设施列表失败: %w", err)
	}

	cache := make(map[string]bool, len(facilities))
	for _, fac := range facilities {
		cache[fac.Name] = true
	}

	return cache, nil
}
