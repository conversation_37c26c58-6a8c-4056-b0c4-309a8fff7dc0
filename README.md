# Hertz基础服务架构

基于Hertz框架的Go语言基础服务架构，包含JWT认证、MySQL+GORM、Redis、MQTT、定时任务等组件。

## 项目结构

```
├── config              # 配置文件
├── internal            # 内部代码
│   ├── controller      # 控制器层
│   ├── middleware      # 中间件
│   │   └── auto_router.go # 自动路由中间件
│   ├── model           # 数据模型层
│   ├── pkg             # 内部公共包
│   │   ├── cron        # 定时任务
│   │   ├── logger      # 日志管理
│   │   ├── mqtt        # MQTT客户端
│   │   ├── redis       # Redis客户端
│   │   ├── request     # 统一请求处理
│   │   └── response    # 统一响应处理
│   ├── router          # 路由管理
│   │   ├── auth        # 认证路由
│   │   └── sys         # 系统路由
│   └── service         # 服务层
├── main.go             # 程序入口
└── scripts             # 脚本文件
```

## 技术栈

- Web框架：Hertz
- 数据库：MySQL 8.0 + GORM
- 缓存：Redis
- 消息队列：MQTT
- 认证：JWT
- 配置管理：Viper + .env
- 日志：Zap
- 定时任务：Cron
- 容器化：Docker (极简镜像)

## 功能特性

- JWT认证中间件
- 统一响应格式
- 分层架构（Controller/Service/Model）
- 路由分组
- 配置管理
- 日志管理
- 数据库连接池
- Redis客户端
- MQTT通信
- 定时任务
- 自动路由（不区分HTTP方法）
- 统一参数处理（简化GET和POST处理）

## 环境要求

- Go 1.16+
- MySQL 8.0+
- Redis 6.0+
- MQTT Broker (如Mosquitto)
- Docker (可选)

## 快速开始

1. 克隆项目

```bash
git clone https://github.com/yourusername/hertz-demo.git
cd hertz-demo
```

2. 安装依赖

```bash
go mod tidy
```

3. 配置环境变量

复制`.env.example`为`.env`，并根据实际情况修改配置：

```bash
cp .env.example .env
```

4. 启动服务

```bash
go run main.go
```

## API文档

### 认证相关

- `/auth/register` - 用户注册（支持所有HTTP方法）
- `/auth/login` - 用户登录（支持所有HTTP方法）
- `/auth/refresh` - 刷新Token（支持所有HTTP方法）

### 系统相关

- `/sys/hello` - Hello接口（需要JWT认证，支持所有HTTP方法）
- `/sys/user/info` - 获取用户信息（需要JWT认证，支持所有HTTP方法）
- `/sys/user/current` - 获取当前用户菜单信息（需要JWT认证，支持所有HTTP方法）

### 会议相关

#### 会议室管理
- `/room/post_add` - 添加会议室（基础信息）
- `/room/post_advanced_settings` - 更新会议室高级设置
- `/room/post_modify` - 修改会议室
- `/room/post_del` - 删除会议室
- `/room/get_info` - 获取会议室详情
- `/room/get_ls` - 获取会议室列表
- `/room/get_all` - 获取所有会议室

会议室管理功能说明：
1. 会议室功能已拆分为基础信息和高级设置两部分
2. 基础信息包括：会议室名称、所属分组、地点、设施、容纳人数、关联设备、是否开放预定、图片、备注等
3. 高级设置包括：可见范围、预约审批、开放时间、预定周期、时段限制、周期预定、会议室抢占、签到设置、超时释放、延迟设置等
4. 新增会议室时只处理基础信息，高级设置通过单独的接口更新

#### 分组管理
- `/group/post_add` - 添加分组
- `/group/post_modify` - 修改分组
- `/group/post_del` - 删除分组
- `/group/get_info` - 获取分组详情
- `/group/get_ls` - 获取分组列表
- `/group/get_all` - 获取所有分组

#### 设备管理
- `/device/post_add` - 添加设备
- `/device/post_modify` - 修改设备
- `/device/post_del` - 删除设备
- `/device/get_info` - 获取设备详情
- `/device/get_ls` - 获取设备列表
- `/device/get_all` - 获取所有设备
- `/device/batch_add` - 批量添加设备

设备管理功能说明：
1. 支持设备的增删改查基本功能
2. 支持设备的批量添加
3. 支持按关键词或分组筛选设备
4. 设备属性包括：设备名称、SN号、型号、灯带亮度、设备音量、是否自定义模板、是否关联门牌、会议系统模式、自动开关机设置等

#### 会议预订管理
- `/booking/post_add` - 新增会议预订
- `/booking/post_modify` - 修改会议预订
- `/booking/post_del` - 删除会议预订
- `/booking/get_info` - 获取会议预订详情
- `/booking/get_ls` - 获取会议预订列表

**会议预订参数说明 (`/booking/post_add` 和 `/booking/post_modify`):**
| 参数名                | 说明                                                              | 类型     | 是否必须 | 示例                                                                                                                                     |
| --------------------- | ----------------------------------------------------------------- | -------- | -------- | ---------------------------------------------------------------------------------------------------------------------------------------- |
| `title`               | 会议标题                                                          | `string` | **是**   | `"第一季度产品总结会"`                                                                                                                    |
| `start_time`          | 会议开始时间 (UTC格式)                                            | `string` | **是**   | `"2024-07-23T10:00:00Z"`                                                                                                                 |
| `end_time`            | 会议结束时间 (UTC格式)                                            | `string` | **是**   | `"2024-07-23T12:00:00Z"`                                                                                                                 |
| `room_ids`            | 会议室ID，多个用逗号分隔                                          | `string` | 否       | `"101,102"`                                                                                                                              |
| `room_names`          | 会议室名称                                                        | `string` | 否       | `"第一会议室, 第二会议室"`                                                                                                               |
| `location`            | 自定义会议地点                                                    | `string` | 否       | `"公司A座大培训室"`                                                                                                                      |
| `participants`        | 参会者，包括部门和用户                                            | `object` | 否       | `{"departments": [{"dept_id": "d001"}], "users": [{"userid": "u1001"}]}`                                                                 |
| `description`         | 会议描述                                                          | `string` | 否       | `"讨论新产品线的设计方案"`                                                                                                               |
| `reminders`           | 会议提醒设置                                                      | `array`  | 否       | `[{"minutes": 15, "type": 1}]`                                                                                                           |
| `attachments`         | 会议附件                                                          | `array`  | 否       | `[{"file_id": "f001", "file_name": "需求文档.pdf"}]`                                                                                     |
| `allow_invite_others` | 是否允许参会者邀请他人 (1:是, 0:否)                               | `number` | 否       | `1`                                                                                                                                      |
| `is_full_day`         | 是否全天会议 (1:是, 0:否)                                         | `number` | 否       | `0`                                                                                                                                      |
| `repeat_type`         | 重复类型 (例如 0:不重复, 1:每天, 2:每周)                          | `number` | 否       | `0`                                                                                                                                      |
| `notify_types`        | 通知类型                                                          | `string` | 否       | `"email,sms"`                                                                                                                            |
| `enable_notify`       | 是否开启通知 (1:是, 0:否)                                         | `number` | 否       | `1`                                                                                                                                      |
| `status`              | 预订状态 (例如 0:待确认, 1:已确认, 2:已取消)                      | `number` | 否       | `1`                                                                                                                                      |
