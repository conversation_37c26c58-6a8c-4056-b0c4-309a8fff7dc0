package service

import (
	"context"
	"encoding/json"
	"errors"
	"eyc3_meeting/internal/model"
	"eyc3_meeting/internal/pkg/logger"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"

	"github.com/cloudwego/hertz/pkg/app"
)

// RemindersData 提醒设置类型
type RemindersData []struct {
	Minutes int    `json:"minutes"`
	Type    int    `json:"type"`
	Status  int    `json:"status"`
	Label   string `json:"label,omitempty"`
}

// Participant 参会人信息
type Participant struct {
	UserID   string `json:"user_id"`
	UserName string `json:"user_name,omitempty"`
	Role     int    `json:"role,omitempty"`
	Status   int    `json:"status,omitempty"`
}

// EycMeetingBookingService 会议预订服务
type EycMeetingBookingService struct {
	BaseServiceImpl[model.EycMeetingBooking]
}

// AddService 新增会议预订，重写基础方法以实现自定义逻辑
func (s *EycMeetingBookingService) AddService(ac *app.RequestContext, data interface{}) (uint, error) {
	m, ok := data.(map[string]interface{})
	if !ok {
		return 0, fmt.Errorf("参数类型错误")
	}

	booking := &model.EycMeetingBooking{}

	// 从 context 获取 corpid
	corpIDValue, exists := ac.Get("corp_id")
	if !exists {
		return 0, errors.New("无法获取企业ID，请检查登录状态")
	}
	corpID, _ := corpIDValue.(string)
	booking.Corpid = corpID

	// 从 map 解析参数
	if title, ok := m["title"].(string); ok && title != "" {
		booking.Title = title
	} else {
		return 0, errors.New("会议标题不能为空")
	}

	// 应用其他字段和默认值逻辑
	if err := s.applyBookingLogic(booking, m); err != nil {
		return 0, err
	}

	// 参数验证
	if err := validateBookingParams(booking); err != nil {
		return 0, err
	}

	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	now := time.Now()
	booking.CreatedAt = now
	booking.UpdatedAt = now
	booking.Status = model.BookingStatusPending // 默认待确认状态

	if err := tx.Create(booking).Error; err != nil {
		tx.Rollback()
		return 0, err
	}

	// 处理提醒设置
	if err := s.handleRemindersInTx(tx, booking); err != nil {
		tx.Rollback()
		return 0, err
	}

	// 处理附件
	if err := s.handleAttachmentsInTx(tx, booking); err != nil {
		tx.Rollback()
		return 0, err
	}

	if err := tx.Commit().Error; err != nil {
		return 0, err
	}

	return uint(booking.ID), nil
}

// ModifyService 修改会议预订，重写基础方法
func (s *EycMeetingBookingService) ModifyService(ac *app.RequestContext, id uint, data interface{}) error {
	m, ok := data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("参数类型错误")
	}

	corpIDValue, exists := ac.Get("corp_id")
	if !exists {
		return errors.New("无法获取企业ID")
	}
	corpID, _ := corpIDValue.(string)

	// 查找现有预订
	var existingBooking model.EycMeetingBooking
	if err := model.DB.Where("id = ? AND corpid = ?", id, corpID).First(&existingBooking).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("会议预订不存在或无权访问")
		}
		return err
	}

	// 应用更新字段
	if err := s.applyBookingLogic(&existingBooking, m); err != nil {
		return err
	}

	// 参数验证
	if err := validateBookingParams(&existingBooking); err != nil {
		return err
	}

	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	existingBooking.UpdatedAt = time.Now()
	if err := tx.Save(&existingBooking).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 处理提醒设置
	if err := s.handleRemindersInTx(tx, &existingBooking); err != nil {
		tx.Rollback()
		return err
	}

	// 处理附件
	if err := s.handleAttachmentsInTx(tx, &existingBooking); err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// DeleteService 删除会议预订，重写基础方法
func (s *EycMeetingBookingService) DeleteService(ac *app.RequestContext, id uint) error {
	corpIDValue, exists := ac.Get("corp_id")
	if !exists {
		return errors.New("无法获取企业ID")
	}
	corpID, _ := corpIDValue.(string)

	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var booking model.EycMeetingBooking
	if err := tx.Where("id = ? AND corp_id = ?", id, corpID).First(&booking).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("会议预订不存在或无权访问")
		}
		return fmt.Errorf("查找要删除的会议预订时出错: %w", err)
	}

	if err := tx.Delete(&booking).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// GetListService 获取会议预订列表，增加了自定义筛选逻辑
func (s *EycMeetingBookingService) GetListService(ac *app.RequestContext, page, perPage int, fields map[string]string, orderBy string) (model.PageResult[model.EycMeetingBooking], error) {
	var bookings []model.EycMeetingBooking
	var total int64
	var pageResult model.PageResult[model.EycMeetingBooking]

	db := model.DB.Model(&model.EycMeetingBooking{})

	// 从 context 获取 corp_id
	corpID, _ := ac.Get("corp_id")
	if corpIDStr, ok := corpID.(string); ok && corpIDStr != "" {
		db = db.Where("corpid = ?", corpIDStr)
	}

	// 关键字搜索 (会议标题)
	if keyword, ok := fields["keyword"]; ok && keyword != "" {
		db = db.Where("title LIKE ?", "%"+keyword+"%")
	}

	// 按会议室ID筛选 (支持多个, 逗号分隔)
	if roomIDs, ok := fields["room_ids"]; ok && roomIDs != "" {
		db = db.Where("room_ids IN (?)", strings.Split(roomIDs, ","))
	}

	// 按预约人ID筛选 (支持多个, 逗号分隔)
	if createdBy, ok := fields["created_by"]; ok && createdBy != "" {
		db = db.Where("created_by IN (?)", strings.Split(createdBy, ","))
	}

	// 按时间范围筛选
	if startTimeStr, ok := fields["start_time"]; ok && startTimeStr != "" {
		if startTime, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			db = db.Where("start_time >= ?", startTime)
		}
	}
	if endTimeStr, ok := fields["end_time"]; ok && endTimeStr != "" {
		if endTime, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			db = db.Where("end_time <= ?", endTime)
		}
	}

	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		return pageResult, err
	}

	// 设置分页结果
	pageResult.Total = total
	pageResult.Items = []model.EycMeetingBooking{}

	// 默认排序：按开始时间降序
	if orderBy == "" {
		orderBy = "start_time DESC"
	}
	db = db.Order(orderBy)

	// 分页查询
	if page > 0 && perPage > 0 {
		offset := (page - 1) * perPage
		db = db.Offset(offset).Limit(perPage)
	}

	if total > 0 {
		if err := db.Find(&bookings).Error; err != nil {
			return pageResult, err
		}
		pageResult.Items = bookings
	}

	return pageResult, nil
}

// applyBookingLogic 辅助函数，用于将map中的数据应用到booking结构体
func (s *EycMeetingBookingService) applyBookingLogic(booking *model.EycMeetingBooking, m map[string]interface{}) error {
	// 处理会议室信息
	if roomIDs, ok := m["room_ids"].(string); ok && roomIDs != "" {
		booking.RoomIDs = roomIDs
	}
	if roomNames, ok := m["room_names"].(string); ok && roomNames != "" {
		booking.RoomNames = roomNames
	}
	if location, ok := m["location"].(string); ok {
		booking.Location = location
	}

	// 处理时间信息
	if startTime, ok := m["start_time"].(string); ok && startTime != "" {
		t, err := time.Parse(time.RFC3339, startTime)
		if err != nil {
			return fmt.Errorf("开始时间格式错误: %w", err)
		}
		booking.StartTime = t
	}
	if endTime, ok := m["end_time"].(string); ok && endTime != "" {
		t, err := time.Parse(time.RFC3339, endTime)
		if err != nil {
			return fmt.Errorf("结束时间格式错误: %w", err)
		}
		booking.EndTime = t
	}
	if val, ok := m["is_full_day"]; ok {
		if v, ok := val.(float64); ok {
			booking.IsFullDay = int(v)
		}
	}

	// 修正：处理参会人信息，参考会议室服务的实现
	if participantsVal, ok := m["participants"]; ok {
		// 记录原始输入
		logger.Info("处理参会人信息开始",
			logger.String("type", fmt.Sprintf("%T", participantsVal)),
			logger.String("raw_value", fmt.Sprintf("%+v", participantsVal)))

		if participantsVal == nil {
			logger.Info("参会人信息为空")
			booking.Participants = nil
		} else {
			var jsonBytes []byte
			var err error

			switch v := participantsVal.(type) {
			case string:
				v = strings.TrimSpace(v)
				logger.Info("参会人信息为字符串类型", logger.String("value", v))
				if v == "" {
					booking.Participants = nil
					return nil
				}
				// 尝试解析字符串
				var data interface{}
				if err := json.Unmarshal([]byte(v), &data); err != nil {
					if strings.HasPrefix(v, "map[") {
						errMsg := "参会人信息格式错误: 接收到Go map字符串，请客户端修改为发送标准JSON格式。"
						logger.Error(errMsg,
							logger.String("value", v))
						return fmt.Errorf(errMsg)
					}

					logger.Error("解析参会人字符串失败",
						logger.String("error", err.Error()),
						logger.String("value", v))
					return fmt.Errorf("参会人信息格式错误: %w", err)
				}
				jsonBytes, err = json.Marshal(data)
			case map[string]interface{}:
				logger.Info("参会人信息为map类型",
					logger.String("departments", fmt.Sprintf("%+v", v["departments"])),
					logger.String("users", fmt.Sprintf("%+v", v["users"])))

				// 验证数据结构
				if v["departments"] == nil && v["users"] == nil {
					logger.Error("参会人信息不完整: departments 和 users 都为空")
					return fmt.Errorf("参会人信息不完整: departments 和 users 都为空")
				}

				// 直接使用原始数据
				jsonBytes, err = json.Marshal(v)
				if err != nil {
					logger.Error("序列化参会人信息失败",
						logger.String("error", err.Error()),
						logger.String("data", fmt.Sprintf("%+v", v)))
					return fmt.Errorf("序列化参会人信息失败: %w", err)
				}

				// 验证序列化后的数据
				logger.Info("序列化后的数据",
					logger.String("json", string(jsonBytes)))

				// 尝试反序列化验证
				var temp struct {
					Departments []struct {
						DeptID string `json:"dept_id"`
						Name   string `json:"name"`
					} `json:"departments"`
					Users []struct {
						UserID string `json:"userid"`
						Name   string `json:"name"`
						Avatar string `json:"avatar"`
					} `json:"users"`
				}
				if err := json.Unmarshal(jsonBytes, &temp); err != nil {
					logger.Error("验证参会人信息结构失败",
						logger.String("error", err.Error()),
						logger.String("json", string(jsonBytes)))
					return fmt.Errorf("参会人信息结构错误: %w", err)
				}

				// 验证数据内容
				if len(temp.Departments) == 0 && len(temp.Users) == 0 {
					logger.Error("参会人信息不完整: departments 和 users 数组为空")
					return fmt.Errorf("参会人信息不完整: departments 和 users 数组为空")
				}

				logger.Info("验证后的数据结构",
					logger.Int("departments_count", len(temp.Departments)),
					logger.Int("users_count", len(temp.Users)))

			default:
				logger.Error("参会人信息类型错误",
					logger.String("type", fmt.Sprintf("%T", participantsVal)))
				return fmt.Errorf("参会人信息类型错误: %T", participantsVal)
			}

			if err != nil {
				logger.Error("处理参会人信息失败",
					logger.String("error", err.Error()),
					logger.String("data", fmt.Sprintf("%+v", participantsVal)))
				return fmt.Errorf("处理参会人信息失败: %w", err)
			}

			booking.Participants = model.RawJSON(jsonBytes)

			// 记录最终结果
			logger.Info("参会人信息处理完成",
				logger.String("final_json", string(booking.Participants)))
		}
	}

	// 处理通知设置
	if notifyTypes, ok := m["notify_types"].(string); ok {
		booking.NotifyTypes = notifyTypes
	}
	if enableNotify, ok := m["enable_notify"].(float64); ok {
		booking.EnableNotify = int(enableNotify)
	}

	// 处理提醒设置，直接使用RawJSON
	if remindersVal, ok := m["reminders"]; ok {
		if rawJSON, err := json.Marshal(remindersVal); err == nil {
			booking.Reminders = rawJSON
		}
	}

	// 处理重复设置
	if repeatType, ok := m["repeat_type"].(float64); ok {
		booking.RepeatType = int(repeatType)
	}
	if repeatEndDate, ok := m["repeat_end_date"].(string); ok && repeatEndDate != "" {
		t, err := time.Parse(time.RFC3339, repeatEndDate)
		if err != nil {
			return fmt.Errorf("重复结束日期格式错误: %w", err)
		}
		booking.RepeatEndDate = &t
	}

	// 处理其他信息
	if description, ok := m["description"].(string); ok {
		booking.Description = description
	}
	// 处理附件，直接使用RawJSON
	if attachmentsVal, ok := m["attachments"]; ok {
		if rawJSON, err := json.Marshal(attachmentsVal); err == nil {
			booking.Attachments = rawJSON
		}
	}
	if status, ok := m["status"].(float64); ok {
		booking.Status = int(status)
	}

	if val, ok := m["allow_invite_others"]; ok {
		if v, ok := val.(float64); ok {
			booking.AllowInviteOthers = int(v)
		}
	}

	return nil
}

// validateBookingParams 验证会议预订参数
func validateBookingParams(booking *model.EycMeetingBooking) error {
	if booking.Title == "" {
		return errors.New("会议标题不能为空")
	}

	if booking.StartTime.IsZero() || booking.EndTime.IsZero() {
		return errors.New("会议开始时间和结束时间不能为空")
	}

	if booking.EndTime.Before(booking.StartTime) {
		return errors.New("会议结束时间不能早于开始时间")
	}

	// 验证参会人信息
	if booking.Participants != nil {
		var jsonData interface{}
		if err := json.Unmarshal(booking.Participants, &jsonData); err != nil {
			// 这里返回原始错误，更有助于调试
			return fmt.Errorf("参会人信息格式错误: %w", err)
		}
	}

	return nil
}

// handleRemindersInTx 处理提醒设置
func (s *EycMeetingBookingService) handleRemindersInTx(tx *gorm.DB, booking *model.EycMeetingBooking) error {
	if len(booking.Reminders) == 0 {
		return nil
	}

	return tx.Model(booking).Update("reminders", booking.Reminders).Error
}

// handleAttachmentsInTx 处理附件
func (s *EycMeetingBookingService) handleAttachmentsInTx(tx *gorm.DB, booking *model.EycMeetingBooking) error {
	if len(booking.Attachments) == 0 {
		return nil
	}

	return tx.Model(booking).Update("attachments", booking.Attachments).Error
}

// GetMeetingBookingById 根据ID获取会议预订详情
func GetMeetingBookingById(ctx context.Context, id int) (*model.EycMeetingBooking, error) {
	var booking model.EycMeetingBooking
	if err := model.DB.First(&booking, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("预订记录不存在")
		}
		return nil, err
	}

	return &booking, nil
}
