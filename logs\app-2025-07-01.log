{"level":"INFO","time":"2025-07-01T11:15:11.117+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:11.281+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:15:11.480+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:11.539+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:11.677+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:15:11.820+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:11.954+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:12.034+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:12.174+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":139,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:15:12.324+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":149,"影响行数":3}
{"level":"INFO","time":"2025-07-01T11:15:12.380+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":55,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:12.547+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":166,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:15:12.678+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":131,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:12.744+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:12.911+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":166,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:15:13.044+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:13.257+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `t_user` MODIFY COLUMN `password` varchar(100) NOT NULL","耗时":212,"影响行数":0}
{"level":"INFO","time":"2025-07-01T11:15:13.327+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:13.506+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:15:13.624+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":117,"影响行数":3}
{"level":"INFO","time":"2025-07-01T11:15:13.714+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:13.849+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:15:13.987+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":136,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:14.080+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:14.189+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":109,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:15:14.321+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":132,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:14.392+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `t_user` MODIFY COLUMN `created_at` datetime(3) NULL","耗时":69,"影响行数":0}
{"level":"INFO","time":"2025-07-01T11:15:14.485+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"ALTER TABLE `t_user` MODIFY COLUMN `updated_at` datetime(3) NULL","耗时":92,"影响行数":0}
{"level":"INFO","time":"2025-07-01T11:15:14.568+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":82,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:14.733+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":165,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:15:14.956+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":222,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:15.034+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:15.162+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":128,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:15:15.330+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":167,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T11:15:15.330+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-01T11:15:15.485+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-01T11:15:15.486+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-01T11:15:15.486+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-01T11:15:15.487+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-01T11:15:35.115+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":158,"影响行数":2}
{"level":"INFO","time":"2025-07-01T11:15:35.670+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '郑州/管城区/升龙广场' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":338,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:15:35.940+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`meet_room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','','黄河会议室',188,'中国河南省郑州市','',10,'',1,NULL,'这是一个测试备注',0,'{\"type\":\"all\"}',0,'','',0,'',0,0,1,0,0,0,0,0,0)","耗时":269,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:15:49.263+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 4 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":224,"影响行数":0}
{"level":"ERROR","time":"2025-07-01T11:15:49.264+0800","caller":"controller/base.go:466","msg":"修改记录失败","error":"会议室不存在"}
{"level":"INFO","time":"2025-07-01T11:16:01.759+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 4 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":152,"影响行数":0}
{"level":"ERROR","time":"2025-07-01T11:16:01.759+0800","caller":"controller/base.go:466","msg":"修改记录失败","error":"会议室不存在"}
{"level":"INFO","time":"2025-07-01T11:16:12.537+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`meet_room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','','亚马逊森林会议室4',182,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,NULL,'领导专用',0,NULL,0,'','',0,'',0,0,1,0,0,0,0,0,0)","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:16:12.767+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 182 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":230,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:16:12.978+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_groups` SET `room_id`='43,44,45,46,54',`updated_at`='2025-07-01 11:16:12.769' WHERE `id` = 182","耗时":209,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:16:18.354+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`meet_room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','','亚马逊森林会议室4',182,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,NULL,'领导专用',0,NULL,0,'','',0,'',0,0,1,0,0,0,0,0,0)","耗时":175,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:16:18.485+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 182 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:16:18.657+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_groups` SET `room_id`='43,44,45,46,54,55',`updated_at`='2025-07-01 11:16:18.486' WHERE `id` = 182","耗时":171,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:20:47.774+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 0 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":145,"影响行数":0}
{"level":"INFO","time":"2025-07-01T11:21:13.204+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 0 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":430,"影响行数":0}
{"level":"INFO","time":"2025-07-01T11:21:26.891+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0","耗时":547,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:21:27.103+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":211,"影响行数":10}
{"level":"INFO","time":"2025-07-01T11:22:04.780+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 0 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":124,"影响行数":0}
{"level":"INFO","time":"2025-07-01T11:25:06.413+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 0 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":195,"影响行数":0}
{"level":"INFO","time":"2025-07-01T11:25:33.918+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 0 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":122,"影响行数":0}
{"level":"INFO","time":"2025-07-01T11:26:44.687+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 128 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":104,"影响行数":0}
{"level":"INFO","time":"2025-07-01T11:26:57.189+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0","耗时":285,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:26:57.399+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":209,"影响行数":10}
{"level":"INFO","time":"2025-07-01T11:27:04.847+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 41 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":150,"影响行数":1}
{"level":"INFO","time":"2025-07-01T11:31:04.992+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 0 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":158,"影响行数":0}
{"level":"INFO","time":"2025-07-01T11:34:03.424+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 0 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":260,"影响行数":0}
{"level":"INFO","time":"2025-07-01T11:40:09.268+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 0 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":180,"影响行数":0}
{"level":"INFO","time":"2025-07-01T11:40:29.233+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 0 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":181,"影响行数":0}
{"level":"INFO","time":"2025-07-01T11:42:31.345+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 0 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":135,"影响行数":0}
{"level":"INFO","time":"2025-07-01T11:42:45.189+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 0 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":118,"影响行数":0}
{"level":"INFO","time":"2025-07-01T13:42:17.475+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 0 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":176,"影响行数":0}
{"level":"INFO","time":"2025-07-01T13:42:41.449+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 0 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":159,"影响行数":0}
{"level":"INFO","time":"2025-07-01T13:42:48.441+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-01T13:42:48.443+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-01T13:42:48.443+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-01T13:43:07.897+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":103,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:08.098+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":174,"影响行数":1}
{"level":"INFO","time":"2025-07-01T13:43:08.264+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":165,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:08.338+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:08.474+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-07-01T13:43:08.608+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:08.776+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":166,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:08.868+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:09.047+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":178,"影响行数":1}
{"level":"INFO","time":"2025-07-01T13:43:09.196+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":148,"影响行数":3}
{"level":"INFO","time":"2025-07-01T13:43:09.257+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:09.435+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":176,"影响行数":1}
{"level":"INFO","time":"2025-07-01T13:43:09.655+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":220,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:09.735+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:09.937+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":200,"影响行数":1}
{"level":"INFO","time":"2025-07-01T13:43:10.112+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":174,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:10.236+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":123,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:10.382+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-07-01T13:43:10.559+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":175,"影响行数":3}
{"level":"INFO","time":"2025-07-01T13:43:10.723+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":164,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:10.944+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":220,"影响行数":1}
{"level":"INFO","time":"2025-07-01T13:43:11.205+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":260,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:11.300+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:11.507+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":206,"影响行数":1}
{"level":"INFO","time":"2025-07-01T13:43:11.705+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":197,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:11.821+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:12.004+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":181,"影响行数":1}
{"level":"INFO","time":"2025-07-01T13:43:12.186+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":182,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:12.298+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":111,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:12.524+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":225,"影响行数":1}
{"level":"INFO","time":"2025-07-01T13:43:12.723+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-07-01T13:43:12.724+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-01T13:43:12.919+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-01T13:43:12.920+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-01T13:43:12.920+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-01T13:43:12.921+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-01T13:43:19.035+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 0 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":186,"影响行数":0}
{"level":"INFO","time":"2025-07-01T15:44:23.254+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-01T15:44:23.255+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-01T15:44:23.255+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
