package model

import (
	"encoding/json"
	"eyc3_meeting/internal/pkg/logger"
	"fmt"
	"strconv"
	"time"
)

const TableNameEycMeetingRoom = "eyc_meeting_room"

// EycMeetingRoom 会议室表
type EycMeetingRoom struct {
	ID            int     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Corpid        string  `gorm:"column:corpid;not null;comment:架构corpid" json:"corpid"` // 架构corpid
	Title         string  `json:"title" gorm:"column:title;comment:会议室分组名称"`
	MeetRoomName  string  `gorm:"column:meet_room_name;not null;comment:会议室名称" json:"meet_room_name"`      // 会议室名称
	GroupID       int     `gorm:"column:group_id;comment:分组id" json:"group_id"`                            // 所属分组id
	Location      string  `gorm:"column:location;comment:地点" json:"location"`                              // 地点
	Facility      string  `gorm:"column:facility;comment:容纳设施(逗号分隔)" json:"facility"`                      // 容纳设施(逗号分隔ID或名称)
	Capacity      int     `gorm:"column:capacity;not null;comment:容纳人数" json:"capacity"`                   // 容纳人数
	Device        string  `gorm:"column:device;comment:关联设备(逗号分隔)" json:"device"`                          // 关联设备(逗号分隔ID或名称)
	IsOpenBooking int     `gorm:"column:is_open_booking;default:1;comment:会议室开放预定" json:"is_open_booking"` // 会议室开放预定
	Image         RawJSON `gorm:"column:image;type:json;comment:图片URL" json:"image"`                       // 图片(JSON格式)
	Remark        string  `gorm:"column:remark;comment:备注" json:"remark"`                                  // 备注
	Sort          int     `gorm:"column:sort;comment:排序值" json:"sort"`                                     // 排序值，值越小排序越靠前

	// 高级设置相关字段
	VisibleScope      RawJSON   `gorm:"column:visible_scope;type:json;comment:可见范围" json:"visible_scope"`                                // 会议室可见范围(JSON格式)
	NeedApproval      int       `gorm:"column:need_approval;default:0;comment:预约是否需要审批" json:"need_approval"`                            // 预约是否需要审批
	ApprovalScope     string    `gorm:"column:approval_scope;comment:审批范围" json:"approval_scope"`                                        // 审批范围
	ApprovalType      string    `gorm:"column:approval_type;comment:审批类型" json:"approval_type"`                                          // 审批类型
	OpenStartTime     time.Time `gorm:"column:open_start_time;not null;default:CURRENT_TIMESTAMP;comment:开放开始时间" json:"open_start_time"` // 开放开始时间
	OpenEndTime       time.Time `gorm:"column:open_end_time;not null;default:CURRENT_TIMESTAMP;comment:开放结束时间" json:"open_end_time"`     // 开放结束时间
	EarliestBookCycle int       `gorm:"column:earliest_book_cycle;comment:最早可提前预定周期(天/周)" json:"earliest_book_cycle"`                    // 最早可提前预定周期
	EarliestBookTime  string    `gorm:"column:earliest_book_time;comment:最早可提前预定时间" json:"earliest_book_time"`                           // 最早可提前预定时间
	MinBookDuration   int       `gorm:"column:min_book_duration;comment:单次可预定最小时段(分钟)" json:"min_book_duration"`                         // 单次可预定最小时段
	MaxBookDuration   int       `gorm:"column:max_book_duration;comment:单次可预定最长时段(分钟)" json:"max_book_duration"`                         // 单次可预定最长时段
	AllowRecurring    int       `gorm:"column:allow_recurring;default:1;comment:允许周期预定" json:"allow_recurring"`                          // 允许周期预定
	AllowOccupy       int       `gorm:"column:allow_occupy;default:0;comment:允许会议室抢占" json:"allow_occupy"`                               // 允许会议室抢占
	KeepSignin        int       `gorm:"column:keep_signin;default:0;comment:会议室签到保留（1是0否）" json:"keep_signin"`                           // 会议室签到保留

	ShowSigninAdvanceTime int `gorm:"column:show_signin_advance_time;comment:提前签到显示时长（分钟）" json:"show_signin_advance_time"` // 提前签到显示时长（分钟）

	ReleaseOvertimeTime int `gorm:"column:release_overtime_time;comment:超时释放会议室时长（分钟）" json:"release_overtime_time"` // 超时释放会议室时长（分钟）
	AllowDelay          int `gorm:"column:allow_delay;default:0;comment:会议室延迟（1是0否）" json:"allow_delay"`             // 会议室延迟
	DelayTime           int `gorm:"column:delay_time;comment:延迟时长（分钟）" json:"delay_time"`                            // 延迟时长（分钟）

	CreatedAt time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP;comment:添加时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;comment:最后修改时间" json:"updated_at"`
}

// UnmarshalJSON 自定义JSON解析方法，处理Device字段的类型转换
func (r *EycMeetingRoom) UnmarshalJSON(data []byte) error {
	type Alias EycMeetingRoom
	aux := &struct {
		Device interface{} `json:"device"`
		*Alias
	}{
		Alias: (*Alias)(r),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	switch v := aux.Device.(type) {
	case string:
		r.Device = v
	case float64:
		r.Device = strconv.Itoa(int(v))
	case int:
		r.Device = strconv.Itoa(v)
	case int64:
		r.Device = strconv.FormatInt(v, 10)
	case nil:
		r.Device = ""
	default:
		return fmt.Errorf("unsupported type for Device: %T", v)
	}

	return nil
}

// TableName EycMeetingRoom's table name
func (*EycMeetingRoom) TableName() string {
	return TableNameEycMeetingRoom
}

// IsRoomNameExists 检查在指定企业下，会议室名称是否已存在（可排除指定ID）
func (r *EycMeetingRoom) IsRoomNameExists(corpid string, MeetroomName string, excludeID int) (bool, error) {
	var count int64
	query := DB.Model(&EycMeetingRoom{}).
		Where("corpid = ? AND meet_room_name = ?", corpid, MeetroomName)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	err := query.Count(&count).Error
	if err != nil {
		logger.Error("检查会议室名称是否存在时出错",
			logger.String("corpid", corpid),
			logger.String("meet_room_name", MeetroomName),
			logger.Int("excludeID", excludeID),
			logger.Error2(err))
		return false, err
	}
	return count > 0, nil
}

// EycMeetingRoomAdvancedSettings 会议室高级设置请求结构体
type EycMeetingRoomAdvancedSettings struct {
	ID                int     `json:"id"`                  // 会议室ID
	Corpid            string  `json:"corpid"`              // 企业ID
	VisibleScope      RawJSON `json:"visible_scope"`       // 会议室可见范围(JSON格式)
	NeedApproval      int     `json:"need_approval"`       // 预约是否需要审批
	ApprovalScope     string  `json:"approval_scope"`      // 审批范围
	ApprovalType      string  `json:"approval_type"`       // 审批类型
	OpenStartTime     string  `json:"open_start_time"`     // 开放开始时间
	OpenEndTime       string  `json:"open_end_time"`       // 开放结束时间
	EarliestBookCycle int     `json:"earliest_book_cycle"` // 最早可提前预定周期
	EarliestBookTime  string  `json:"earliest_book_time"`  // 最早可提前预定时间
	MinBookDuration   int     `json:"min_book_duration"`   // 单次可预定最小时段
	MaxBookDuration   int     `json:"max_book_duration"`   // 单次可预定最长时段
	AllowRecurring    int     `json:"allow_recurring"`     // 允许周期预定
	AllowOccupy       int     `json:"allow_occupy"`        // 允许会议室抢占
	KeepSignin        int     `json:"keep_signin"`         // 会议室签到保留

	ShowSigninAdvanceTime int `json:"show_signin_advance_time"` // 提前签到显示时长

	ReleaseOvertimeTime int `json:"release_overtime_time"` // 超时释放会议室时长
	AllowDelay          int `json:"allow_delay"`           // 会议室延迟
	DelayTime           int `json:"delay_time"`            // 延迟时长
}
