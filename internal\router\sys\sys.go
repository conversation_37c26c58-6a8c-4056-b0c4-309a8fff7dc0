package sys

import (
	"eyc3_meeting/internal/controller"
	"eyc3_meeting/internal/middleware"

	"github.com/cloudwego/hertz/pkg/app/server"
)

// Register 注册系统相关路由
func Register(r *server.Hertz) {
	// 使用JWT中间件创建系统路由组
	sysGroup := r.Group("/sys", middleware.JWT())

	// 使用传统方式注册路由
	{
		// Hello接口，支持所有HTTP方法
		sysGroup.Any("/hello", controller.Hello)

		// 获取用户信息
		sysGroup.Any("/user/info", controller.GetUserInfo)

		// 获取当前用户信息（从bak项目重构过来的）
		sysGroup.Any("/user/current", controller.CurrentUser)
	}

	// 你也可以使用自动路由中间件方式
	/*
		autoRouter := middleware.NewAutoRouter()

		// Hello接口，不指定HTTP方法，表示支持所有方法
		autoRouter.Register("/sys/hello", nil, middleware.JWT(), controller.Hello)

		// 获取用户信息
		autoRouter.Register("/sys/user/info", nil, middleware.JWT(), controller.GetUserInfo)

		// 获取当前用户信息
		autoRouter.Register("/sys/user/current", nil, middleware.JWT(), controller.CurrentUser)

		// 应用路由
		autoRouter.ApplyToRouter(r)
	*/
}
