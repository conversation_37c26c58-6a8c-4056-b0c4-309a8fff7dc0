{"level":"INFO","time":"2025-07-02T08:55:45.742+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:45.872+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":108,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:46.002+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:46.067+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:46.165+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":96,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:46.329+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:46.449+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:46.538+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":88,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:46.655+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":116,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:46.797+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":142,"影响行数":3}
{"level":"INFO","time":"2025-07-02T08:55:46.852+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:46.982+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:47.112+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:47.178+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:47.278+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:47.395+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:47.464+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:47.566+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:47.667+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":101,"影响行数":3}
{"level":"INFO","time":"2025-07-02T08:55:47.764+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:47.900+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:48.058+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:48.131+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:48.255+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:48.375+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:48.436+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:48.573+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:48.712+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:48.776+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:48.878+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:49.008+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:49.008+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T08:55:49.139+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T08:55:49.140+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T08:55:49.141+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T08:55:49.143+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T08:59:05.439+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":271,"影响行数":39}
{"level":"INFO","time":"2025-07-02T08:59:05.630+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND DATE(start_time) <= '2025-6-02' AND DATE(end_time) >= '2025-6-02'","耗时":190,"影响行数":0}
{"level":"INFO","time":"2025-07-02T09:02:14.068+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 35 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":93,"影响行数":1}
{"level":"INFO","time":"2025-07-02T09:02:14.188+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 28 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":119,"影响行数":1}
{"level":"INFO","time":"2025-07-02T09:02:14.300+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=sort - 1,`updated_at`='2025-07-02 09:02:14.189' WHERE group_id = 0 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND sort > 84 AND sort < 85","耗时":112,"影响行数":0}
{"level":"INFO","time":"2025-07-02T09:02:14.380+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=84,`updated_at`='2025-07-02 09:02:14.301' WHERE `id` = 35","耗时":80,"影响行数":1}
{"level":"INFO","time":"2025-07-02T09:39:05.542+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T09:39:05.543+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T09:39:05.543+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T10:18:41.950+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:42.317+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":339,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:42.578+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":259,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:42.649+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:43.378+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":728,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:43.562+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":183,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:43.745+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":182,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:43.863+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:44.068+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":204,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:44.243+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":175,"影响行数":3}
{"level":"INFO","time":"2025-07-02T10:18:44.425+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":181,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:44.665+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":239,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:44.912+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":246,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:45.003+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:45.168+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:45.403+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":233,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:45.612+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":208,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:45.884+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":271,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:46.044+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":159,"影响行数":3}
{"level":"INFO","time":"2025-07-02T10:18:46.138+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:46.429+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":290,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:46.659+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":230,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:46.748+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:46.987+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":238,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:47.137+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:47.292+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":153,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:47.538+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":244,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:47.784+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":245,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:47.844+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:48.117+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":272,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:48.394+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":275,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:48.394+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T10:18:48.563+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T10:18:48.564+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T10:18:48.564+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T10:18:48.565+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T10:20:00.707+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 35 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":327,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:20:00.951+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=sort + 1,`updated_at`='2025-07-02 10:20:00.708' WHERE sort >= 1 AND sort < 84 AND group_id = 0 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND id != 35","耗时":243,"影响行数":12}
{"level":"INFO","time":"2025-07-02T10:20:01.220+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=1,`updated_at`='2025-07-02 10:20:00.951' WHERE id = 35","耗时":269,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:22:27.736+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '你好aqaa'","耗时":268,"影响行数":1}
{"level":"ERROR","time":"2025-07-02T10:22:27.736+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"分组名称已存在"}
{"level":"INFO","time":"2025-07-02T10:22:52.983+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 123 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":238,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:22:53.207+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_groups` SET `corp_id`='',`title`='你好a',`d_group_id`=0,`created_at`='2025-05-26 11:26:05',`updated_at`='2025-07-02 10:22:52.984',`sort`=0,`lock`=0,`type`=0,`status`=0,`room_id`='',`meet_room_name`='黄河会议室' WHERE `id` = 123","耗时":223,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:24:04.047+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 35 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":958,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:24:04.217+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=sort - 1,`updated_at`='2025-07-02 10:24:04.048' WHERE sort > 1 AND sort <= 2 AND group_id = 0 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND id != 35","耗时":169,"影响行数":6}
{"level":"INFO","time":"2025-07-02T10:24:04.360+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=2,`updated_at`='2025-07-02 10:24:04.218' WHERE id = 35","耗时":142,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:31:21.076+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0","耗时":370,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:31:21.629+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":552,"影响行数":10}
{"level":"INFO","time":"2025-07-02T10:54:12.069+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T10:54:12.071+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T10:54:12.071+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T10:54:19.558+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:19.806+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":235,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:20.046+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":238,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:20.181+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":134,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:20.522+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":340,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:20.720+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":197,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:21.061+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":340,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:21.160+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:21.476+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":315,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:21.726+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":249,"影响行数":3}
{"level":"INFO","time":"2025-07-02T10:54:21.948+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":221,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:22.440+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":491,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:22.870+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":429,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:23.003+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":131,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:23.255+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":251,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:23.456+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":200,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:23.597+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:23.791+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":193,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:24.058+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":266,"影响行数":3}
{"level":"INFO","time":"2025-07-02T10:54:24.146+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:24.478+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":331,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:24.862+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":382,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:25.038+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":175,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:25.340+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":302,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:25.595+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":254,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:25.762+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":165,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:26.050+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":288,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:26.270+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":219,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:26.362+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:26.553+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":191,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:26.819+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":264,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:26.819+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T10:54:27.038+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T10:54:27.040+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T10:54:27.040+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T10:54:27.042+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T10:54:51.478+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":300,"影响行数":2}
{"level":"INFO","time":"2025-07-02T10:54:51.927+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '郑州' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":322,"影响行数":0}
{"level":"INFO","time":"2025-07-02T10:54:52.156+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_groups` (`corp_id`,`title`,`d_group_id`,`sort`,`lock`,`type`,`status`,`room_id`,`meet_room_name`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','郑州',0,0,0,0,0,'','')","耗时":228,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:52.374+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`meet_room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','','黄河会议室',189,'中国河南省郑州市','',10,'',1,NULL,'这是一个测试备注',0,'{\"type\":\"all\"}',0,'','',0,'',0,0,1,0,0,0,0,0,0)","耗时":217,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:19.774+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T11:03:19.775+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T11:03:19.775+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T11:03:24.822+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:24.991+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":152,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:25.306+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":314,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:25.410+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":103,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:25.573+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:25.688+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":114,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:25.893+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":205,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:25.991+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:26.189+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":197,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:26.374+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":185,"影响行数":3}
{"level":"INFO","time":"2025-07-02T11:03:26.481+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:26.672+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":190,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:26.880+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":208,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:27.031+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":150,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:27.231+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":198,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:27.376+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":145,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:27.455+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:27.611+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":155,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:27.771+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":159,"影响行数":3}
{"level":"INFO","time":"2025-07-02T11:03:27.926+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":155,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:28.063+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:28.227+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:28.349+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":121,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:28.490+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:28.632+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:28.715+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:28.878+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:29.047+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":168,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:29.153+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:29.323+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:29.475+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":152,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:29.476+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T11:03:29.627+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T11:03:29.628+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T11:03:29.628+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T11:03:29.630+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T11:03:38.924+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":153,"影响行数":2}
{"level":"INFO","time":"2025-07-02T11:03:39.238+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '郑州' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":221,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:39.382+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`meet_room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','郑州','黄河会议室',189,'中国河南省郑州市','',10,'',1,NULL,'这是一个测试备注',0,'{\"type\":\"all\"}',0,'','',0,'',0,0,1,0,0,0,0,0,0)","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:19.562+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T11:32:19.564+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T11:32:19.565+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T11:32:25.672+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":51,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:25.944+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":254,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:26.262+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":318,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:26.407+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:26.563+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:26.859+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":296,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:27.084+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":223,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:27.274+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":190,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:27.638+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":362,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:27.983+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":344,"影响行数":3}
{"level":"INFO","time":"2025-07-02T11:32:28.186+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":202,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:28.468+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":282,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:28.780+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":311,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:28.966+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":185,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:29.283+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":316,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:29.670+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":386,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:29.820+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:30.009+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":188,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:30.321+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":311,"影响行数":3}
{"level":"INFO","time":"2025-07-02T11:32:30.467+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":146,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:30.789+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":320,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:31.049+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":259,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:31.191+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:31.409+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":218,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:31.770+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":360,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:31.956+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":185,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:32.289+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":332,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:32.454+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":164,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:32.528+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:32.663+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:32.891+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":227,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:32.892+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T11:32:33.200+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T11:32:33.201+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T11:32:33.201+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T11:32:33.202+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T11:32:42.499+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":497,"影响行数":41}
{"level":"INFO","time":"2025-07-02T11:38:12.572+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":292,"影响行数":41}
{"level":"INFO","time":"2025-07-02T11:38:15.190+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":302,"影响行数":41}
{"level":"INFO","time":"2025-07-02T13:46:29.465+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T13:46:29.467+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T13:46:29.468+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T13:46:45.302+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":82,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:45.513+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":183,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:45.690+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":175,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:45.794+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":103,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:46.022+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":227,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:46.263+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":241,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:46.503+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":239,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:46.590+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:46.753+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":163,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:46.910+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":156,"影响行数":3}
{"level":"INFO","time":"2025-07-02T13:46:47.000+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:47.209+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":208,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:47.454+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":245,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:47.562+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:47.840+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":278,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:48.120+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":279,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:48.240+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:48.455+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":214,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:48.819+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":363,"影响行数":3}
{"level":"INFO","time":"2025-07-02T13:46:48.964+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":145,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:49.145+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":180,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:49.344+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:49.424+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:49.585+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:49.774+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":188,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:49.864+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:50.055+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":191,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:50.265+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":209,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:50.407+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:50.585+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":178,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:50.800+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":214,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:50.800+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T13:46:50.969+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T13:46:50.970+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T13:46:50.971+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T13:46:50.972+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T13:46:56.500+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":412,"影响行数":41}
{"level":"INFO","time":"2025-07-02T13:47:15.900+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":593,"影响行数":41}
{"level":"INFO","time":"2025-07-02T13:49:54.336+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T13:49:54.342+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T13:49:54.343+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T13:50:04.063+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":172,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:04.264+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":182,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:04.423+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:04.529+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":105,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:04.689+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:04.860+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":171,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:05.066+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":205,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:05.237+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":170,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:05.417+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:05.668+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":251,"影响行数":3}
{"level":"INFO","time":"2025-07-02T13:50:05.805+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":136,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:05.994+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":188,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:06.168+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":173,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:06.235+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:06.667+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":431,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:07.023+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":356,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:07.136+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":112,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:07.385+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":248,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:07.633+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":247,"影响行数":3}
{"level":"INFO","time":"2025-07-02T13:50:07.723+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:07.927+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":204,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:08.085+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":157,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:08.191+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:08.413+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":222,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:08.566+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":151,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:08.616+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":48,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:08.776+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:08.970+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":193,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:09.035+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:09.316+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":280,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:09.823+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":507,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:09.823+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T13:50:10.506+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T13:50:10.507+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T13:50:10.507+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T13:50:10.509+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T13:50:17.057+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":317,"影响行数":41}
{"level":"INFO","time":"2025-07-02T14:13:33.316+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 1 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":273,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:37:28.207+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T14:37:28.208+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T14:37:28.208+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T14:37:48.689+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":191,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:48.850+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:37:49.129+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":279,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:49.211+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":81,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:49.445+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":233,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:37:49.729+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":283,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:50.038+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":308,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:50.174+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":134,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:50.364+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":189,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:37:50.665+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":300,"影响行数":3}
{"level":"INFO","time":"2025-07-02T14:37:50.781+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:51.001+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":219,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:37:51.202+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:51.296+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:51.455+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:37:51.674+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":218,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:51.812+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:52.194+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":381,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:37:52.477+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":282,"影响行数":3}
{"level":"INFO","time":"2025-07-02T14:37:52.631+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":153,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:52.970+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":339,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:37:53.313+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":343,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:53.514+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:53.817+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":303,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:37:54.581+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":763,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:55.037+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":454,"影响行数":-1}
{"level":"WARN","time":"2025-07-02T14:37:57.836+0800","caller":"model/logger.go:81","msg":"GORM慢查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":2799,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:37:58.764+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":927,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:59.050+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":285,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:59.582+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":530,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:37:59.980+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":397,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:37:59.980+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T14:38:00.174+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T14:38:00.177+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T14:38:00.177+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T14:38:00.178+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T14:39:45.417+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T14:39:45.418+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T14:39:45.418+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T14:39:48.379+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:48.717+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":323,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:39:49.001+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":283,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:49.143+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:49.438+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":294,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:39:49.934+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":495,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:50.502+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":370,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:50.647+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:51.232+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":584,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:39:51.566+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":334,"影响行数":3}
{"level":"INFO","time":"2025-07-02T14:39:51.658+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":91,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:51.904+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":245,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:39:52.289+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":384,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:52.528+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":239,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:52.883+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":354,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:39:53.164+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":280,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:53.294+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:53.594+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":299,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:39:53.941+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":347,"影响行数":3}
{"level":"INFO","time":"2025-07-02T14:39:54.111+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":169,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:54.518+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":406,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:39:55.001+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":483,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:55.193+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":191,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:55.537+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":344,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:39:55.914+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":376,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:56.148+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":232,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:56.444+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":296,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:39:56.970+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":524,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:57.211+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":240,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:57.601+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":389,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:39:57.877+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":275,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:39:57.877+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T14:39:58.145+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T14:39:58.147+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T14:39:58.147+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T14:39:58.148+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T14:41:46.551+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T14:41:46.552+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T14:41:46.552+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T14:41:48.759+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:49.069+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":280,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:41:49.364+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":294,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:49.448+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:49.765+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":316,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:41:50.116+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":351,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:50.299+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":183,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:50.414+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":114,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:50.653+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":239,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:41:50.881+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":227,"影响行数":3}
{"level":"INFO","time":"2025-07-02T14:41:51.000+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:51.154+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":153,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:41:51.283+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:51.375+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:51.559+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":182,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:41:51.740+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":180,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:51.839+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:52.054+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":214,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:41:52.326+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":272,"影响行数":3}
{"level":"INFO","time":"2025-07-02T14:41:52.400+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:52.703+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":302,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:41:52.871+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":167,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:52.933+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:53.174+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":240,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:41:53.383+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":208,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:53.504+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:53.641+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":137,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:41:53.843+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":201,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:53.921+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:54.123+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":202,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:41:54.585+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":461,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:41:54.586+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T14:41:54.778+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T14:41:54.779+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T14:41:54.779+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T14:41:54.781+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T14:41:59.862+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T14:41:59.863+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T14:41:59.863+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T14:42:02.931+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":229,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:03.565+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":606,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:42:03.886+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":320,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:04.080+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":192,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:04.394+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":314,"影响行数":1}
{"level":"WARN","time":"2025-07-02T14:42:05.994+0800","caller":"model/logger.go:81","msg":"GORM慢查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":1599,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:06.426+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":431,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:06.574+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":147,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:06.796+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":221,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:42:07.041+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":244,"影响行数":3}
{"level":"INFO","time":"2025-07-02T14:42:07.119+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:07.495+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":375,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:42:07.797+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":302,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:07.866+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:08.050+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":183,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:42:08.259+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":208,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:08.354+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:08.571+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":216,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:42:08.825+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":253,"影响行数":3}
{"level":"INFO","time":"2025-07-02T14:42:08.923+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:09.130+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":206,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:42:09.280+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:09.365+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:09.609+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":243,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:42:09.811+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":202,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:09.924+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":111,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:10.137+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":212,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:42:10.310+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":172,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:10.371+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:10.571+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:42:10.790+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":218,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:42:10.791+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T14:42:10.993+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T14:42:10.995+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T14:42:10.995+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T14:42:10.996+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T14:44:17.095+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T14:44:17.095+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T14:44:17.095+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T14:44:37.408+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":131,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:37.771+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":336,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:44:38.002+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":230,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:38.089+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":86,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:38.395+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":305,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:44:38.633+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":237,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:38.862+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":228,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:38.919+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":55,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:39.176+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":257,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:44:39.412+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":235,"影响行数":3}
{"level":"INFO","time":"2025-07-02T14:44:39.539+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:39.738+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":198,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:44:39.976+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":237,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:40.058+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":82,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:40.346+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":287,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:44:40.656+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":309,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:40.876+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":218,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:41.351+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":474,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:44:41.708+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":356,"影响行数":3}
{"level":"INFO","time":"2025-07-02T14:44:42.246+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":537,"影响行数":-1}
{"level":"WARN","time":"2025-07-02T14:44:44.431+0800","caller":"model/logger.go:81","msg":"GORM慢查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":2184,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:44:44.851+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":418,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:44.939+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:45.331+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":391,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:44:45.622+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":290,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:45.802+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":178,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:46.194+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":392,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:44:46.501+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":306,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:46.690+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":189,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:46.976+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":285,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:44:47.313+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":336,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T14:44:47.313+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T14:44:47.621+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T14:44:47.623+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T14:44:47.623+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T14:44:47.624+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T14:45:17.069+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE end_time > '2025-07-02 14:45:16.858' AND (FIND_IN_SET(22, room_ids) > 0 OR FIND_IN_SET(23, room_ids) > 0 OR FIND_IN_SET(24, room_ids) > 0) ORDER BY `eyc_meeting_bookings`.`id` LIMIT 1","耗时":211,"影响行数":0}
{"level":"INFO","time":"2025-07-02T14:45:17.227+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id IN (22,23,24)","耗时":158,"影响行数":3}
{"level":"INFO","time":"2025-07-02T14:45:17.405+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_room` WHERE id IN (22,23,24)","耗时":176,"影响行数":3}
{"level":"INFO","time":"2025-07-02T14:45:17.632+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=sort - 1,`updated_at`='2025-07-02 14:45:17.405' WHERE group_id = 0 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND sort > 84","耗时":227,"影响行数":2}
{"level":"INFO","time":"2025-07-02T14:48:05.782+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE end_time > '2025-07-02 14:48:05.46' AND (FIND_IN_SET(22, room_ids) > 0 OR FIND_IN_SET(23, room_ids) > 0 OR FIND_IN_SET(24, room_ids) > 0) ORDER BY `eyc_meeting_bookings`.`id` LIMIT 1","耗时":321,"影响行数":0}
{"level":"INFO","time":"2025-07-02T14:48:05.975+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id IN (22,23,24)","耗时":192,"影响行数":0}
{"level":"INFO","time":"2025-07-02T14:48:25.748+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE end_time > '2025-07-02 14:48:25.489' AND (FIND_IN_SET(19, room_ids) > 0 OR FIND_IN_SET(20, room_ids) > 0) ORDER BY `eyc_meeting_bookings`.`id` LIMIT 1","耗时":257,"影响行数":0}
{"level":"INFO","time":"2025-07-02T14:48:26.001+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id IN (19,20)","耗时":252,"影响行数":2}
{"level":"INFO","time":"2025-07-02T14:48:26.258+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_room` WHERE id IN (19,20)","耗时":257,"影响行数":2}
{"level":"INFO","time":"2025-07-02T14:48:26.416+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=sort - 1,`updated_at`='2025-07-02 14:48:26.259' WHERE group_id = 0 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND sort > 1","耗时":157,"影响行数":8}
{"level":"INFO","time":"2025-07-02T14:48:26.587+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=sort - 1,`updated_at`='2025-07-02 14:48:26.417' WHERE group_id = 0 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND sort > 1","耗时":169,"影响行数":7}
{"level":"INFO","time":"2025-07-02T14:51:26.869+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0","耗时":300,"影响行数":1}
{"level":"INFO","time":"2025-07-02T14:51:27.640+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":769,"影响行数":10}
{"level":"INFO","time":"2025-07-02T15:04:36.838+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":548,"影响行数":36}
{"level":"INFO","time":"2025-07-02T15:04:40.027+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":437,"影响行数":36}
{"level":"INFO","time":"2025-07-02T15:04:48.422+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":352,"影响行数":36}
{"level":"INFO","time":"2025-07-02T15:27:08.186+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T15:27:08.187+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T15:27:08.187+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T15:27:13.548+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:13.721+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":147,"影响行数":1}
{"level":"INFO","time":"2025-07-02T15:27:13.921+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:13.998+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":75,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:14.298+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":299,"影响行数":1}
{"level":"INFO","time":"2025-07-02T15:27:14.486+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":187,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:14.656+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":170,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:14.772+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":113,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:14.943+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":171,"影响行数":1}
{"level":"INFO","time":"2025-07-02T15:27:15.162+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":218,"影响行数":3}
{"level":"INFO","time":"2025-07-02T15:27:15.237+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:15.398+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":160,"影响行数":1}
{"level":"INFO","time":"2025-07-02T15:27:15.561+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:15.637+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":75,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:15.928+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":290,"影响行数":1}
{"level":"INFO","time":"2025-07-02T15:27:16.158+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":229,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:16.295+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":135,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:16.517+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":221,"影响行数":1}
{"level":"INFO","time":"2025-07-02T15:27:16.746+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":228,"影响行数":3}
{"level":"INFO","time":"2025-07-02T15:27:16.820+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:17.032+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":211,"影响行数":1}
{"level":"INFO","time":"2025-07-02T15:27:17.164+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":132,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:17.246+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":81,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:17.425+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":178,"影响行数":1}
{"level":"INFO","time":"2025-07-02T15:27:17.655+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":229,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:17.803+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":147,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:18.167+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":363,"影响行数":1}
{"level":"INFO","time":"2025-07-02T15:27:18.482+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":314,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:18.567+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:18.758+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":190,"影响行数":1}
{"level":"INFO","time":"2025-07-02T15:27:18.966+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":207,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T15:27:18.966+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T15:27:19.140+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T15:27:19.142+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T15:27:19.142+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T15:27:19.144+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T15:27:28.187+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":411,"影响行数":36}
{"level":"INFO","time":"2025-07-02T15:31:59.885+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":372,"影响行数":36}
{"level":"INFO","time":"2025-07-02T15:32:14.355+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":408,"影响行数":36}
{"level":"INFO","time":"2025-07-02T15:32:18.261+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":428,"影响行数":36}
{"level":"INFO","time":"2025-07-02T15:32:38.088+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":450,"影响行数":36}
{"level":"INFO","time":"2025-07-02T15:38:08.818+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":402,"影响行数":36}
{"level":"INFO","time":"2025-07-02T16:08:17.318+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":372,"影响行数":36}
{"level":"INFO","time":"2025-07-02T16:10:10.456+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":602,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:11:08.988+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":222,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:11:20.372+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":411,"影响行数":20}
{"level":"WARN","time":"2025-07-02T16:11:21.865+0800","caller":"model/logger.go:81","msg":"GORM慢查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0","耗时":1655,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:11:22.246+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":380,"影响行数":10}
{"level":"INFO","time":"2025-07-02T16:14:44.541+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":577,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:14:44.619+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0","耗时":417,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:14:44.961+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":342,"影响行数":10}
{"level":"INFO","time":"2025-07-02T16:16:09.992+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":203,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:16:27.176+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":157,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:16:44.236+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":218,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:17:01.816+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":321,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:17:19.784+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":217,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:17:36.193+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":150,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:17:55.630+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":231,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:18:15.844+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":187,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:18:35.618+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":196,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:18:46.528+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T16:18:46.529+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T16:18:46.529+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T16:18:51.460+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:18:51.695+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":208,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:18:51.893+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":197,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:18:52.108+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":213,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:18:52.866+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":758,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:18:53.331+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":464,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:18:53.656+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":324,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:18:53.824+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":167,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:18:54.019+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":194,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:18:54.333+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":312,"影响行数":3}
{"level":"INFO","time":"2025-07-02T16:18:54.423+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:18:54.769+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":345,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:18:55.123+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":354,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:18:55.355+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":231,"影响行数":-1}
{"level":"WARN","time":"2025-07-02T16:18:56.380+0800","caller":"model/logger.go:81","msg":"GORM慢查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":1024,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:18:56.873+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":491,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:18:56.998+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:18:57.444+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":445,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:18:57.784+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":340,"影响行数":3}
{"level":"INFO","time":"2025-07-02T16:18:57.948+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:18:58.591+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":643,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:18:59.147+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":555,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:18:59.231+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:18:59.502+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":271,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:18:59.688+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":185,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:18:59.858+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":170,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:19:00.199+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":341,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:19:00.470+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":271,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:19:00.589+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:19:00.919+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":330,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:19:01.211+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":291,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:19:01.212+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T16:19:02.470+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T16:19:02.471+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T16:19:02.660+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T16:19:02.661+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T16:19:13.593+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":444,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:19:26.758+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room`","耗时":118,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:19:27.231+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` ORDER BY sort ASC, id DESC LIMIT 10","耗时":473,"影响行数":10}
{"level":"INFO","time":"2025-07-02T16:19:38.191+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room`","耗时":121,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:19:38.488+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` ORDER BY sort ASC, id DESC LIMIT 10","耗时":297,"影响行数":10}
{"level":"INFO","time":"2025-07-02T16:19:48.364+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room`","耗时":92,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:19:48.715+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` ORDER BY sort ASC, id DESC LIMIT 10","耗时":350,"影响行数":10}
{"level":"INFO","time":"2025-07-02T16:19:54.661+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room`","耗时":112,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:19:54.967+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` ORDER BY sort ASC, id DESC LIMIT 10","耗时":305,"影响行数":10}
{"level":"INFO","time":"2025-07-02T16:22:48.829+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":304,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:23:05.759+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":275,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:23:06.557+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":806,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:23:06.759+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":202,"影响行数":10}
{"level":"INFO","time":"2025-07-02T16:27:18.675+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":137,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:27:47.082+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":175,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:29:00.289+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":242,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:29:13.285+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":214,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:31:45.214+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T16:31:45.214+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T16:31:45.214+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T16:36:14.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":103,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:15.101+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":239,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:36:15.363+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":261,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:15.455+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":91,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:15.638+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":181,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:36:15.834+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":195,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:16.021+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":187,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:16.076+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:16.217+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":141,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:36:16.411+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":193,"影响行数":3}
{"level":"INFO","time":"2025-07-02T16:36:16.483+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":72,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:16.656+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":172,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:36:16.837+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":180,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:16.913+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:17.276+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":362,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:36:17.622+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":345,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:17.769+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":146,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:18.078+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":308,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:36:18.348+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":269,"影响行数":3}
{"level":"INFO","time":"2025-07-02T16:36:18.476+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":127,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:18.697+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":219,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:36:18.835+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":138,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:18.926+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:19.076+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:36:19.234+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":157,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:19.326+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:19.453+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":126,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:36:19.587+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":132,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:19.642+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:19.781+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:36:19.961+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":179,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:36:19.962+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T16:36:20.175+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T16:36:20.176+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T16:36:20.176+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T16:36:20.177+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-07-02T16:36:44.621+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"会议室名称不能为空"}
{"level":"ERROR","time":"2025-07-02T16:37:04.549+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"会议室名称不能为空"}
{"level":"ERROR","time":"2025-07-02T16:37:26.633+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"会议室名称不能为空"}
{"level":"ERROR","time":"2025-07-02T16:37:33.540+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"会议室名称不能为空"}
{"level":"INFO","time":"2025-07-02T16:37:37.559+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":195,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:37:38.259+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":894,"影响行数":9}
{"level":"INFO","time":"2025-07-02T16:37:40.172+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":194,"影响行数":9}
{"level":"INFO","time":"2025-07-02T16:37:40.212+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":233,"影响行数":20}
{"level":"ERROR","time":"2025-07-02T16:38:25.923+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"会议室名称不能为空"}
{"level":"INFO","time":"2025-07-02T16:41:24.327+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":200,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:41:24.354+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":226,"影响行数":9}
{"level":"INFO","time":"2025-07-02T16:41:24.578+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":248,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:41:24.726+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":369,"影响行数":9}
{"level":"INFO","time":"2025-07-02T16:41:43.374+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":257,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:41:43.399+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":282,"影响行数":9}
{"level":"INFO","time":"2025-07-02T16:41:50.499+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":182,"影响行数":9}
{"level":"INFO","time":"2025-07-02T16:41:50.511+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":194,"影响行数":20}
{"level":"ERROR","time":"2025-07-02T16:43:21.189+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"会议室名称不能为空"}
{"level":"INFO","time":"2025-07-02T16:43:49.865+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":152,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:43:49.905+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":192,"影响行数":9}
{"level":"INFO","time":"2025-07-02T16:44:01.894+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":234,"影响行数":9}
{"level":"INFO","time":"2025-07-02T16:44:01.910+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":251,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:44:15.485+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":173,"影响行数":9}
{"level":"INFO","time":"2025-07-02T16:44:15.557+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":245,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:44:16.042+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":729,"影响行数":2}
{"level":"INFO","time":"2025-07-02T16:44:47.792+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":149,"影响行数":2}
{"level":"INFO","time":"2025-07-02T16:44:47.795+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":151,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:44:47.795+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":152,"影响行数":9}
{"level":"INFO","time":"2025-07-02T16:44:59.560+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":152,"影响行数":2}
{"level":"INFO","time":"2025-07-02T16:44:59.560+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":152,"影响行数":9}
{"level":"INFO","time":"2025-07-02T16:44:59.572+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":163,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:45:05.165+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":117,"影响行数":2}
{"level":"INFO","time":"2025-07-02T16:45:05.165+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":118,"影响行数":9}
{"level":"INFO","time":"2025-07-02T16:45:05.168+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":121,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:45:10.180+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":240,"影响行数":2}
{"level":"INFO","time":"2025-07-02T16:45:10.180+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":242,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:45:10.182+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":243,"影响行数":9}
{"level":"INFO","time":"2025-07-02T16:45:58.520+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T16:45:58.521+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T16:45:58.521+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T16:46:04.022+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:04.248+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":208,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:46:04.412+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":164,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:04.514+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:04.691+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":176,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:46:04.993+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":301,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:05.176+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":182,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:05.247+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:05.406+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:46:05.671+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":264,"影响行数":3}
{"level":"INFO","time":"2025-07-02T16:46:05.746+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:05.934+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":187,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:46:06.187+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":253,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:06.293+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":105,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:06.516+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":222,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:46:06.695+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":179,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:06.817+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:07.201+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":383,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:46:07.398+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":196,"影响行数":3}
{"level":"INFO","time":"2025-07-02T16:46:07.573+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":174,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:07.748+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":174,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:46:07.892+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:07.987+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:08.116+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:46:08.989+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":872,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:09.067+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:09.201+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:46:09.378+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":176,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:09.487+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":108,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:09.649+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":161,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:46:09.808+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:46:09.809+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T16:46:09.988+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T16:46:09.989+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T16:46:09.990+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T16:46:09.990+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-07-02T16:46:15.845+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"无效的数据格式，期望为 *model.EycMeetingRoom"}
{"level":"ERROR","time":"2025-07-02T16:46:40.268+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"无效的数据格式，期望为 *model.EycMeetingRoom"}
{"level":"INFO","time":"2025-07-02T16:47:59.033+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":147,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:47:59.479+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":593,"影响行数":2}
{"level":"INFO","time":"2025-07-02T16:47:59.481+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":594,"影响行数":9}
{"level":"INFO","time":"2025-07-02T16:48:40.685+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T16:48:40.686+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T16:48:40.686+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T16:48:45.080+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":60,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:45.209+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":102,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:48:45.329+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:45.392+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:45.557+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:48:45.695+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":131,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:45.829+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:45.895+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:46.025+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:48:46.165+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":139,"影响行数":3}
{"level":"INFO","time":"2025-07-02T16:48:46.236+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:46.404+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":168,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:48:46.600+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":195,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:46.650+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:46.766+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:48:46.939+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":173,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:47.013+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:47.180+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":166,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:48:47.317+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":136,"影响行数":3}
{"level":"INFO","time":"2025-07-02T16:48:47.399+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":82,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:47.538+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:48:47.667+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:47.726+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:47.840+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:48:47.965+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:48.067+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:48.181+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:48:48.291+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:48.395+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":102,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:48.605+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":209,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:48:48.754+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:48:48.754+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T16:48:48.881+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T16:48:48.882+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T16:48:48.882+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T16:48:48.883+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-07-02T16:48:52.967+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"无效的数据格式，期望为 map[string]string"}
{"level":"INFO","time":"2025-07-02T16:50:06.675+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":229,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:50:07.145+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":699,"影响行数":2}
{"level":"INFO","time":"2025-07-02T16:50:07.193+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":747,"影响行数":9}
{"level":"ERROR","time":"2025-07-02T16:50:42.804+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"无效的数据格式，期望为 map[string]string"}
{"level":"INFO","time":"2025-07-02T16:51:47.154+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":181,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:51:47.316+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":202,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:51:47.570+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":253,"影响行数":10}
{"level":"INFO","time":"2025-07-02T16:51:47.758+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE end_time > '2025-07-02 16:51:47.553' AND (FIND_IN_SET(19, room_ids) > 0 OR FIND_IN_SET(20, room_ids) > 0) ORDER BY `eyc_meeting_bookings`.`id` LIMIT 1","耗时":203,"影响行数":0}
{"level":"INFO","time":"2025-07-02T16:51:48.015+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id IN (19,20)","耗时":256,"影响行数":0}
{"level":"INFO","time":"2025-07-02T16:51:49.089+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":240,"影响行数":2}
{"level":"INFO","time":"2025-07-02T16:51:49.090+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":241,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:51:49.096+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":247,"影响行数":9}
{"level":"INFO","time":"2025-07-02T16:51:51.832+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":173,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:51:51.875+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:51:52.256+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":381,"影响行数":10}
{"level":"INFO","time":"2025-07-02T16:51:57.357+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":150,"影响行数":2}
{"level":"INFO","time":"2025-07-02T16:51:57.569+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '郑州' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:51:57.720+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','郑州','黄河会议室',189,'中国河南省郑州市','',10,'',1,NULL,'这是一个测试备注',0,'{\"type\":\"all\"}',0,'','',0,'',0,0,1,0,0,0,0,0,0)","耗时":150,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:53:28.638+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":203,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:54:25.265+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":265,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:55:12.005+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":142,"影响行数":20}
{"level":"INFO","time":"2025-07-02T16:56:04.640+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T16:56:04.641+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T16:56:04.641+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T16:56:11.061+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":178,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:11.341+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":252,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:56:11.661+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":320,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:11.812+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":151,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:12.034+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":221,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:56:12.281+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":246,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:12.578+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":297,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:12.721+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:12.846+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:56:13.123+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":277,"影响行数":3}
{"level":"INFO","time":"2025-07-02T16:56:13.263+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:13.461+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":198,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:56:13.717+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":254,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:13.826+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":108,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:14.186+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":360,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:56:14.461+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":272,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:14.552+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:14.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":284,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:56:15.091+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":254,"影响行数":3}
{"level":"INFO","time":"2025-07-02T16:56:15.167+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:15.349+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":181,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:56:15.555+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":205,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:15.627+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:15.804+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":177,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:56:16.196+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":391,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:16.267+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:16.468+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":200,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:56:16.647+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":178,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:16.724+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:16.897+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":172,"影响行数":1}
{"level":"INFO","time":"2025-07-02T16:56:17.176+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":278,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T16:56:17.176+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T16:56:17.326+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T16:56:17.328+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T16:56:17.328+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T16:56:17.330+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-07-02T17:00:13.991+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"无效的数据格式，期望为 *model.EycMeetingRoom"}
{"level":"INFO","time":"2025-07-02T17:00:49.153+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":187,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:01:06.601+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":212,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:01:36.204+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":112,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:03:25.110+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T17:03:25.110+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T17:03:25.111+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T17:03:29.681+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":75,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:29.814+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":106,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:03:29.981+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":166,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:30.061+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:30.194+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:03:30.319+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:30.511+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":191,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:30.570+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:30.733+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":163,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:03:30.913+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":179,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:03:30.958+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":44,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:31.080+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":121,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:03:31.211+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:31.279+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:31.589+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":310,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:03:31.750+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":160,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:31.813+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:31.948+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:03:32.145+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":196,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:03:32.211+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:32.395+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":183,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:03:32.538+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:32.598+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:32.770+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":171,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:03:32.900+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:32.974+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":72,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:33.100+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":126,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:03:33.256+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":155,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:33.327+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:33.464+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":137,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:03:33.601+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:03:33.601+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T17:03:33.808+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T17:03:33.809+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T17:03:33.809+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T17:03:33.810+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T17:03:37.953+0800","caller":"service/EycMeetingRoomService.go:185","msg":"DEBUG: AddService 接收到的数据类型: map[string]interface {}, 值: map[capacity:20 corpid:ding424e63f5c9ac81e1ffe93478753d9884 device:设备1,设备2 facility:投影仪,白板 group_id:182 image: is_open_booking:1 location:3楼东侧 remark:领导专用]"}
{"level":"ERROR","time":"2025-07-02T17:03:37.953+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"无效的数据格式，期望为 *model.EycMeetingRoom"}
{"level":"INFO","time":"2025-07-02T17:03:52.878+0800","caller":"service/EycMeetingRoomService.go:185","msg":"DEBUG: AddService 接收到的数据类型: map[string]interface {}, 值: map[capacity:20 corpid:ding424e63f5c9ac81e1ffe93478753d9884 device:设备1,设备2 facility:投影仪,白板 group_id:182 image: is_open_booking:1 location:3楼东侧 remark:领导专用]"}
{"level":"ERROR","time":"2025-07-02T17:03:52.879+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"无效的数据格式，期望为 *model.EycMeetingRoom"}
{"level":"INFO","time":"2025-07-02T17:04:02.221+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":233,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:04:40.785+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE end_time > '2025-07-02 17:04:40.641' AND (FIND_IN_SET(42, room_ids) > 0 OR FIND_IN_SET(40, room_ids) > 0) ORDER BY `eyc_meeting_bookings`.`id` LIMIT 1","耗时":143,"影响行数":0}
{"level":"INFO","time":"2025-07-02T17:04:40.984+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id IN (42,40)","耗时":198,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:04:41.181+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_room` WHERE id IN (42,40)","耗时":197,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:04:41.365+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=sort - 1,`updated_at`='2025-07-02 17:04:41.183' WHERE group_id = 0 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND sort > 1","耗时":183,"影响行数":6}
{"level":"INFO","time":"2025-07-02T17:04:41.486+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=sort - 1,`updated_at`='2025-07-02 17:04:41.366' WHERE group_id = 0 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND sort > 0","耗时":120,"影响行数":11}
{"level":"INFO","time":"2025-07-02T17:04:41.819+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":194,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:04:41.977+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":157,"影响行数":10}
{"level":"INFO","time":"2025-07-02T17:06:08.014+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":165,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:06:24.704+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":217,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:06:49.338+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T17:06:49.338+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T17:06:49.338+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T17:06:54.167+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:54.293+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:06:54.402+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:54.456+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:54.652+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":195,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:06:54.759+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:54.864+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":105,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:54.924+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:55.027+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":103,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:06:55.168+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":140,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:06:55.217+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":48,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:55.368+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":151,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:06:55.493+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:55.606+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":113,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:55.722+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:06:55.854+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":131,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:55.912+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:56.030+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":117,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:06:56.193+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":162,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:06:56.273+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:56.377+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":103,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:06:56.588+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":210,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:56.653+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:56.845+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":191,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:06:57.004+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:57.070+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:57.205+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:06:57.319+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":113,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:57.378+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:57.474+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":96,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:06:57.620+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":146,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:06:57.620+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T17:06:57.747+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T17:06:57.749+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T17:06:57.749+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T17:06:57.750+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-07-02T17:07:11.280+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"会议室名称不能为空"}
{"level":"ERROR","time":"2025-07-02T17:07:24.545+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"会议室名称不能为空"}
{"level":"ERROR","time":"2025-07-02T17:07:30.483+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"会议室名称不能为空"}
{"level":"INFO","time":"2025-07-02T17:07:52.150+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE end_time > '2025-07-02 17:07:51.926' AND (FIND_IN_SET(38, room_ids) > 0 OR FIND_IN_SET(35, room_ids) > 0) ORDER BY `eyc_meeting_bookings`.`id` LIMIT 1","耗时":223,"影响行数":0}
{"level":"INFO","time":"2025-07-02T17:07:52.336+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id IN (38,35)","耗时":185,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:07:52.456+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"DELETE FROM `eyc_meeting_room` WHERE id IN (38,35)","耗时":120,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:07:52.728+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=sort - 1,`updated_at`='2025-07-02 17:07:52.457' WHERE group_id = 0 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND sort > 0","耗时":271,"影响行数":5}
{"level":"INFO","time":"2025-07-02T17:07:52.929+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=sort - 1,`updated_at`='2025-07-02 17:07:52.729' WHERE group_id = 0 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND sort > 0","耗时":200,"影响行数":4}
{"level":"INFO","time":"2025-07-02T17:07:53.488+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":337,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:07:53.734+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":245,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:08:10.813+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:08:11.014+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":200,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:08:48.057+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":142,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:08:52.069+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":210,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:08:52.309+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":240,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:12:48.760+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":124,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:12:49.104+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":467,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:12:49.143+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":507,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:13:02.413+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":137,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:13:02.463+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":102,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:13:02.604+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":140,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:13:26.433+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T17:13:26.434+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T17:13:26.434+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T17:15:03.575+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:03.728+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:15:03.921+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":192,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:04.045+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":123,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:04.218+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":173,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:15:04.378+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:04.662+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":283,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:04.777+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":113,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:05.030+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":253,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:15:05.256+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":226,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:15:05.317+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":60,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:05.557+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":239,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:15:05.776+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":219,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:05.851+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:06.056+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":205,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:15:06.277+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":220,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:06.371+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:06.558+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":186,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:15:06.782+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":224,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:15:06.842+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:07.020+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":177,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:15:07.258+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":236,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:07.380+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:07.602+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":221,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:15:07.760+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":157,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:07.899+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":138,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:08.041+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:15:08.232+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":190,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:08.300+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:08.541+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":240,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:15:08.781+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":239,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:15:08.781+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T17:15:08.960+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T17:15:08.962+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T17:15:08.962+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T17:15:08.963+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-07-02T17:15:16.390+0800","caller":"controller/base.go:534","msg":"新增记录失败","error":"会议室名称不能为空"}
{"level":"INFO","time":"2025-07-02T17:17:37.526+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":150,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:17:38.184+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":808,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:17:38.261+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":884,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:17:43.343+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":253,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:17:43.443+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":271,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:17:43.684+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":241,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:17:47.664+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 41 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":113,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:17:47.664+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":114,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:17:47.664+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":112,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:17:48.139+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":588,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:17:59.106+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":187,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:17:59.350+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":105,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:17:59.522+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":171,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:19:07.441+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T17:19:07.541+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T17:19:07.541+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T17:19:17.537+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:17.672+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":122,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:19:17.781+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:17.863+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:18.002+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":139,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:19:18.145+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:18.303+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":157,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:18.344+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":40,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:18.468+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":123,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:19:18.598+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":130,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:19:18.642+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":42,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:18.775+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":133,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:19:18.947+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":171,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:19.014+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:19.190+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":175,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:19:19.327+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:19.402+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:19.518+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:19:19.670+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":151,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:19:19.722+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":52,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:19.831+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":107,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:19:20.002+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":170,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:20.070+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:20.199+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":128,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:19:20.342+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:20.379+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":36,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:20.507+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":126,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:19:20.683+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":176,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:20.742+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:20.858+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:19:20.986+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:19:20.986+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T17:19:21.110+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T17:19:21.112+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T17:19:21.112+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T17:19:21.114+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T17:21:41.273+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T17:21:41.273+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T17:21:41.273+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T17:21:46.084+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:46.304+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":191,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:21:46.475+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":171,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:46.543+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:46.766+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":223,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:21:46.925+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:47.144+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":218,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:47.187+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":41,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:47.352+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":165,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:21:47.527+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":174,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:21:47.604+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:47.692+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":87,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:21:47.827+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":134,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:47.919+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":91,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:48.077+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":156,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:21:48.247+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":169,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:48.312+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:48.467+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:21:48.640+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":173,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:21:48.705+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:48.887+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":181,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:21:49.046+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:49.124+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:49.270+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:21:49.488+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":217,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:49.565+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":75,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:49.705+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":139,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:21:49.913+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":207,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:49.998+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:50.140+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":141,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:21:50.327+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":186,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:21:50.327+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T17:21:50.487+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T17:21:50.488+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T17:21:50.489+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T17:21:50.490+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-07-02T17:21:54.852+0800","caller":"controller/base.go:536","msg":"新增记录失败","error":"会议室名称不能为空"}
{"level":"INFO","time":"2025-07-02T17:22:24.608+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":231,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:22:25.201+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":824,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:22:25.202+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":825,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:22:25.226+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 41 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":849,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:26:53.268+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 41 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":102,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:26:53.270+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":105,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:26:53.270+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":105,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:26:53.314+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":148,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:31:30.563+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":118,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:31:30.564+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":119,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:31:30.565+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":120,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:31:30.577+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 41 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:34:25.484+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 41 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:34:25.487+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":172,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:34:25.488+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":173,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:34:25.488+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":173,"影响行数":20}
{"level":"ERROR","time":"2025-07-02T17:36:03.128+0800","caller":"controller/base.go:536","msg":"新增记录失败","error":"会议室名称不能为空"}
{"level":"INFO","time":"2025-07-02T17:36:31.767+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":128,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:36:32.032+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":93,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:36:32.147+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":115,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:37:02.943+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":118,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:37:23.813+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":103,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:37:23.814+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":104,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:37:23.815+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 41 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:37:23.816+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":106,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:39:13.247+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":130,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:39:13.248+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":131,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:39:13.248+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 41 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:39:13.249+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":133,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:39:31.326+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 41 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":103,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:39:31.326+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":103,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:39:31.326+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":103,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:39:31.343+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":121,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:39:49.330+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":144,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:39:49.331+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":145,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:39:49.332+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":146,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:39:49.332+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 41 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:40:12.506+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T17:40:12.507+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T17:40:12.507+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T17:40:17.804+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":40,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:17.922+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":90,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:40:18.153+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":230,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:18.213+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:18.348+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:40:18.493+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:18.643+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:18.690+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":45,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:18.804+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":113,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:40:18.983+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":179,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:40:19.045+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:19.142+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":95,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:40:19.252+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:19.303+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":51,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:19.441+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:40:19.565+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":123,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:19.608+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":42,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:19.738+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":128,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:40:19.822+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":83,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:40:19.869+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:19.982+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":112,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:40:20.083+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:20.131+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":48,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:20.238+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":106,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:40:20.331+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":91,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:20.378+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:20.463+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":84,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:40:20.586+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":123,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:20.644+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:20.739+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":95,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:40:20.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:40:20.837+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T17:40:20.966+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T17:40:20.969+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T17:40:20.969+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T17:40:20.971+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"ERROR","time":"2025-07-02T17:40:24.818+0800","caller":"controller/base.go:536","msg":"新增记录失败","error":"会议室名称不能为空"}
{"level":"ERROR","time":"2025-07-02T17:40:28.355+0800","caller":"controller/base.go:536","msg":"新增记录失败","error":"会议室名称不能为空"}
{"level":"ERROR","time":"2025-07-02T17:40:30.325+0800","caller":"controller/base.go:536","msg":"新增记录失败","error":"会议室名称不能为空"}
{"level":"INFO","time":"2025-07-02T17:41:14.362+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":222,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:41:14.831+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":689,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:41:14.832+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 41 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":690,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:41:14.843+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":702,"影响行数":9}
{"level":"WARN","time":"2025-07-02T17:42:26.873+0800","caller":"model/logger.go:81","msg":"GORM慢查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":1364,"影响行数":2}
{"level":"WARN","time":"2025-07-02T17:42:26.873+0800","caller":"model/logger.go:81","msg":"GORM慢查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 41 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":1364,"影响行数":1}
{"level":"WARN","time":"2025-07-02T17:42:26.873+0800","caller":"model/logger.go:81","msg":"GORM慢查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":1365,"影响行数":9}
{"level":"WARN","time":"2025-07-02T17:42:26.873+0800","caller":"model/logger.go:81","msg":"GORM慢查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":1365,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:44:17.439+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":139,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:44:17.440+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 41 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":139,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:44:17.440+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":140,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:44:17.440+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":140,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:44:48.675+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T17:44:48.676+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T17:44:48.676+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T17:44:53.676+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:53.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":143,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:44:54.004+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":167,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:54.070+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:54.235+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:44:54.381+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":145,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:54.534+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":151,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:54.603+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:54.743+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:44:55.140+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":396,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:44:55.197+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:55.304+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":106,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:44:55.389+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:55.446+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:55.598+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":150,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:44:55.691+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:55.737+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:55.893+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":155,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:44:56.050+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":156,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:44:56.116+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:56.249+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:44:56.362+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":112,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:56.440+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:56.551+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:44:56.678+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:56.746+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:56.886+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":139,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:44:57.014+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":127,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:57.115+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:57.248+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":133,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:44:57.372+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:44:57.372+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T17:44:57.482+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T17:44:57.484+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T17:44:57.484+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T17:44:57.485+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T17:45:08.403+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','OP组','亚马逊森林会议室11',1,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,'\"\"','领导专用',0,'\"全部员工\"',0,'全部','全部','08:00:00','18:00:00',7,'07:00:00',30,240,1,0,0,10,15,0,20)","耗时":134,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:45:08.576+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 1 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":172,"影响行数":0}
{"level":"ERROR","time":"2025-07-02T17:45:08.629+0800","caller":"controller/base.go:536","msg":"新增记录失败","error":"查找新分组失败: record not found"}
{"level":"INFO","time":"2025-07-02T17:45:08.995+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":103,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:45:09.399+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":252,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:45:09.567+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":167,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:45:16.693+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":173,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:45:16.953+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":431,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:45:16.974+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 29 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":451,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:45:16.988+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":465,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:45:25.035+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','OP组','亚马逊森林会议室11',1,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,'\"\"','领导专用',0,'\"全部员工\"',0,'全部','全部','08:00:00','18:00:00',7,'07:00:00',30,240,1,0,0,10,15,0,20)","耗时":141,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:45:25.163+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 1 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":128,"影响行数":0}
{"level":"ERROR","time":"2025-07-02T17:45:25.233+0800","caller":"controller/base.go:536","msg":"新增记录失败","error":"查找新分组失败: record not found"}
{"level":"INFO","time":"2025-07-02T17:45:42.132+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','OP组','亚马逊森林会议室11',1,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,'\"\"','领导专用',0,'\"全部员工\"',0,'全部','全部','08:00:00','18:00:00',7,'07:00:00',30,240,1,0,0,10,15,0,20)","耗时":236,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:45:42.590+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 1 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":458,"影响行数":0}
{"level":"ERROR","time":"2025-07-02T17:45:42.921+0800","caller":"controller/base.go:536","msg":"新增记录失败","error":"查找新分组失败: record not found"}
{"level":"INFO","time":"2025-07-02T17:45:49.805+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','OP组','亚马逊森林会议室11',1,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,'\"\"','领导专用',0,'\"全部员工\"',0,'全部','全部','08:00:00','18:00:00',7,'07:00:00',30,240,1,0,0,10,15,0,20)","耗时":93,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:45:49.908+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 1 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":102,"影响行数":0}
{"level":"ERROR","time":"2025-07-02T17:45:49.981+0800","caller":"controller/base.go:536","msg":"新增记录失败","error":"查找新分组失败: record not found"}
{"level":"INFO","time":"2025-07-02T17:48:16.733+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T17:48:16.734+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T17:48:16.734+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T17:48:21.403+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:21.690+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":259,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:48:21.895+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":204,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:22.032+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":136,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:22.331+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":298,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:48:22.459+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:22.650+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":190,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:22.710+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:22.859+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":147,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:48:23.049+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":189,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:48:23.113+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:23.311+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":196,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:48:23.461+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":150,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:23.586+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:23.777+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":190,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:48:23.919+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:24.019+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:24.238+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":219,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:48:24.461+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":223,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:48:24.568+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":107,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:24.736+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":166,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:48:24.869+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:24.998+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":127,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:25.372+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":372,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:48:25.594+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":222,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:25.791+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":196,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:26.187+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":395,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:48:26.415+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":227,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:26.486+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:26.676+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":188,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:48:26.854+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":178,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:26.855+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T17:48:27.043+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T17:48:27.045+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T17:48:27.045+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T17:48:27.045+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T17:48:31.337+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT IFNULL(MAX(sort), 0) FROM `eyc_meeting_room` WHERE group_id = 1 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":232,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:48:31.533+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','OP组','亚马逊森林会议室11',1,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,'\"\"','领导专用',1,'\"全部员工\"',0,'全部','全部','08:00:00','18:00:00',7,'07:00:00',30,240,1,0,0,10,15,0,20)","耗时":192,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:48:31.795+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 1 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":262,"影响行数":0}
{"level":"ERROR","time":"2025-07-02T17:48:31.895+0800","caller":"controller/base.go:536","msg":"新增记录失败","error":"查找新分组失败: record not found"}
{"level":"INFO","time":"2025-07-02T17:51:53.170+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":184,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:51:53.703+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":717,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:51:53.705+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":719,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:51:53.706+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 29 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":720,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:52:13.955+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T17:52:13.955+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T17:52:13.955+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T17:52:18.030+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:18.305+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":261,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:52:18.482+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":176,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:18.557+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:18.683+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:52:18.884+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":201,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:19.101+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":216,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:19.251+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:19.482+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":230,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:52:19.648+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":166,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:52:19.714+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:19.843+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":128,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:52:19.983+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:20.062+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:20.259+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":196,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:52:20.481+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":222,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:20.600+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:20.778+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":176,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:52:20.908+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":130,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:52:20.981+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:21.176+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":194,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:52:21.413+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":236,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:21.491+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:21.636+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:52:21.852+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":215,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:21.976+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:22.091+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:52:22.199+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":108,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:22.281+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":81,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:22.481+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:52:22.681+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:52:22.681+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T17:52:22.820+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T17:52:22.821+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T17:52:22.821+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T17:52:22.822+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T17:52:29.801+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT IFNULL(MAX(sort), 0) FROM `eyc_meeting_room` WHERE group_id = 1 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":161,"影响行数":-1}
{"level":"ERROR","time":"2025-07-02T17:52:29.923+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','OP组','亚马逊森林会议室11',1,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,NULL,'领导专用',1,NULL,0,'','','','',0,'',0,0,1,0,0,0,0,0,0)","耗时":121,"error":"Error 1292 (22007): Incorrect datetime value: '' for column 'open_start_time' at row 1"}
{"level":"ERROR","time":"2025-07-02T17:52:30.051+0800","caller":"controller/base.go:553","msg":"新增记录失败","error":"Error 1292 (22007): Incorrect datetime value: '' for column 'open_start_time' at row 1"}
{"level":"INFO","time":"2025-07-02T17:54:02.443+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T17:54:02.443+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T17:54:02.444+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T17:54:06.203+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":42,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:06.335+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:54:06.481+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":145,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:06.518+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":36,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:06.641+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":122,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:54:06.757+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:06.863+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:06.938+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:07.064+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:54:07.183+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":119,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:54:07.243+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:07.436+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":192,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:54:07.543+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:07.603+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:07.770+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":166,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:54:07.968+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":197,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:08.023+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:08.191+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":168,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:54:08.443+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":251,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:54:08.536+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:08.653+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:54:08.811+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":157,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:08.882+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:09.025+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":142,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:54:09.163+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:09.232+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:09.345+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":112,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:54:09.491+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":146,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:09.545+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":52,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:09.743+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":197,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:54:09.937+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":193,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:09.937+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T17:54:10.118+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T17:54:10.119+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T17:54:10.119+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T17:54:10.120+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T17:54:18.495+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT IFNULL(MAX(sort), 0) FROM `eyc_meeting_room` WHERE group_id = 1 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":231,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:54:18.701+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','OP组','亚马逊森林会议室11',1,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,NULL,'领导专用',1,NULL,0,'全部','全部','08:00:00','18:00:00',7,'07:00:00',30,240,1,0,0,10,15,0,20)","耗时":203,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:54:18.922+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 1 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":221,"影响行数":0}
{"level":"ERROR","time":"2025-07-02T17:54:18.985+0800","caller":"controller/base.go:553","msg":"新增记录失败","error":"查找新分组失败: record not found"}
{"level":"INFO","time":"2025-07-02T17:56:22.918+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":133,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:56:23.254+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":469,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:56:23.254+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 29 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":469,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:56:23.277+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":493,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:56:54.646+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":104,"影响行数":2}
{"level":"INFO","time":"2025-07-02T17:56:54.658+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 29 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":117,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:56:54.668+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":127,"影响行数":20}
{"level":"INFO","time":"2025-07-02T17:56:54.740+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":199,"影响行数":9}
{"level":"INFO","time":"2025-07-02T17:57:25.336+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room`","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:57:25.536+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` ORDER BY sort ASC, id DESC LIMIT 10","耗时":200,"影响行数":10}
{"level":"INFO","time":"2025-07-02T17:57:35.912+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room`","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:57:36.132+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` ORDER BY sort ASC, id DESC LIMIT 10","耗时":220,"影响行数":10}
{"level":"INFO","time":"2025-07-02T17:57:42.911+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room`","耗时":90,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:57:43.103+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` ORDER BY sort ASC, id DESC LIMIT 10","耗时":191,"影响行数":10}
{"level":"INFO","time":"2025-07-02T17:58:35.901+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room`","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:58:36.066+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` ORDER BY sort ASC, id DESC LIMIT 10","耗时":164,"影响行数":10}
{"level":"INFO","time":"2025-07-02T17:58:43.724+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room`","耗时":66,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:58:43.881+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` ORDER BY sort ASC, id DESC LIMIT 10","耗时":156,"影响行数":10}
{"level":"INFO","time":"2025-07-02T17:59:45.108+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T17:59:45.109+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T17:59:45.109+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T17:59:48.695+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:48.870+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":148,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:59:48.987+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:49.029+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":41,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:49.175+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:59:49.286+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:49.427+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:49.480+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":52,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:49.628+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":147,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:59:49.754+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":126,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:59:49.819+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:49.947+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:59:50.074+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:50.134+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:50.272+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:59:50.390+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:50.463+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:50.584+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:59:50.695+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":111,"影响行数":3}
{"level":"INFO","time":"2025-07-02T17:59:50.735+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":39,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:50.840+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":104,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:59:50.990+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:51.091+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:51.221+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:59:51.372+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":150,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:51.425+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":52,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:51.555+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:59:51.726+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":171,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:51.781+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:51.921+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:59:52.052+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T17:59:52.052+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T17:59:52.207+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T17:59:52.207+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T17:59:52.207+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T17:59:52.208+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T17:59:56.529+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room`","耗时":47,"影响行数":1}
{"level":"INFO","time":"2025-07-02T17:59:56.653+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` ORDER BY sort ASC, id DESC LIMIT 10","耗时":124,"影响行数":10}
{"level":"INFO","time":"2025-07-02T18:00:01.811+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT IFNULL(MAX(sort), 0) FROM `eyc_meeting_room` WHERE group_id = 1 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":138,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:00:01.948+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','OP组','亚马逊森林会议室11',1,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,NULL,'领导专用',1,NULL,0,'全部','全部','08:00:00','18:00:00',7,'07:00:00',30,240,1,0,0,10,15,0,20)","耗时":137,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:00:02.081+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 1 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":132,"影响行数":0}
{"level":"ERROR","time":"2025-07-02T18:00:02.152+0800","caller":"controller/base.go:553","msg":"新增记录失败","error":"查找新分组失败: record not found"}
{"level":"INFO","time":"2025-07-02T18:00:04.093+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT IFNULL(MAX(sort), 0) FROM `eyc_meeting_room` WHERE group_id = 1 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":91,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:00:04.192+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','OP组','亚马逊森林会议室11',1,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,NULL,'领导专用',1,NULL,0,'全部','全部','08:00:00','18:00:00',7,'07:00:00',30,240,1,0,0,10,15,0,20)","耗时":98,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:00:04.328+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 1 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":135,"影响行数":0}
{"level":"ERROR","time":"2025-07-02T18:00:04.393+0800","caller":"controller/base.go:553","msg":"新增记录失败","error":"查找新分组失败: record not found"}
{"level":"INFO","time":"2025-07-02T18:00:07.434+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT IFNULL(MAX(sort), 0) FROM `eyc_meeting_room` WHERE group_id = 1 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:00:07.584+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','OP组','亚马逊森林会议室11',1,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,NULL,'领导专用',1,NULL,0,'全部','全部','08:00:00','18:00:00',7,'07:00:00',30,240,1,0,0,10,15,0,20)","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:00:07.704+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 1 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":119,"影响行数":0}
{"level":"ERROR","time":"2025-07-02T18:00:07.787+0800","caller":"controller/base.go:553","msg":"新增记录失败","error":"查找新分组失败: record not found"}
{"level":"INFO","time":"2025-07-02T18:02:23.682+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T18:02:23.682+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T18:02:23.683+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T18:02:28.132+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:28.407+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":260,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:02:28.663+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":256,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:28.714+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:28.879+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:02:29.010+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":131,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:29.152+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:29.232+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:29.416+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":182,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:02:29.651+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":235,"影响行数":3}
{"level":"INFO","time":"2025-07-02T18:02:29.732+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:29.891+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":158,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:02:30.052+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":161,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:30.131+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:30.331+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:02:30.691+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":359,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:30.816+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:31.011+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":195,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:02:31.212+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":200,"影响行数":3}
{"level":"INFO","time":"2025-07-02T18:02:31.265+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:31.392+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":126,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:02:31.572+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":179,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:31.621+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":47,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:31.791+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":170,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:02:31.952+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":161,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:32.093+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:32.271+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":177,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:02:32.451+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":178,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:32.545+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:32.752+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":206,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:02:32.951+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":198,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:32.951+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T18:02:33.106+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T18:02:33.107+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T18:02:33.107+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T18:02:33.109+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T18:02:42.951+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT IFNULL(MAX(sort), 0) FROM `eyc_meeting_room` WHERE group_id = 1 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":162,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:43.106+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','OP组','亚马逊森林会议室11',1,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,'\"\"','领导专用',1,'\"全部员工\"',0,'全部','全部','08:00:00','18:00:00',7,'07:00:00',30,240,1,0,0,10,15,0,20)","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:02:43.302+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 1 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":195,"影响行数":0}
{"level":"ERROR","time":"2025-07-02T18:02:43.371+0800","caller":"controller/base.go:535","msg":"新增记录失败","error":"查找新分组失败: record not found"}
{"level":"INFO","time":"2025-07-02T18:02:57.295+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T18:02:57.296+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T18:02:57.296+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T18:02:59.748+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":105,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:02:59.968+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":207,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:00.187+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":218,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:00.297+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:00.548+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":250,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:00.687+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:00.855+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":167,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:00.981+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":125,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:01.188+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":205,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:01.349+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":160,"影响行数":3}
{"level":"INFO","time":"2025-07-02T18:03:01.448+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:01.587+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":139,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:01.824+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":236,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:01.927+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":102,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:02.176+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":249,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:02.424+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":247,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:02.522+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:02.773+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":249,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:02.871+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":97,"影响行数":3}
{"level":"INFO","time":"2025-07-02T18:03:02.966+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:03.152+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":185,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:03.349+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":197,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:03.448+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:03.647+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:03.916+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":267,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:03.991+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:04.228+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":235,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:04.458+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":229,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:04.618+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:04.844+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":225,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:05.057+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":213,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:05.058+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T18:03:05.352+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T18:03:05.352+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T18:03:05.352+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T18:03:05.353+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T18:03:06.787+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T18:03:06.787+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T18:03:06.787+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T18:03:10.527+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":113,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:10.742+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":191,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:10.907+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":164,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:11.008+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:11.129+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":119,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:11.301+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":172,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:11.453+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":151,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:11.524+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:11.687+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:11.827+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":140,"影响行数":3}
{"level":"INFO","time":"2025-07-02T18:03:11.926+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:12.127+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":201,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:12.315+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":187,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:12.428+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":111,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:12.613+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":184,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:12.867+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":252,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:12.972+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":105,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:13.167+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":194,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:13.347+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":178,"影响行数":3}
{"level":"INFO","time":"2025-07-02T18:03:13.431+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:13.660+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":228,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:13.868+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":207,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:13.952+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:14.169+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":216,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:14.333+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:14.425+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:14.602+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":176,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:14.725+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:14.801+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:14.911+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":108,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:15.046+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":134,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:15.047+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T18:03:15.166+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T18:03:15.167+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T18:03:15.167+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T18:03:15.169+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T18:03:21.240+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT IFNULL(MAX(sort), 0) FROM `eyc_meeting_room` WHERE group_id = 1 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":193,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:03:21.395+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','OP组','亚马逊森林会议室11',1,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,'\"\"','领导专用',1,'\"全部员工\"',0,'全部','全部','08:00:00','18:00:00',7,'07:00:00',30,240,1,0,0,10,15,0,20)","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:03:21.523+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 1 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":125,"影响行数":0}
{"level":"ERROR","time":"2025-07-02T18:03:21.606+0800","caller":"controller/base.go:535","msg":"新增记录失败","error":"查找新分组失败: record not found"}
{"level":"INFO","time":"2025-07-02T18:05:19.335+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":112,"影响行数":20}
{"level":"INFO","time":"2025-07-02T18:05:19.639+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":416,"影响行数":2}
{"level":"INFO","time":"2025-07-02T18:05:19.640+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 29 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":416,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:05:19.653+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":430,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:06:00.245+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":127,"影响行数":2}
{"level":"INFO","time":"2025-07-02T18:06:00.246+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 29 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:06:00.246+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":128,"影响行数":20}
{"level":"INFO","time":"2025-07-02T18:06:00.258+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":140,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:06:22.341+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":141,"影响行数":20}
{"level":"INFO","time":"2025-07-02T18:06:22.649+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:06:22.752+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":103,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:06:27.211+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:06:27.366+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":126,"影响行数":2}
{"level":"INFO","time":"2025-07-02T18:06:27.366+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":126,"影响行数":20}
{"level":"INFO","time":"2025-07-02T18:06:27.371+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":159,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:06:27.407+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":167,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:06:32.141+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":136,"影响行数":20}
{"level":"INFO","time":"2025-07-02T18:06:32.270+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":172,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:06:32.448+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":177,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:08:05.110+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 29 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:08:05.121+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":160,"影响行数":2}
{"level":"INFO","time":"2025-07-02T18:08:05.122+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":161,"影响行数":20}
{"level":"INFO","time":"2025-07-02T18:08:05.151+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":190,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:08:34.145+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T18:08:34.146+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T18:08:34.147+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T18:08:39.350+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":114,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:39.523+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":160,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:08:39.691+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":167,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:39.741+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":48,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:39.852+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:08:40.000+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":147,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:40.149+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":147,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:40.228+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:40.373+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":145,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:08:40.492+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":119,"影响行数":3}
{"level":"INFO","time":"2025-07-02T18:08:40.542+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:40.634+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":91,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:08:40.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":202,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:40.891+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:40.974+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":82,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:08:41.086+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":112,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:41.202+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:41.366+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:08:41.507+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":141,"影响行数":3}
{"level":"INFO","time":"2025-07-02T18:08:41.559+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":52,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:41.659+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:08:41.792+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":131,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:41.854+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:41.934+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":79,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:08:42.046+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":112,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:42.100+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:42.194+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":92,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:08:42.288+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:42.335+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":47,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:42.461+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:08:42.558+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:42.559+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T18:08:42.646+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T18:08:42.647+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T18:08:42.647+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T18:08:42.648+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T18:08:51.440+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT IFNULL(MAX(sort), 0) FROM `eyc_meeting_room` WHERE group_id = 1 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":105,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:08:51.533+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','OP组','亚马逊森林会议室11',1,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,'\"\"','领导专用',1,'\"全部员工\"',0,'全部','全部','08:00:00','18:00:00',7,'07:00:00',30,240,1,0,0,10,15,0,20)","耗时":93,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:08:51.615+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 1 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":81,"影响行数":0}
{"level":"ERROR","time":"2025-07-02T18:08:51.673+0800","caller":"controller/base.go:535","msg":"新增记录失败","error":"查找新分组失败: record not found"}
{"level":"INFO","time":"2025-07-02T18:12:24.793+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":127,"影响行数":20}
{"level":"INFO","time":"2025-07-02T18:12:25.068+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":402,"影响行数":2}
{"level":"INFO","time":"2025-07-02T18:12:25.093+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":427,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:12:25.093+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 29 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":427,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:13:22.251+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":73,"影响行数":20}
{"level":"INFO","time":"2025-07-02T18:13:22.600+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:13:22.715+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":115,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:13:26.105+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":185,"影响行数":2}
{"level":"INFO","time":"2025-07-02T18:13:26.107+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":189,"影响行数":20}
{"level":"INFO","time":"2025-07-02T18:13:26.128+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":208,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:13:32.191+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":88,"影响行数":2}
{"level":"INFO","time":"2025-07-02T18:13:32.193+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":91,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:13:32.204+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":102,"影响行数":20}
{"level":"INFO","time":"2025-07-02T18:13:32.941+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":168,"影响行数":20}
{"level":"INFO","time":"2025-07-02T18:13:32.986+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:13:33.166+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":178,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:13:44.370+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T18:13:44.370+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T18:13:44.370+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T18:13:48.854+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":107,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:49.067+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":185,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:13:49.219+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":151,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:49.302+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":82,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:49.464+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:13:49.623+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:49.812+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":187,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:49.864+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":52,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:50.060+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":195,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:13:50.204+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":143,"影响行数":3}
{"level":"INFO","time":"2025-07-02T18:13:50.284+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:50.464+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":180,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:13:50.662+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":196,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:50.764+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":102,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:50.904+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":139,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:13:51.064+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:51.164+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:51.315+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":150,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:13:51.464+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":149,"影响行数":3}
{"level":"INFO","time":"2025-07-02T18:13:51.564+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:51.696+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:13:51.885+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":189,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:51.984+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:52.117+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:13:52.290+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":172,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:52.386+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:52.582+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":196,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:13:52.768+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":185,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:52.905+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":135,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:53.076+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":171,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:13:53.241+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":164,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:13:53.241+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T18:13:53.463+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T18:13:53.465+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T18:13:53.465+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T18:13:53.467+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T18:14:04.080+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = 'OP组' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":155,"影响行数":0}
{"level":"INFO","time":"2025-07-02T18:14:04.231+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_groups` (`corp_id`,`title`,`d_group_id`,`sort`,`lock`,`type`,`status`,`room_id`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','OP组',0,0,0,0,0,'')","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:14:04.431+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT IFNULL(MAX(sort), 0) FROM `eyc_meeting_room` WHERE group_id = 190 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:14:04.504+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','OP组','亚马逊森林会议室11',190,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,'\"\"','领导专用',1,'\"全部员工\"',0,'全部','全部','08:00:00','18:00:00',7,'07:00:00',30,240,1,0,0,10,15,0,20)","耗时":73,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:14:04.644+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 190 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":139,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:14:04.801+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_groups` SET `room_id`='71',`updated_at`='2025-07-02 18:14:04.645' WHERE `id` = 190","耗时":155,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:14:27.378+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":94,"影响行数":21}
{"level":"INFO","time":"2025-07-02T18:14:27.768+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 29 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":484,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:14:27.770+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":486,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:14:27.790+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":505,"影响行数":2}
{"level":"INFO","time":"2025-07-02T18:14:37.356+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":179,"影响行数":21}
{"level":"INFO","time":"2025-07-02T18:14:37.779+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":286,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:14:37.977+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":197,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:14:40.324+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":107,"影响行数":2}
{"level":"INFO","time":"2025-07-02T18:14:40.326+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":109,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:14:40.344+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":128,"影响行数":21}
{"level":"INFO","time":"2025-07-02T18:15:33.070+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT IFNULL(MAX(sort), 0) FROM `eyc_meeting_room` WHERE group_id = 161 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":150,"影响行数":-1}
{"level":"ERROR","time":"2025-07-02T18:15:33.188+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','','测试会议室',161,'北京市东城区东华门街道劳动人民文化宫派出所北京市劳动人民文化宫','天台',30,'测试门牌008,啊',1,'\"\"','1111',1,NULL,0,'','','','',0,'',0,0,1,0,0,0,0,0,0)","耗时":118,"error":"Error 1292 (22007): Incorrect datetime value: '' for column 'open_start_time' at row 1"}
{"level":"ERROR","time":"2025-07-02T18:15:33.262+0800","caller":"controller/base.go:535","msg":"新增记录失败","error":"Error 1292 (22007): Incorrect datetime value: '' for column 'open_start_time' at row 1"}
{"level":"INFO","time":"2025-07-02T18:17:05.404+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":121,"影响行数":21}
{"level":"INFO","time":"2025-07-02T18:17:05.585+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":195,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:17:05.749+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":164,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:17:09.162+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":147,"影响行数":2}
{"level":"INFO","time":"2025-07-02T18:17:09.164+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 29 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":148,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:17:09.164+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":152,"影响行数":21}
{"level":"INFO","time":"2025-07-02T18:17:09.192+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":177,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:17:17.424+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":101,"影响行数":21}
{"level":"INFO","time":"2025-07-02T18:17:17.752+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":119,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:17:17.903+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":150,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:17:33.867+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":135,"影响行数":2}
{"level":"INFO","time":"2025-07-02T18:17:33.868+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":136,"影响行数":21}
{"level":"INFO","time":"2025-07-02T18:17:33.878+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 12 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":146,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:17:33.878+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":146,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:17:43.822+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":130,"影响行数":21}
{"level":"INFO","time":"2025-07-02T18:17:44.112+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":107,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:17:44.237+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":124,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:17:46.673+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 158","耗时":217,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:17:46.873+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 158 ORDER BY sort ASC, id DESC LIMIT 10","耗时":200,"影响行数":2}
{"level":"INFO","time":"2025-07-02T18:17:48.992+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 161","耗时":188,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:17:49.180+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 161 ORDER BY sort ASC, id DESC LIMIT 10","耗时":188,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:17:52.424+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":119,"影响行数":2}
{"level":"INFO","time":"2025-07-02T18:17:52.425+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":120,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:17:52.426+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":121,"影响行数":21}
{"level":"INFO","time":"2025-07-02T18:17:52.460+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 32 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":155,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:17:59.396+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":104,"影响行数":21}
{"level":"INFO","time":"2025-07-02T18:17:59.699+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":133,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:17:59.835+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":135,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:18:04.460+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 158","耗时":73,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:18:04.529+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 158 ORDER BY sort ASC, id DESC LIMIT 10","耗时":68,"影响行数":2}
{"level":"INFO","time":"2025-07-02T18:18:06.890+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":100,"影响行数":2}
{"level":"INFO","time":"2025-07-02T18:18:06.890+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":101,"影响行数":21}
{"level":"INFO","time":"2025-07-02T18:18:06.890+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":101,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:18:06.895+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 36 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":105,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:19:12.768+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:19:12.901+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":132,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:20:00.176+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T18:20:00.177+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T18:20:00.178+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T18:20:04.562+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:04.698+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":123,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:20:04.809+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:04.871+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:04.984+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":112,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:20:05.112+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":127,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:05.227+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":114,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:05.284+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":55,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:05.434+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:20:05.586+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":152,"影响行数":3}
{"level":"INFO","time":"2025-07-02T18:20:05.677+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:05.812+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:20:05.952+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:06.017+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:06.171+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":153,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:20:06.301+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:06.399+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:06.524+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:20:06.627+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":102,"影响行数":3}
{"level":"INFO","time":"2025-07-02T18:20:06.692+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:06.808+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":115,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:20:06.919+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":111,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:06.963+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":42,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:07.049+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":85,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:20:07.162+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":112,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:07.230+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:07.351+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:20:07.481+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:07.528+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:07.671+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":142,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:20:07.811+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:07.811+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T18:20:07.906+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T18:20:07.907+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T18:20:07.907+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T18:20:07.907+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T18:20:16.677+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = 'OP组' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":138,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:20:16.778+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT IFNULL(MAX(sort), 0) FROM `eyc_meeting_room` WHERE group_id = 190 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:20:16.890+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','OP组','亚马逊森林会议室11',190,'3楼东侧','投影仪,白板',20,'设备1,设备2',1,'\"\"','领导专用',2,'\"全部员工\"',0,'全部','全部','08:00:00','18:00:00',7,'07:00:00',30,240,1,0,0,10,15,0,20)","耗时":112,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:20:16.998+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 190 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":107,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:20:17.148+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_groups` SET `room_id`='71,72',`updated_at`='2025-07-02 18:20:16.998' WHERE `id` = 190","耗时":150,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:20:28.631+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":169,"影响行数":21}
{"level":"INFO","time":"2025-07-02T18:20:28.957+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:20:29.089+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":132,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:20:31.568+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":114,"影响行数":21}
{"level":"INFO","time":"2025-07-02T18:20:31.849+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":395,"影响行数":2}
{"level":"INFO","time":"2025-07-02T18:20:31.870+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_device` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":416,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:20:55.477+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT IFNULL(MAX(sort), 0) FROM `eyc_meeting_room` WHERE group_id = 161 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":117,"影响行数":-1}
{"level":"ERROR","time":"2025-07-02T18:20:55.605+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','','测试会议室',161,'北京市东城区东华门街道太子林北京市劳动人民文化宫','11,天台',30,'并,啊',1,'\"\"','1111',1,NULL,0,'','','','',0,'',0,0,1,0,0,0,0,0,0)","耗时":126,"error":"Error 1292 (22007): Incorrect datetime value: '' for column 'open_start_time' at row 1"}
{"level":"ERROR","time":"2025-07-02T18:20:55.687+0800","caller":"controller/base.go:535","msg":"新增记录失败","error":"Error 1292 (22007): Incorrect datetime value: '' for column 'open_start_time' at row 1"}
{"level":"INFO","time":"2025-07-02T18:23:14.555+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":120,"影响行数":21}
{"level":"INFO","time":"2025-07-02T18:23:14.699+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE group_id = 0","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:23:14.855+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":156,"影响行数":9}
{"level":"INFO","time":"2025-07-02T18:26:41.484+0800","caller":"eyc3_meeting/main.go:72","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T18:26:41.484+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T18:26:41.485+0800","caller":"eyc3_meeting/main.go:58","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T18:26:45.909+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:46.074+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":137,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:26:46.237+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":162,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:46.300+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:46.442+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":141,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:26:46.582+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:46.714+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":132,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:46.762+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:46.917+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:26:47.020+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":102,"影响行数":3}
{"level":"INFO","time":"2025-07-02T18:26:47.063+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":42,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:47.182+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":119,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:26:47.339+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":156,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:47.419+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:47.625+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":206,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:26:47.712+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":86,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:47.854+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:48.009+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:26:48.150+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":140,"影响行数":3}
{"level":"INFO","time":"2025-07-02T18:26:48.232+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":81,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:48.362+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:26:48.492+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:48.592+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:48.762+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:26:48.945+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":182,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:49.034+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":86,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:49.233+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:26:49.404+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":170,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:49.506+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:49.652+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-07-02T18:26:49.816+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":164,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T18:26:49.817+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T18:26:50.004+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T18:26:50.005+0800","caller":"eyc3_meeting/main.go:62","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T18:26:50.006+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T18:26:50.007+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
