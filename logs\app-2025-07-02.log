{"level":"INFO","time":"2025-07-02T08:55:45.742+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:45.872+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":108,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:46.002+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:46.067+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:46.165+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":96,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:46.329+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:46.449+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:46.538+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":88,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:46.655+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":116,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:46.797+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":142,"影响行数":3}
{"level":"INFO","time":"2025-07-02T08:55:46.852+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:46.982+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:47.112+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:47.178+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:47.278+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:47.395+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:47.464+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:47.566+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:47.667+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":101,"影响行数":3}
{"level":"INFO","time":"2025-07-02T08:55:47.764+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:47.900+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:48.058+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:48.131+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:48.255+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:48.375+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:48.436+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:48.573+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:48.712+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:48.776+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:48.878+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-07-02T08:55:49.008+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T08:55:49.008+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T08:55:49.139+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T08:55:49.140+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T08:55:49.141+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T08:55:49.143+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T08:59:05.439+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":271,"影响行数":39}
{"level":"INFO","time":"2025-07-02T08:59:05.630+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND DATE(start_time) <= '2025-6-02' AND DATE(end_time) >= '2025-6-02'","耗时":190,"影响行数":0}
{"level":"INFO","time":"2025-07-02T09:02:14.068+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 35 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":93,"影响行数":1}
{"level":"INFO","time":"2025-07-02T09:02:14.188+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 28 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":119,"影响行数":1}
{"level":"INFO","time":"2025-07-02T09:02:14.300+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=sort - 1,`updated_at`='2025-07-02 09:02:14.189' WHERE group_id = 0 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND sort > 84 AND sort < 85","耗时":112,"影响行数":0}
{"level":"INFO","time":"2025-07-02T09:02:14.380+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=84,`updated_at`='2025-07-02 09:02:14.301' WHERE `id` = 35","耗时":80,"影响行数":1}
{"level":"INFO","time":"2025-07-02T09:39:05.542+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T09:39:05.543+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T09:39:05.543+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T10:18:41.950+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:42.317+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":339,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:42.578+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":259,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:42.649+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:43.378+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":728,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:43.562+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":183,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:43.745+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":182,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:43.863+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:44.068+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":204,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:44.243+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":175,"影响行数":3}
{"level":"INFO","time":"2025-07-02T10:18:44.425+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":181,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:44.665+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":239,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:44.912+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":246,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:45.003+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:45.168+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:45.403+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":233,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:45.612+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":208,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:45.884+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":271,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:46.044+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":159,"影响行数":3}
{"level":"INFO","time":"2025-07-02T10:18:46.138+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:46.429+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":290,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:46.659+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":230,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:46.748+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:46.987+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":238,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:47.137+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:47.292+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":153,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:47.538+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":244,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:47.784+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":245,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:47.844+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:48.117+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":272,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:18:48.394+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":275,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:18:48.394+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T10:18:48.563+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T10:18:48.564+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T10:18:48.564+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T10:18:48.565+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T10:20:00.707+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 35 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":327,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:20:00.951+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=sort + 1,`updated_at`='2025-07-02 10:20:00.708' WHERE sort >= 1 AND sort < 84 AND group_id = 0 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND id != 35","耗时":243,"影响行数":12}
{"level":"INFO","time":"2025-07-02T10:20:01.220+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=1,`updated_at`='2025-07-02 10:20:00.951' WHERE id = 35","耗时":269,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:22:27.736+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '你好aqaa'","耗时":268,"影响行数":1}
{"level":"ERROR","time":"2025-07-02T10:22:27.736+0800","caller":"controller/base.go:557","msg":"新增记录失败","error":"分组名称已存在"}
{"level":"INFO","time":"2025-07-02T10:22:52.983+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE `eyc_meeting_groups`.`id` = 123 ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":238,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:22:53.207+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_groups` SET `corp_id`='',`title`='你好a',`d_group_id`=0,`created_at`='2025-05-26 11:26:05',`updated_at`='2025-07-02 10:22:52.984',`sort`=0,`lock`=0,`type`=0,`status`=0,`room_id`='',`meet_room_name`='黄河会议室' WHERE `id` = 123","耗时":223,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:24:04.047+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE id = 35 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":958,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:24:04.217+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=sort - 1,`updated_at`='2025-07-02 10:24:04.048' WHERE sort > 1 AND sort <= 2 AND group_id = 0 AND corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND id != 35","耗时":169,"影响行数":6}
{"level":"INFO","time":"2025-07-02T10:24:04.360+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"UPDATE `eyc_meeting_room` SET `sort`=2,`updated_at`='2025-07-02 10:24:04.218' WHERE id = 35","耗时":142,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:31:21.076+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0","耗时":370,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:31:21.629+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' AND group_id = 0 ORDER BY sort ASC, id DESC LIMIT 10","耗时":552,"影响行数":10}
{"level":"INFO","time":"2025-07-02T10:54:12.069+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T10:54:12.071+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T10:54:12.071+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T10:54:19.558+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:19.806+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":235,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:20.046+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":238,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:20.181+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":134,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:20.522+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":340,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:20.720+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":197,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:21.061+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":340,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:21.160+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:21.476+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":315,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:21.726+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":249,"影响行数":3}
{"level":"INFO","time":"2025-07-02T10:54:21.948+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":221,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:22.440+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":491,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:22.870+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":429,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:23.003+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":131,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:23.255+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":251,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:23.456+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":200,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:23.597+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:23.791+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":193,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:24.058+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":266,"影响行数":3}
{"level":"INFO","time":"2025-07-02T10:54:24.146+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:24.478+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":331,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:24.862+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":382,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:25.038+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":175,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:25.340+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":302,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:25.595+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":254,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:25.762+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":165,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:26.050+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":288,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:26.270+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":219,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:26.362+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:26.553+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":191,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:26.819+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":264,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T10:54:26.819+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T10:54:27.038+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T10:54:27.040+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T10:54:27.040+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T10:54:27.042+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T10:54:51.478+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":300,"影响行数":2}
{"level":"INFO","time":"2025-07-02T10:54:51.927+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '郑州' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":322,"影响行数":0}
{"level":"INFO","time":"2025-07-02T10:54:52.156+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_groups` (`corp_id`,`title`,`d_group_id`,`sort`,`lock`,`type`,`status`,`room_id`,`meet_room_name`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','郑州',0,0,0,0,0,'','')","耗时":228,"影响行数":1}
{"level":"INFO","time":"2025-07-02T10:54:52.374+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`meet_room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','','黄河会议室',189,'中国河南省郑州市','',10,'',1,NULL,'这是一个测试备注',0,'{\"type\":\"all\"}',0,'','',0,'',0,0,1,0,0,0,0,0,0)","耗时":217,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:19.774+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T11:03:19.775+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T11:03:19.775+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T11:03:24.822+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:24.991+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":152,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:25.306+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":314,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:25.410+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":103,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:25.573+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:25.688+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":114,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:25.893+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":205,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:25.991+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:26.189+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":197,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:26.374+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":185,"影响行数":3}
{"level":"INFO","time":"2025-07-02T11:03:26.481+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:26.672+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":190,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:26.880+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":208,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:27.031+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":150,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:27.231+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":198,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:27.376+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":145,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:27.455+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:27.611+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":155,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:27.771+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":159,"影响行数":3}
{"level":"INFO","time":"2025-07-02T11:03:27.926+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":155,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:28.063+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:28.227+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:28.349+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":121,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:28.490+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:28.632+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:28.715+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:28.878+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:29.047+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":168,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:29.153+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:29.323+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:29.475+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":152,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:03:29.476+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T11:03:29.627+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T11:03:29.628+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T11:03:29.628+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T11:03:29.630+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T11:03:38.924+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":153,"影响行数":2}
{"level":"INFO","time":"2025-07-02T11:03:39.238+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '郑州' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":221,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:03:39.382+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`meet_room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','郑州','黄河会议室',189,'中国河南省郑州市','',10,'',1,NULL,'这是一个测试备注',0,'{\"type\":\"all\"}',0,'','',0,'',0,0,1,0,0,0,0,0,0)","耗时":144,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:19.562+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T11:32:19.564+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T11:32:19.565+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T11:32:25.672+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":51,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:25.944+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":254,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:26.262+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":318,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:26.407+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":144,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:26.563+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":154,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:26.859+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":296,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:27.084+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":223,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:27.274+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":190,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:27.638+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":362,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:27.983+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":344,"影响行数":3}
{"level":"INFO","time":"2025-07-02T11:32:28.186+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":202,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:28.468+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":282,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:28.780+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":311,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:28.966+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":185,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:29.283+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":316,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:29.670+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":386,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:29.820+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:30.009+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":188,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:30.321+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":311,"影响行数":3}
{"level":"INFO","time":"2025-07-02T11:32:30.467+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":146,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:30.789+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":320,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:31.049+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":259,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:31.191+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:31.409+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":218,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:31.770+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":360,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:31.956+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":185,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:32.289+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":332,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:32.454+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":164,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:32.528+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:32.663+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":135,"影响行数":1}
{"level":"INFO","time":"2025-07-02T11:32:32.891+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":227,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T11:32:32.892+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T11:32:33.200+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T11:32:33.201+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T11:32:33.201+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T11:32:33.202+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T11:32:42.499+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":497,"影响行数":41}
{"level":"INFO","time":"2025-07-02T11:38:12.572+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":292,"影响行数":41}
{"level":"INFO","time":"2025-07-02T11:38:15.190+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":302,"影响行数":41}
{"level":"INFO","time":"2025-07-02T13:46:29.465+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T13:46:29.467+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T13:46:29.468+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T13:46:45.302+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":82,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:45.513+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":183,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:45.690+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":175,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:45.794+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":103,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:46.022+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":227,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:46.263+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":241,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:46.503+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":239,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:46.590+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:46.753+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":163,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:46.910+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":156,"影响行数":3}
{"level":"INFO","time":"2025-07-02T13:46:47.000+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:47.209+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":208,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:47.454+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":245,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:47.562+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:47.840+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":278,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:48.120+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":279,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:48.240+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":119,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:48.455+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":214,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:48.819+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":363,"影响行数":3}
{"level":"INFO","time":"2025-07-02T13:46:48.964+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":145,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:49.145+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":180,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:49.344+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:49.424+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:49.585+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:49.774+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":188,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:49.864+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:50.055+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":191,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:50.265+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":209,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:50.407+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:50.585+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":178,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:46:50.800+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":214,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:46:50.800+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T13:46:50.969+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T13:46:50.970+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T13:46:50.971+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T13:46:50.972+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T13:46:56.500+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":412,"影响行数":41}
{"level":"INFO","time":"2025-07-02T13:47:15.900+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":593,"影响行数":41}
{"level":"INFO","time":"2025-07-02T13:49:54.336+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-07-02T13:49:54.342+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-07-02T13:49:54.343+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-07-02T13:50:04.063+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":172,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:04.264+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":182,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:04.423+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:04.529+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":105,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:04.689+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:04.860+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":171,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:05.066+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":205,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:05.237+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":170,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:05.417+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:05.668+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":251,"影响行数":3}
{"level":"INFO","time":"2025-07-02T13:50:05.805+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":136,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:05.994+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":188,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:06.168+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":173,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:06.235+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:06.667+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":431,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:07.023+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":356,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:07.136+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":112,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:07.385+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":248,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:07.633+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":247,"影响行数":3}
{"level":"INFO","time":"2025-07-02T13:50:07.723+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:07.927+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":204,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:08.085+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":157,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:08.191+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:08.413+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":222,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:08.566+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":151,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:08.616+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":48,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:08.776+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:08.970+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":193,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:09.035+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:09.316+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":280,"影响行数":1}
{"level":"INFO","time":"2025-07-02T13:50:09.823+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":507,"影响行数":-1}
{"level":"INFO","time":"2025-07-02T13:50:09.823+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-07-02T13:50:10.506+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-07-02T13:50:10.507+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-07-02T13:50:10.507+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-07-02T13:50:10.509+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-07-02T13:50:17.057+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":317,"影响行数":41}
{"level":"INFO","time":"2025-07-02T14:13:33.316+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE `eyc_meeting_room`.`id` = 1 ORDER BY `eyc_meeting_room`.`id` LIMIT 1","耗时":273,"影响行数":1}
