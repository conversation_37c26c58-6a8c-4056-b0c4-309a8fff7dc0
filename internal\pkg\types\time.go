package types

import (
	"database/sql/driver"
	"fmt"
	"time"
)

// JSONTime 自定义时间类型，用于JSON序列化时使用指定格式
type JSONTime time.Time

// 时间格式常量
const (
	TimeFormat = "2006-01-02 15:04:05"
	DateFormat = "2006-01-02"
)

// MarshalJSON 实现json.Marshaler接口，自定义时间序列化格式
func (t JSONTime) MarshalJSON() ([]byte, error) {
	stamp := fmt.Sprintf("\"%s\"", time.Time(t).Format(TimeFormat))
	return []byte(stamp), nil
}

// MarshalJSONAsDate 将时间格式化为日期格式
func (t JSONTime) MarshalJSONAsDate() ([]byte, error) {
	stamp := fmt.Sprintf("\"%s\"", time.Time(t).Format(DateFormat))
	return []byte(stamp), nil
}

// FormatDate 返回日期格式的字符串(YYYY-MM-DD)
func (t JSONTime) FormatDate() string {
	return time.Time(t).Format(DateFormat)
}

// FormatDateTime 返回日期时间格式的字符串(YYYY-MM-DD HH:MM:SS)
func (t JSONTime) FormatDateTime() string {
	return time.Time(t).Format(TimeFormat)
}

// UnmarshalJSON 实现json.Unmarshaler接口，自定义反序列化
func (t *JSONTime) UnmarshalJSON(data []byte) error {
	if string(data) == "null" {
		return nil
	}

	// 去除引号
	timeStr := string(data)
	if len(timeStr) >= 2 {
		timeStr = timeStr[1 : len(timeStr)-1]
	}

	// 尝试按标准日期时间格式解析
	parsedTime, err := time.Parse(TimeFormat, timeStr)
	if err != nil {
		// 尝试按日期格式解析
		parsedTime, err = time.Parse(DateFormat, timeStr)
		if err != nil {
			// 尝试按照ISO格式解析
			parsedTime, err = time.Parse(time.RFC3339, timeStr)
			if err != nil {
				return err
			}
		}
	}

	*t = JSONTime(parsedTime)
	return nil
}

// Value 实现 driver.Valuer 接口，用于数据库存储
func (t JSONTime) Value() (driver.Value, error) {
	if time.Time(t).IsZero() {
		return nil, nil
	}
	return time.Time(t), nil
}

// Scan 实现 sql.Scanner 接口，用于从数据库中读取
func (t *JSONTime) Scan(value interface{}) error {
	if value == nil {
		*t = JSONTime(time.Time{})
		return nil
	}

	switch v := value.(type) {
	case time.Time:
		*t = JSONTime(v)
	case []byte:
		// 尝试用日期时间格式解析
		parsedTime, err := time.Parse(TimeFormat, string(v))
		if err != nil {
			// 尝试用日期格式解析
			parsedTime, err = time.Parse(DateFormat, string(v))
			if err != nil {
				return err
			}
		}
		*t = JSONTime(parsedTime)
	case string:
		// 尝试用日期时间格式解析
		parsedTime, err := time.Parse(TimeFormat, v)
		if err != nil {
			// 尝试用日期格式解析
			parsedTime, err = time.Parse(DateFormat, v)
			if err != nil {
				return err
			}
		}
		*t = JSONTime(parsedTime)
	default:
		return fmt.Errorf("无法将 %T 转换为 JSONTime", value)
	}

	return nil
}

// String 返回格式化的时间字符串
func (t JSONTime) String() string {
	return time.Time(t).Format(TimeFormat)
}

// DateString 返回格式化的日期字符串
func (t JSONTime) DateString() string {
	return time.Time(t).Format(DateFormat)
}

// ParseDate 从日期字符串解析时间
func ParseDate(dateStr string) (JSONTime, error) {
	t, err := time.Parse(DateFormat, dateStr)
	if err != nil {
		return JSONTime{}, err
	}
	return JSONTime(t), nil
}

// ParseDateTime 从日期时间字符串解析时间
func ParseDateTime(dateTimeStr string) (JSONTime, error) {
	t, err := time.Parse(TimeFormat, dateTimeStr)
	if err != nil {
		return JSONTime{}, err
	}
	return JSONTime(t), nil
}

// Date 创建仅包含日期部分的JSONTime
func Date(year int, month time.Month, day int) JSONTime {
	return JSONTime(time.Date(year, month, day, 0, 0, 0, 0, time.Local))
}
