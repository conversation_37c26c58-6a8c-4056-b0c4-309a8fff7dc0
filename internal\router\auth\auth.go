package auth

import (
	"eyc3_meeting/internal/controller"

	"github.com/cloudwego/hertz/pkg/app/server"
)

// Register 注册认证相关路由
func Register(r *server.Hertz) {
	// 认证相关路由组
	authGroup := r.Group("/sys")
	{
		// 登录接口 - 支持所有HTTP方法
		authGroup.Any("/get_login", controller.Login)

		// 注册接口 - 支持所有HTTP方法
		authGroup.Any("/register", controller.Register)

		// 刷新Token - 支持所有HTTP方法
		authGroup.Any("/refresh", controller.RefreshToken)
	}
}
