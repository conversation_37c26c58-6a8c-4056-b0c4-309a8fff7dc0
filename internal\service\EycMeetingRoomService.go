package service

import (
	"bytes"
	"encoding/json"
	"errors"
	"eyc3_meeting/internal/model"
	"eyc3_meeting/internal/pkg/excel"
	"fmt"
	"io"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

// EycMeetingRoomService 会议室服务
// 负责会议室的增删改查及相关业务逻辑
type EycMeetingRoomService struct {
	BaseServiceImpl[model.EycMeetingRoom]
}

// GetInfoService 获取单个会议室详情
// 直接调用基础服务即可，因为会议室详情没有额外的关联查询逻辑
func (s *EycMeetingRoomService) GetInfoService(ac *app.RequestContext, id uint) (model.EycMeetingRoom, bool, error) {
	return s.BaseServiceImpl.GetInfoService(ac, id)
}

// DeleteService 删除会议室
func (s *EycMeetingRoomService) DeleteService(ac *app.RequestContext, id uint) error {
	// 查询会议室
	var room model.EycMeetingRoom
	if err := model.DB.First(&room, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("会议室不存在")
		}
		return err
	}

	// 开启事务
	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除会议室
	if err := tx.Delete(&model.EycMeetingRoom{}, id).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 如果是未分组会议室，更新其他未分组会议室的排序（sort值大于被删除会议室的都-1）
	if room.GroupID == 0 {
		if err := tx.Model(&model.EycMeetingRoom{}).
			Where("group_id = 0 AND corpid = ? AND sort > ?", room.Corpid, room.Sort).
			Update("sort", gorm.Expr("sort - 1")).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 从旧分组的room_ids字段中移除
	if room.GroupID != 0 {
		if err := s.removeRoomFromGroupInTx(tx, room.GroupID, int(id)); err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// GetListService 获取会议室列表
func (s *EycMeetingRoomService) GetListService(ac *app.RequestContext, page int, perPage int, filters map[string]string, order string) (model.PageResult[model.EycMeetingRoom], error) {
	var result model.PageResult[model.EycMeetingRoom]
	var EycMeetingRooms []model.EycMeetingRoom
	db := model.DB.Model(&model.EycMeetingRoom{})

	// 从 context 中获取 corpid
	corpid, _ := ac.Get("corpid")
	if corpid != nil {
		db = db.Where("corpid = ?", corpid)
	}

	// 应用筛选条件
	for key, value := range filters {
		switch key {
		case "group_id":
			if value != "" {
				groupID, err := strconv.Atoi(value)
				if err != nil {
					return result, errors.New("group_id 必须是有效的整数")
				}
				// 当 group_id 不为 -1 时，才应用此筛选条件
				if groupID != -1 {
					db = db.Where("group_id = ?", groupID)
				}
			}
		case "room_name":
			if value != "" {
				db = db.Where("room_name LIKE ?", "%"+value+"%")
			}
		case "keyword":
			if value != "" {
				db = db.Where("room_name LIKE ?", "%"+value+"%")
			}
		}
	}

	// 获取总数
	err := db.Model(&model.EycMeetingRoom{}).Count(&result.Total).Error
	if err != nil {
		return result, err
	}

	result.Items = []model.EycMeetingRoom{} // 初始化，避免返回null

	// 排序
	if order == "" {
		order = "sort ASC, id DESC" // 默认排序
	}
	db = db.Order(order)

	// 分页
	if page > 0 && perPage > 0 {
		db = db.Offset((page - 1) * perPage).Limit(perPage)
	}

	if result.Total > 0 {
		if err := db.Find(&EycMeetingRooms).Error; err != nil {
			return result, err
		}
		result.Items = EycMeetingRooms
	}

	return result, nil
}

// GetAllService 查询全部会议室
func (s *EycMeetingRoomService) GetAllService(ac *app.RequestContext, fields map[string]string, orderBy string) ([]model.EycMeetingRoom, error) {
	var rooms []model.EycMeetingRoom
	db := model.DB.Model(&model.EycMeetingRoom{})

	// 从 context 获取 corp_id
	corpID, _ := ac.Get("corp_id")
	if corpIDStr, ok := corpID.(string); ok && corpIDStr != "" {
		db = db.Where("corpid = ?", corpIDStr)
	}

	// 处理其他查询条件
	if keyword, ok := fields["keyword"]; ok && keyword != "" {
		db = db.Where("room_name LIKE ?", "%"+keyword+"%")
	}
	if groupIDStr, ok := fields["group_id"]; ok && groupIDStr != "" {
		groupID, err := strconv.Atoi(groupIDStr)
		if err == nil {
			db = db.Where("group_id = ?", groupID)
		}
	} else if _, ok := fields["group_id"]; ok {
		db = db.Where("group_id = 0")
	}

	// 排序
	if orderBy == "" {
		orderBy = "sort ASC, id DESC"
	}
	db = db.Order(orderBy)

	if err := db.Find(&rooms).Error; err != nil {
		return nil, err
	}
	return rooms, nil
}

// AddService 新增会议室
func (s *EycMeetingRoomService) AddService(ac *app.RequestContext, data interface{}) (uint, error) {
	// 将接口类型转换为具体的会议室模型指针
	room, ok := data.(*model.EycMeetingRoom)
	if !ok {
		return 0, errors.New("无效的数据类型，期望为 *model.EycMeetingRoom")
	}

	// 从JWT上下文中获取企业ID (这一步是冗余的，因为BaseController已经设置了，但保留以防服务层被单独调用)
	if room.Corpid == "" {
		corpIDValue, exists := ac.Get("corp_id")
		if !exists {
			return 0, errors.New("无法获取企业ID，请检查登录状态")
		}
		corpID, ok := corpIDValue.(string)
		if !ok || corpID == "" {
			return 0, errors.New("无效的企业ID")
		}
		room.Corpid = corpID
	}

	// 后端验证
	if room.RoomName == "" {
		return 0, errors.New("会议室名称不能为空")
	}
	if room.Capacity <= 0 {
		return 0, errors.New("容纳人数必须大于0")
	}

	// 开启事务
	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 优先根据 Title (分组名称) 查找或创建分组，使接口更健壮
	if room.Title != "" {
		groupID, err := findOrCreateFlattenedGroupInTx(tx, room.Corpid, room.Title)
		if err != nil {
			tx.Rollback()
			return 0, fmt.Errorf("处理分组 '%s' 失败: %w", room.Title, err)
		}
		room.GroupID = groupID
	}

	// 处理排序逻辑
	if room.GroupID == 0 {
		// 未分组会议室，sort值设为最大+1
		var maxSort int
		tx.Model(&model.EycMeetingRoom{}).Where("group_id = 0 AND corpid = ?", room.Corpid).Select("IFNULL(MAX(sort), 0)").Row().Scan(&maxSort)
		room.Sort = maxSort + 1
	} else {
		// 已分组会议室，sort值设为组内最大+1
		var maxSort int
		tx.Model(&model.EycMeetingRoom{}).Where("group_id = ? AND corpid = ?", room.GroupID, room.Corpid).Select("IFNULL(MAX(sort), 0)").Row().Scan(&maxSort)
		room.Sort = maxSort + 1
	}

	// 创建会议室
	if err := tx.Create(room).Error; err != nil {
		tx.Rollback()
		return 0, err
	}

	// 如果有关联的分组，更新分组的 room_ids 字段
	if room.GroupID != 0 {
		if err := s.appendRoomToGroupInTx(tx, room.GroupID, int(room.ID)); err != nil {
			tx.Rollback()
			return 0, err
		}
	}

	if err := tx.Commit().Error; err != nil {
		return 0, err
	}

	return uint(room.ID), nil
}

// ModifyService 修改会议室
func (s *EycMeetingRoomService) ModifyService(ac *app.RequestContext, id uint, data interface{}) error {
	// 接收来自 BaseController 的、已经绑定好的数据
	updateData, ok := data.(*model.EycMeetingRoom)
	if !ok {
		return errors.New("无效的数据类型，期望为 *model.EycMeetingRoom")
	}

	// 检查会议室是否存在
	var existingRoom model.EycMeetingRoom
	if err := model.DB.First(&existingRoom, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("会议室不存在")
		}
		return err
	}

	originalGroupID := existingRoom.GroupID
	newGroupID := updateData.GroupID // 从更新数据中获取新的group_id

	// 验证并更新字段
	if updateData.RoomName == "" {
		return errors.New("会议室名称不能为空")
	}
	if updateData.Capacity <= 0 {
		return errors.New("容纳人数必须大于0")
	}

	existingRoom.RoomName = updateData.RoomName
	existingRoom.Capacity = updateData.Capacity
	existingRoom.Location = updateData.Location
	existingRoom.Facility = updateData.Facility
	existingRoom.Device = updateData.Device
	existingRoom.IsOpenBooking = updateData.IsOpenBooking
	existingRoom.Image = updateData.Image
	existingRoom.Remark = updateData.Remark
	existingRoom.Sort = updateData.Sort

	// 检查会议室名称是否已存在(排除自己)
	if exists, err := existingRoom.IsRoomNameExists(existingRoom.Corpid, existingRoom.RoomName, existingRoom.ID); err != nil {
		return err
	} else if exists {
		return errors.New("会议室名称已存在")
	}

	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 如果分组ID发生变化
	if originalGroupID != newGroupID {
		// 检查是否从某个分组移除（group_id由非0变为0）
		if newGroupID == 0 {
			var maxSort int
			tx.Model(&model.EycMeetingRoom{}).
				Where("group_id = 0 AND corpid = ?", existingRoom.Corpid).
				Select("COALESCE(MAX(sort), 0)").
				Scan(&maxSort)
			existingRoom.Sort = maxSort + 1
		} else { // 检查新分组是否存在
			var group model.EycMeetingGroup
			if err := tx.First(&group, newGroupID).Error; err != nil {
				tx.Rollback()
				return errors.New("目标分组不存在")
			}
		}
		existingRoom.GroupID = newGroupID // 更新GroupID
	}

	// 更新会议室基础信息 - 保存整个 struct
	existingRoom.UpdatedAt = time.Now()
	if err := tx.Save(&existingRoom).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 如果分组ID发生变化，需要同时维护旧分组和新分组的 room_ids 字段
	if originalGroupID != newGroupID {
		// 从旧分组移除
		if originalGroupID != 0 {
			if err := s.removeRoomFromGroupInTx(tx, originalGroupID, int(id)); err != nil {
				tx.Rollback()
				return err
			}
		}
		// 添加到新分组
		if newGroupID != 0 {
			if err := s.appendRoomToGroupInTx(tx, newGroupID, int(id)); err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	return tx.Commit().Error
}

// removeRoomFromGroupInTx 从分组的 RoomIds 字段中移除一个会议室ID
func (s *EycMeetingRoomService) removeRoomFromGroupInTx(tx *gorm.DB, groupID int, roomID int) error {
	var group model.EycMeetingGroup
	if err := tx.First(&group, groupID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil // 分组不存在，无需处理
		}
		return fmt.Errorf("查找旧分组失败: %w", err)
	}

	if group.RoomIds == "" {
		return nil
	}

	idStrs := strings.Split(group.RoomIds, ",")
	var newIDs []string
	roomIDStr := strconv.Itoa(roomID)
	for _, idStr := range idStrs {
		idStr = strings.TrimSpace(idStr)
		// 过滤掉空字符串、"0"以及要删除的ID
		if idStr != "" && idStr != "0" && idStr != roomIDStr {
			newIDs = append(newIDs, idStr)
		}
	}

	return tx.Model(&group).Update("room_id", strings.Join(newIDs, ",")).Error
}

// appendRoomToGroupInTx 将一个会议室ID追加到分组的 RoomIds 字段
func (s *EycMeetingRoomService) appendRoomToGroupInTx(tx *gorm.DB, groupID int, roomID int) error {
	var group model.EycMeetingGroup
	if err := tx.First(&group, groupID).Error; err != nil {
		return fmt.Errorf("查找新分组失败: %w", err)
	}

	var initialIDs []string
	if group.RoomIds != "" {
		initialIDs = strings.Split(group.RoomIds, ",")
	}

	// 过滤掉无效的ID（空字符串和"0"）
	var cleanIDs []string
	for _, idStr := range initialIDs {
		trimmedID := strings.TrimSpace(idStr)
		if trimmedID != "" && trimmedID != "0" {
			cleanIDs = append(cleanIDs, trimmedID)
		}
	}

	// 避免重复添加
	roomIDStr := strconv.Itoa(roomID)
	found := false
	for _, idStr := range cleanIDs {
		if idStr == roomIDStr {
			found = true
			break
		}
	}
	if !found {
		cleanIDs = append(cleanIDs, roomIDStr)
	}

	return tx.Model(&group).Update("room_id", strings.Join(cleanIDs, ",")).Error
}

// UpdateEycMeetingRoomAdvancedSettings 更新会议室高级设置
func (s *EycMeetingRoomService) UpdateEycMeetingRoomAdvancedSettings(ac *app.RequestContext, req *model.EycMeetingRoomAdvancedSettings) error {
	// 验证会议室是否存在
	var existingRoom model.EycMeetingRoom
	if err := model.DB.First(&existingRoom, req.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("会议室不存在")
		}
		return err
	}

	// 权限检查：确保操作的是同一个企业下的资源
	corpID, _ := ac.Get("corp_id")
	if existingRoom.Corpid != corpID.(string) {
		return errors.New("无权操作该资源")
	}

	// 使用 map 来动态构建更新数据，只更新非零值/非空字段
	updateData := make(map[string]interface{})

	// 定义允许更新的字段白名单
	allowedFields := map[string]bool{
		"VisibleScope": true, "NeedApproval": true, "ApprovalScope": true,
		"ApprovalType": true, "OpenStartTime": true, "OpenEndTime": true,
		"EarliestBookCycle": true, "EarliestBookTime": true, "MinBookDuration": true,
		"MaxBookDuration": true, "AllowRecurring": true, "AllowOccupy": true,
		"KeepSignin": true, "ShowSigninAdvanceTime": true, "ReleaseOvertimeTime": true,
		"AllowDelay": true, "DelayTime": true,
	}

	// 将 req 转换为 map
	reqMap := make(map[string]interface{})
	reqData, _ := json.Marshal(req)
	json.Unmarshal(reqData, &reqMap)

	for key, value := range reqMap {
		// Go的JSON库会将字段名转换为大驼峰，我们需要首字母小写以匹配map key
		// 但这里的字段名已经是正确的，因为 model tag 是小写开头
		if _, ok := allowedFields[key]; ok {
			updateData[key] = value
		}
	}

	// ID 和 Corpid 不应被更新
	delete(updateData, "ID")
	delete(updateData, "Corpid")

	if len(updateData) == 0 {
		return nil
	}

	updateData["updated_at"] = time.Now()

	// 只更新白名单中的字段
	if err := model.DB.Model(&existingRoom).Updates(updateData).Error; err != nil {
		return fmt.Errorf("更新高级设置失败: %w", err)
	}

	return nil
}

// RoomSortRequest 会议室排序请求结构
type RoomSortRequest struct {
	RoomIDs []int `json:"room_ids"` // 排序后的会议室ID列表
}

// UpdateEycMeetingRoomSort 更新未分组会议室的排序
func (s *EycMeetingRoomService) UpdateEycMeetingRoomSort(ac *app.RequestContext, req *RoomSortRequest) error {
	if len(req.RoomIDs) == 0 {
		return errors.New("会议室ID列表不能为空")
	}

	// 从 context 获取 corp_id
	corpID, _ := ac.Get("corp_id")
	corpIDStr, ok := corpID.(string)
	if !ok || corpIDStr == "" {
		return errors.New("无效的企业ID")
	}

	// 开启事务
	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 强制只处理指定企业下，未分组的会议室
	for index, roomID := range req.RoomIDs {
		sortValue := index + 1 // 从1开始排序
		result := tx.Model(&model.EycMeetingRoom{}).
			Where("id = ? AND group_id = 0 AND corpid = ?", roomID, corpIDStr).
			Update("sort", sortValue)
		if result.Error != nil {
			tx.Rollback()
			return result.Error
		}
		if result.RowsAffected == 0 {
			tx.Rollback()
			return fmt.Errorf("会议室ID %d 不存在、不属于该企业或已分组", roomID)
		}
	}

	// 提交事务
	return tx.Commit().Error
}

// MoveMeetingRoomToSort 移动未分组会议室到指定排序
func (s *EycMeetingRoomService) MoveMeetingRoomToSort(ac *app.RequestContext, id int, targetSort int) error {
	// 确保targetSort不小于1（排序从1开始）
	if targetSort < 1 {
		targetSort = 1
	}

	// 开启事务
	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询当前会议室
	var currentRoom model.EycMeetingRoom
	if err := tx.Where("id = ?", id).First(&currentRoom).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("会议室不存在")
		}
		return err
	}

	// 只允许对未分组的会议室进行排序
	if currentRoom.GroupID != 0 {
		tx.Rollback()
		return errors.New("只能对未分组的会议室进行排序")
	}

	// 如果当前位置和目标位置相同，无需调整
	if currentRoom.Sort == targetSort {
		tx.Rollback() // 实际上没有做任何事，直接回滚或提交空事务都可以
		return nil
	}

	// 位置交换逻辑
	if currentRoom.Sort > targetSort {
		// 向前移动: [targetSort, currentRoom.Sort) 区间的元素 +1
		if err := tx.Model(&model.EycMeetingRoom{}).
			Where("sort >= ? AND sort < ? AND group_id = 0 AND corpid = ? AND id != ?", targetSort, currentRoom.Sort, currentRoom.Corpid, id).
			Update("sort", gorm.Expr("sort + 1")).Error; err != nil {
			tx.Rollback()
			return err
		}
	} else {
		// 向后移动: (currentRoom.Sort, targetSort] 区间的元素 -1
		if err := tx.Model(&model.EycMeetingRoom{}).
			Where("sort > ? AND sort <= ? AND group_id = 0 AND corpid = ? AND id != ?", currentRoom.Sort, targetSort, currentRoom.Corpid, id).
			Update("sort", gorm.Expr("sort - 1")).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 设置当前会议室到目标位置
	if err := tx.Model(&model.EycMeetingRoom{}).
		Where("id = ?", id).
		Update("sort", targetSort).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// ExportAllMeetingRooms 导出所有会议室数据为Excel文件
func (s *EycMeetingRoomService) ExportAllMeetingRooms(ac *app.RequestContext) (*bytes.Buffer, error) {
	// 1. 获取所有会议室数据
	rooms, err := s.GetAllService(ac, nil, "sort ASC, id DESC")
	if err != nil {
		return nil, fmt.Errorf("获取会议室列表失败: %w", err)
	}

	// 2. 将数据转换为map切片，就像参考模板中做的那样
	roomMaps := make([]map[string]interface{}, 0, len(rooms))
	for _, room := range rooms {
		isOpenBooking := "关闭"
		if room.IsOpenBooking == 1 {
			isOpenBooking = "开放"
		}
		visibleScope := "部分成员可见"
		if string(room.VisibleScope) == `{"type":"all"}` {
			visibleScope = "全员可见"
		}

		roomMap := map[string]interface{}{
			"room_name":       room.RoomName,
			"group_name":      room.Title, // 分组名称存在Title字段中
			"location":        room.Location,
			"capacity":        room.Capacity,
			"facility":        room.Facility,
			"remark":          room.Remark,
			"is_open_booking": isOpenBooking,
			"visible_scope":   visibleScope,
		}
		roomMaps = append(roomMaps, roomMap)
	}

	// 3. 使用统一的工厂函数创建服务并导出
	service := NewMeetingRoomExcelService()
	return service.ExportToBuffer(roomMaps)
}

// NewMeetingRoomExcelService 创建一个用于会议室导入和导出的统一Excel服务
func NewMeetingRoomExcelService() *excel.GenericExcelService {
	headers := []excel.ExcelHeader{
		{Title: "会议室名称", Field: "room_name", Width: 30},
		{Title: "分组名称", Field: "group_name", Width: 20},
		{Title: "地址", Field: "location", Width: 40},
		{Title: "容纳人数", Field: "capacity", Width: 10, Type: "number"},
		{Title: "会议室设施", Field: "facility", Width: 40},
		{Title: "备注", Field: "remark", Width: 30},
		{Title: "会议室开放预定", Field: "is_open_booking", Width: 20},
		{Title: "会议室可见范围", Field: "visible_scope", Width: 25},
	}
	// 创建通用服务
	service := excel.NewGenericExcelService(headers, "会议室信息")
	// 设置导入时使用的验证函数
	service.SetValidator(validateMeetingRoomImport)
	return service
}

// validateMeetingRoomImport 验证会议室导入数据
func validateMeetingRoomImport(data map[string]interface{}) error {
	roomName := data["room_name"].(string)
	if roomName == "" {
		return errors.New("会议室名称不能为空")
	}
	if len(roomName) > 30 {
		return fmt.Errorf("会议室名称 '%s' 不能超过30个字符", roomName)
	}

	if groupName, ok := data["group_name"].(string); ok && len(groupName) > 20 {
		return errors.New("分组名称不能超过20个字符")
	}

	if location, ok := data["location"].(string); ok && len(location) > 50 {
		return errors.New("地址不能超过50个字符")
	}

	if capacityStr, ok := data["capacity"].(string); ok && capacityStr != "" {
		capacity, err := strconv.Atoi(capacityStr)
		if err != nil {
			return errors.New("容纳人数必须是数字")
		}
		if capacity > 10000 {
			return errors.New("容纳人数不能超过10000")
		}
		data["capacity"] = capacity // 将字符串转换为int
	}

	if remark, ok := data["remark"].(string); ok && len(remark) > 100 {
		return errors.New("备注不能超过100个字符")
	}

	isOpenStr := data["is_open_booking"].(string)
	isOpenBooking := 0 // 默认关闭
	if isOpenStr == "开放" {
		isOpenBooking = 1
	} else if isOpenStr != "" && isOpenStr != "关闭" {
		return errors.New("会议室开放预定只能填写'开放'或'关闭'")
	}
	data["is_open_booking"] = isOpenBooking

	visibleScopeStr := data["visible_scope"].(string)
	if visibleScopeStr != "" && visibleScopeStr != "全员可见" && visibleScopeStr != "部分成员可见" {
		return errors.New("会议室可见范围只能填写'全员可见'或'部分成员可见'")
	}

	return nil
}

func (s *EycMeetingRoomService) BatchAddFromExcel(ac *app.RequestContext, file io.Reader) error {
	corpID, _ := ac.Get("corp_id")
	corpIDStr, _ := corpID.(string)

	excelService := NewMeetingRoomExcelService()
	roomsData, validationErrors, err := excelService.Import(file)
	if err != nil {
		return fmt.Errorf("导入失败: %w", err)
	}
	if len(validationErrors) > 0 {
		return errors.New(strings.Join(validationErrors, "; "))
	}

	// 预加载所有设施
	facilityCache, err := model.GetAllFacilities(corpIDStr)
	if err != nil {
		return fmt.Errorf("加载设施列表失败: %w", err)
	}

	var roomsToCreate []model.EycMeetingRoom

	// 业务层面的二次校验和数据准备
	for i, roomData := range roomsData {
		rowNum := i + 2 // Excel行号从2开始

		// 校验设施
		var validFacilities []string
		if facilityStr, ok := roomData["facility"].(string); ok && facilityStr != "" {
			parts := strings.FieldsFunc(facilityStr, func(r rune) bool { return r == ' ' || r == ';' || r == '；' || r == ',' || r == '，' })
			for _, part := range parts {
				if _, ok := facilityCache[part]; !ok {
					validationErrors = append(validationErrors, fmt.Sprintf("第%d行: 设施 '%s' 在系统中不存在", rowNum, part))
				}
				validFacilities = append(validFacilities, part)
			}
		}

		groupName := roomData["group_name"].(string)

		visibleScopeStr := roomData["visible_scope"].(string)
		needApproval := 0
		visibleScope := "null"
		if visibleScopeStr == "全员可见" {
			visibleScope = `{"type":"all"}`
		} else { // 部分成员可见
			needApproval = 1
		}

		capacity, _ := roomData["capacity"].(int)

		roomsToCreate = append(roomsToCreate, model.EycMeetingRoom{
			Corpid:        corpIDStr,
			RoomName:      roomData["room_name"].(string),
			Location:      roomData["location"].(string),
			Capacity:      capacity,
			Facility:      strings.Join(validFacilities, ","),
			Remark:        roomData["remark"].(string),
			IsOpenBooking: roomData["is_open_booking"].(int),
			NeedApproval:  needApproval,
			VisibleScope:  []byte(visibleScope),
			// 临时存储分组信息
			Title: groupName,
		})
	}

	if len(validationErrors) > 0 {
		return errors.New(strings.Join(validationErrors, "; "))
	}

	// 数据库操作
	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 为每个会议室找到或创建扁平化的分组，并更新会议室的GroupID
	for i := range roomsToCreate {
		room := &roomsToCreate[i]
		groupName := room.Title

		groupID, err := findOrCreateFlattenedGroupInTx(tx, room.Corpid, groupName)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("处理分组失败: %w", err)
		}
		room.GroupID = groupID

		if err := tx.Create(room).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("创建会议室 '%s' 失败: %w", room.RoomName, err)
		}
	}

	return tx.Commit().Error
}

// findOrCreateFlattenedGroupInTx 在事务中查找或创建扁平化后的分组
func findOrCreateFlattenedGroupInTx(tx *gorm.DB, corpid string, groupName string) (int, error) {
	if groupName == "" {
		return 0, nil // 没有分组名称，视为未分组
	}

	var group model.EycMeetingGroup
	err := tx.Where("corp_id = ? AND title = ?", corpid, groupName).First(&group).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 没找到，创建新的分组
			newGroup := model.EycMeetingGroup{
				Corpid: corpid,
				Title:  groupName,
				// ParentId, Sort等字段使用其默认值或由数据库自动处理
			}
			if err := tx.Create(&newGroup).Error; err != nil {
				return 0, err
			}
			return newGroup.ID, nil
		}
		// 其他数据库错误
		return 0, err
	}

	// 找到了，返回其ID
	return group.ID, nil
}

// GenerateTemplateExcel 生成用于批量添加会议室的Excel模板文件
func (s *EycMeetingRoomService) GenerateTemplateExcel() (*bytes.Buffer, error) {
	f := excelize.NewFile()
	sheetName := "会议室批量导入模板"
	f.SetSheetName("Sheet1", sheetName)

	// 设置填写须知
	instructions := `填写须知:
1. 请勿修改表格结构
2. 各个字段字符输入上限：会议室名称(30)、分组名称(20)、地址(50)、容纳人数(10000)、备注(100)
3. 其中会议室名称为必填项，其他字段均可选填。
4. 会议室开放预定仅支持填写"开放"和"关闭"。
5. 会议室可见范围仅支持填写"全员可见"和"部分成员可见"。
6. 会议室设施填写多个设施类型之间请使用;间隔，不支持输入管理后台当前未添加的设施类型。`
	f.MergeCell(sheetName, "A1", "J1")
	f.SetCellValue(sheetName, "A1", instructions)

	// 设置表头
	headers := []string{
		"会议室名称", "分组名称", "地址", "容纳人数", "会议室设施", "备注", "会议室开放预定", "会议室可见范围",
	}
	for i, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 2)
		f.SetCellValue(sheetName, cell, header)
	}

	// 设置样式
	style, _ := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{Vertical: "top", WrapText: true},
		Font:      &excelize.Font{Bold: true, Color: "FFFFFF"},
		Fill:      excelize.Fill{Type: "pattern", Color: []string{"#4F81BD"}, Pattern: 1},
	})
	f.SetCellStyle(sheetName, "A1", "A1", style)
	f.SetRowHeight(sheetName, 1, 100)

	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true},
	})
	f.SetCellStyle(sheetName, "A2", "H2", headerStyle)

	// 设置列宽
	f.SetColWidth(sheetName, "A", "A", 30)
	f.SetColWidth(sheetName, "B", "B", 20)
	f.SetColWidth(sheetName, "C", "C", 40)
	f.SetColWidth(sheetName, "D", "D", 15)
	f.SetColWidth(sheetName, "E", "F", 30)
	f.SetColWidth(sheetName, "G", "H", 20)

	// 将文件写入缓冲区
	buf, err := f.WriteToBuffer()
	if err != nil {
		return nil, fmt.Errorf("写入excel缓冲区失败: %w", err)
	}

	return buf, nil
}

// BatchDeleteRooms 批量删除会议室
func (s *EycMeetingRoomService) BatchDeleteRooms(ac *app.RequestContext, ids []uint) error {
	if len(ids) == 0 {
		return errors.New("请选择要删除的会议室")
	}

	corpID, _ := ac.Get("corp_id")
	corpIDStr, ok := corpID.(string)
	if !ok || corpIDStr == "" {
		return errors.New("无效的企业ID")
	}

	tx := model.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 检查是否有未完成的预定
	var conflictingBooking model.EycMeetingBooking
	// 构建一个查询，检查任何一个待删除的room_id是否出现在预定中
	query := tx.Model(&model.EycMeetingBooking{}).Where("end_time > ?", time.Now())
	var orConditions []string
	var orValues []interface{}
	for _, id := range ids {
		orConditions = append(orConditions, "FIND_IN_SET(?, room_ids) > 0")
		orValues = append(orValues, id)
	}
	query = query.Where(strings.Join(orConditions, " OR "), orValues...)

	err := query.First(&conflictingBooking).Error
	if err == nil { // 如果找到了记录，说明有冲突
		tx.Rollback()
		return errors.New("删除失败：所选会议室中至少有一个存在未完成的预定")
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) { // 如果是其他数据库错误
		tx.Rollback()
		return fmt.Errorf("检查会议室预定时发生错误: %w", err)
	}

	// 2. 验证所有ID都存在且属于该企业，并找出所有未分组的会议室
	var roomsToDelete []model.EycMeetingRoom
	if err := tx.Where("id IN ?", ids).Find(&roomsToDelete).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("查询待删除会议室失败: %w", err)
	}

	if len(roomsToDelete) != len(ids) {
		tx.Rollback()
		return errors.New("部分会议室ID不存在")
	}

	ungroupedRoomsSorts := make([]int, 0)
	for _, room := range roomsToDelete {
		if room.Corpid != corpIDStr {
			tx.Rollback()
			return fmt.Errorf("无权删除会议室 '%s' (ID: %d)", room.RoomName, room.ID)
		}
		if room.GroupID == 0 {
			ungroupedRoomsSorts = append(ungroupedRoomsSorts, room.Sort)
		}
	}

	// 3. 执行批量删除
	if err := tx.Where("id IN ?", ids).Delete(&model.EycMeetingRoom{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("批量删除会议室失败: %w", err)
	}

	// 4. 对被删除的未分组会议室，更新后续会议室的排序
	if len(ungroupedRoomsSorts) > 0 {
		// 对排序值进行排序，从大到小处理，避免冲突
		sort.Sort(sort.Reverse(sort.IntSlice(ungroupedRoomsSorts)))
		for _, sortVal := range ungroupedRoomsSorts {
			if err := tx.Model(&model.EycMeetingRoom{}).
				Where("group_id = 0 AND corpid = ? AND sort > ?", corpIDStr, sortVal).
				Update("sort", gorm.Expr("sort - 1")).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("更新会议室排序失败: %w", err)
			}
		}
	}

	return tx.Commit().Error
}
