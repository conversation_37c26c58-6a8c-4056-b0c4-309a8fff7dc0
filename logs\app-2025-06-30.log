{"level":"INFO","time":"2025-06-30T14:23:40.024+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":148,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:40.592+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":548,"影响行数":1}
{"level":"INFO","time":"2025-06-30T14:23:41.208+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":615,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:41.391+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"CREATE TABLE `t_user` (`id` bigint unsigned AUTO_INCREMENT,`username` varchar(50) NOT NULL,`password` varchar(100) NOT NULL,`nickname` varchar(50),`email` varchar(100),`avatar` varchar(255),`status` bigint DEFAULT 1,`created_at` datetime(3) NULL,`updated_at` datetime(3) NULL,PRIMARY KEY (`id`),UNIQUE INDEX `idx_t_user_username` (`username`),UNIQUE INDEX `idx_t_user_email` (`email`))","耗时":180,"影响行数":0}
{"level":"INFO","time":"2025-06-30T14:23:41.391+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T14:23:41.754+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T14:23:41.756+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T14:23:41.756+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T14:23:41.757+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T14:23:49.223+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T14:23:49.223+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T14:23:49.223+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T14:23:52.855+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":387,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:53.341+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":461,"影响行数":1}
{"level":"INFO","time":"2025-06-30T14:23:53.708+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":366,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:53.824+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:53.971+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":146,"影响行数":1}
{"level":"INFO","time":"2025-06-30T14:23:54.123+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":152,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:54.256+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":132,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:54.317+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:54.558+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":239,"影响行数":1}
{"level":"INFO","time":"2025-06-30T14:23:54.721+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":162,"影响行数":3}
{"level":"INFO","time":"2025-06-30T14:23:54.805+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:54.998+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":192,"影响行数":1}
{"level":"INFO","time":"2025-06-30T14:23:55.335+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":335,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:55.399+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:55.640+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":241,"影响行数":1}
{"level":"INFO","time":"2025-06-30T14:23:56.219+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":578,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:56.272+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:56.419+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":146,"影响行数":1}
{"level":"INFO","time":"2025-06-30T14:23:56.619+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":199,"影响行数":3}
{"level":"INFO","time":"2025-06-30T14:23:56.758+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:57.258+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":500,"影响行数":1}
{"level":"INFO","time":"2025-06-30T14:23:57.410+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":151,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:57.473+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:57.639+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":166,"影响行数":1}
{"level":"INFO","time":"2025-06-30T14:23:57.808+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":168,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:57.868+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:58.068+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-06-30T14:23:58.228+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":160,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:58.290+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:58.469+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-06-30T14:23:58.634+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T14:23:58.634+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T14:23:58.798+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T14:23:58.799+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T14:23:58.799+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T14:23:58.800+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T15:37:41.646+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T15:37:41.647+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T15:37:41.647+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T15:37:47.591+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":41,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:47.709+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":90,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:37:47.947+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":237,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:48.145+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":197,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:48.226+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":80,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:37:48.462+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":236,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:48.706+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":241,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:48.823+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:49.068+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":244,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:37:49.383+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":315,"影响行数":3}
{"level":"INFO","time":"2025-06-30T15:37:49.506+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":121,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:49.626+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:37:49.905+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":279,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:50.021+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:50.179+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:37:50.501+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":321,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:50.742+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":240,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:50.976+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":232,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:37:51.146+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":169,"影响行数":3}
{"level":"INFO","time":"2025-06-30T15:37:51.382+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":235,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:51.581+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":198,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:37:51.831+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":248,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:51.981+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":149,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:52.063+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":81,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:37:52.341+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":277,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:52.463+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":121,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:52.662+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":198,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:37:52.906+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":243,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:53.026+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:53.196+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":170,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:37:53.461+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":264,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:37:53.461+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T15:37:53.706+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T15:37:53.708+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T15:37:53.708+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T15:37:53.709+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T15:54:21.501+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T15:54:21.503+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T15:54:21.504+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T15:54:26.407+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:26.541+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":119,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:54:26.696+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":154,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:26.770+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:26.891+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:54:26.991+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:27.131+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:27.190+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:27.292+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:54:27.392+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":99,"影响行数":3}
{"level":"INFO","time":"2025-06-30T15:54:27.450+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:27.507+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":55,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:54:27.607+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:27.652+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":44,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:27.751+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:54:27.908+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":155,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:28.026+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:28.267+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":240,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:54:28.470+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":203,"影响行数":3}
{"level":"INFO","time":"2025-06-30T15:54:28.595+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":125,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:28.808+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":212,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:54:29.032+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":224,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:29.084+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":52,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:29.212+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":126,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:54:29.346+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:29.410+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:29.527+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":116,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:54:29.674+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":146,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:29.730+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":55,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:29.942+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":211,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:54:30.040+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:54:30.040+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T15:54:30.151+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T15:54:30.153+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T15:54:30.153+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T15:54:30.154+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T15:54:35.493+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":164,"影响行数":2}
{"level":"ERROR","time":"2025-06-30T15:54:35.684+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND parentid = 0 AND title = '杭州' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":87,"error":"Error 1054 (42S22): Unknown column 'parentid' in 'where clause'"}
{"level":"INFO","time":"2025-06-30T15:56:25.265+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T15:56:25.266+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T15:56:25.266+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T15:56:29.530+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:29.767+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":209,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:30.004+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":236,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:30.124+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:30.203+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":77,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:30.453+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":249,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:30.647+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":193,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:30.814+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":166,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:31.047+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":233,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:31.245+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":197,"影响行数":3}
{"level":"INFO","time":"2025-06-30T15:56:31.369+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":123,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:31.617+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":247,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:31.808+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":189,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:31.925+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:32.129+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":203,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:32.339+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":208,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:32.422+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":82,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:32.592+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:32.809+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":216,"影响行数":3}
{"level":"INFO","time":"2025-06-30T15:56:32.919+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:33.070+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":151,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:33.367+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":297,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:33.468+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":100,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:33.602+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":133,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:33.772+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":167,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:33.841+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:34.052+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":210,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:34.194+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:34.257+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:34.439+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":181,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:34.591+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":152,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:34.592+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T15:56:34.734+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T15:56:34.736+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T15:56:34.736+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T15:56:34.737+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T15:56:39.967+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":147,"影响行数":2}
{"level":"ERROR","time":"2025-06-30T15:56:40.162+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND parent_id = 0 AND title = '杭州' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":119,"error":"Error 1054 (42S22): Unknown column 'parent_id' in 'where clause'"}
{"level":"INFO","time":"2025-06-30T15:56:44.567+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":204,"影响行数":2}
{"level":"ERROR","time":"2025-06-30T15:56:44.703+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND parent_id = 0 AND title = '杭州' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":68,"error":"Error 1054 (42S22): Unknown column 'parent_id' in 'where clause'"}
{"level":"INFO","time":"2025-06-30T15:56:48.235+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T15:56:48.236+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T15:56:48.236+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T15:56:49.990+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:50.184+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":162,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:50.328+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":143,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:50.400+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:50.537+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:50.690+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":152,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:50.820+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:50.887+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:50.992+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":104,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:51.127+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":134,"影响行数":3}
{"level":"INFO","time":"2025-06-30T15:56:51.167+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":40,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:51.325+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:51.450+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:51.522+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:51.687+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:51.838+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":151,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:51.955+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":115,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:52.130+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":175,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:52.323+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":192,"影响行数":3}
{"level":"INFO","time":"2025-06-30T15:56:52.444+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:52.612+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":167,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:52.787+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":174,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:52.851+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:52.959+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":106,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:53.069+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:53.127+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:53.278+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":150,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:53.424+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":145,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:53.487+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:53.807+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":320,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:56:53.974+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":166,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:56:53.974+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T15:56:54.084+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T15:56:54.086+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T15:56:54.086+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T15:56:54.086+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T15:57:04.707+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":112,"影响行数":2}
{"level":"ERROR","time":"2025-06-30T15:57:04.902+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND parent_id = 0 AND title = '杭州' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":94,"error":"Error 1054 (42S22): Unknown column 'parent_id' in 'where clause'"}
{"level":"INFO","time":"2025-06-30T15:59:50.383+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T15:59:50.384+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T15:59:50.384+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T15:59:55.055+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:55.211+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:59:55.316+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:55.408+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:55.607+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":198,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:59:55.806+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":198,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:55.947+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:56.040+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":92,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:56.190+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:59:56.324+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":133,"影响行数":3}
{"level":"INFO","time":"2025-06-30T15:59:56.447+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:56.643+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":195,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:59:56.811+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":167,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:56.920+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":108,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:57.109+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":188,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:59:57.281+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":171,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:57.380+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:57.542+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":161,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:59:57.688+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":146,"影响行数":3}
{"level":"INFO","time":"2025-06-30T15:59:57.768+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:58.031+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":262,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:59:58.178+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":146,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:58.283+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:58.459+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":175,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:59:58.678+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":218,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:58.766+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:58.966+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:59:59.087+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:59.178+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:59.336+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-30T15:59:59.465+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T15:59:59.466+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T15:59:59.603+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T15:59:59.604+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T15:59:59.604+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T15:59:59.606+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T16:00:02.684+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T16:00:02.685+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T16:00:02.685+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T16:00:04.212+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":51,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:04.358+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:00:04.455+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:04.533+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:04.604+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":70,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:00:04.746+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:04.854+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":107,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:04.902+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":47,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:05.021+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":118,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:00:05.123+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":101,"影响行数":3}
{"level":"INFO","time":"2025-06-30T16:00:05.196+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:05.328+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":132,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:00:05.451+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:05.501+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:05.627+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:00:05.784+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":156,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:05.845+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:06.041+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":195,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:00:06.286+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":245,"影响行数":3}
{"level":"INFO","time":"2025-06-30T16:00:06.350+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":63,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:06.541+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":190,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:00:06.720+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":179,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:06.816+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:06.934+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":118,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:00:07.071+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":135,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:07.119+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":47,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:07.219+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:00:07.378+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:07.459+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:07.633+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":174,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:00:07.761+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:00:07.761+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T16:00:07.896+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T16:00:07.896+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T16:00:07.896+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T16:00:07.897+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T16:00:17.654+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":137,"影响行数":2}
{"level":"ERROR","time":"2025-06-30T16:00:17.821+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND parentid = 0 AND title = '杭州' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":67,"error":"Error 1054 (42S22): Unknown column 'parentid' in 'where clause'"}
{"level":"INFO","time":"2025-06-30T16:12:42.168+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T16:12:42.168+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T16:12:42.168+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T16:12:46.840+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:47.096+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":229,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:12:47.381+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":285,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:47.470+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":88,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:47.650+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:12:47.842+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":191,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:48.096+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":253,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:48.293+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":195,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:48.612+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":318,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:12:48.820+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":208,"影响行数":3}
{"level":"INFO","time":"2025-06-30T16:12:48.948+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":127,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:49.886+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":938,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:12:50.548+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":661,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:50.661+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":112,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:50.940+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":279,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:12:51.185+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":244,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:51.300+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":114,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:51.501+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":201,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:12:51.780+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":278,"影响行数":3}
{"level":"INFO","time":"2025-06-30T16:12:51.902+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":121,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:52.185+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":282,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:12:52.385+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:52.502+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:52.748+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":244,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:12:52.981+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":232,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:53.080+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:53.263+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":183,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:12:53.537+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":274,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:53.660+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":121,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:53.897+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":236,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:12:54.085+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":188,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:12:54.086+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T16:12:54.350+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T16:12:54.352+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T16:12:54.352+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T16:12:54.354+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T16:13:06.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":181,"影响行数":2}
{"level":"INFO","time":"2025-06-30T16:13:07.142+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '杭州/西溪园区/1号楼' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":205,"影响行数":0}
{"level":"ERROR","time":"2025-06-30T16:13:07.262+0800","caller":"model/logger.go:75","msg":"GORM错误","sql":"INSERT INTO `eyc_meeting_groups` (`corp_id`,`title`,`d_group_id`,`created_at`,`updated_at`,`sort`,`lock`,`type`,`status`,`room_id`,`meet_room_name`,`parentid`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','杭州/西溪园区/1号楼',0,'2025-06-30 16:13:07.143','2025-06-30 16:13:07.143',0,0,0,0,'','',0)","耗时":119,"error":"Error 1054 (42S22): Unknown column 'parentid' in 'field list'"}
{"level":"INFO","time":"2025-06-30T16:16:55.198+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T16:16:55.199+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T16:16:55.199+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T16:16:59.850+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:00.087+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":224,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:17:00.308+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":219,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:00.418+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:00.657+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":238,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:17:00.842+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":185,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:00.978+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:01.087+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":109,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:01.302+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":213,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:17:01.486+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":184,"影响行数":3}
{"level":"INFO","time":"2025-06-30T16:17:01.608+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":121,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:01.781+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":173,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:17:01.986+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":204,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:02.105+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:02.345+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":239,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:17:02.506+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":160,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:02.663+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":156,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:02.865+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":202,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:17:03.102+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":237,"影响行数":3}
{"level":"INFO","time":"2025-06-30T16:17:03.225+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":122,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:03.473+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":247,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:17:03.705+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":231,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:03.847+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:04.012+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:17:04.225+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":211,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:04.329+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:04.520+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":189,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:17:04.728+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":207,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:04.904+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":176,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:05.213+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":308,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:17:05.371+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":157,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:17:05.371+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T16:17:05.575+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T16:17:05.576+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T16:17:05.576+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T16:17:05.577+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T16:17:10.711+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":373,"影响行数":2}
{"level":"INFO","time":"2025-06-30T16:17:11.060+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '杭州/西溪园区/1号楼' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":229,"影响行数":0}
{"level":"INFO","time":"2025-06-30T16:17:11.226+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_groups` (`corp_id`,`title`,`d_group_id`,`created_at`,`updated_at`,`sort`,`lock`,`type`,`status`,`room_id`,`meet_room_name`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','杭州/西溪园区/1号楼',0,'2025-06-30 16:17:11.06','2025-06-30 16:17:11.06',0,0,0,0,'','')","耗时":165,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:17:11.367+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`meet_room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','','亚马逊河会议室',187,'中国浙江省杭州市','',10,'',1,NULL,'这是一个测试备注',0,'{\"type\":\"all\"}',0,'','','','',0,'',0,0,1,0,0,0,0,0,0)","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:27:10.525+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T16:27:10.525+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T16:27:10.525+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T16:27:15.333+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":276,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:15.633+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":287,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:27:15.831+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":198,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:16.036+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":203,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:16.247+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":209,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:27:16.552+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":305,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:16.796+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":243,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:16.911+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":113,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:17.049+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":137,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:27:17.311+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":262,"影响行数":3}
{"level":"INFO","time":"2025-06-30T16:27:17.442+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:17.557+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:27:17.793+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":235,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:17.917+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":123,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:18.117+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:27:18.327+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":209,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:18.462+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:18.982+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":519,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:27:19.199+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":215,"影响行数":3}
{"level":"INFO","time":"2025-06-30T16:27:19.317+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:19.519+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":201,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:27:19.731+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":211,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:19.873+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:20.116+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":242,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:27:20.276+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:20.394+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:20.628+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":233,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:27:20.884+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":256,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:20.972+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":86,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:21.193+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":220,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:27:21.392+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":198,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:27:21.392+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T16:27:21.631+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T16:27:21.632+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T16:27:21.633+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T16:27:21.634+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T16:27:33.855+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":253,"影响行数":2}
{"level":"INFO","time":"2025-06-30T16:27:34.232+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '杭州/西溪园区/1号楼' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":188,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:27:34.457+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`meet_room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','','亚马逊河会议室',187,'中国浙江省杭州市','',10,'',1,NULL,'这是一个测试备注',0,'{\"type\":\"all\"}',0,'','','','',0,'',0,0,1,0,0,0,0,0,0)","耗时":224,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:28:47.644+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T16:28:47.645+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T16:28:47.645+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T16:28:51.160+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:51.394+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":206,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:28:51.643+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":248,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:51.754+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:52.073+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":317,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:28:52.315+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":241,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:52.599+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":284,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:52.719+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:52.913+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":194,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:28:53.171+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":257,"影响行数":3}
{"level":"INFO","time":"2025-06-30T16:28:53.257+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:53.456+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":198,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:28:53.641+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":184,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:53.759+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:54.004+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":244,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:28:54.241+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":237,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:54.355+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":113,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:54.593+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":237,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:28:54.836+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":242,"影响行数":3}
{"level":"INFO","time":"2025-06-30T16:28:54.994+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:55.203+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":208,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:28:55.398+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":195,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:55.519+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":120,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:55.720+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":200,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:28:55.911+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":190,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:56.045+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":133,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:56.260+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":214,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:28:56.517+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":257,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:56.678+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:56.878+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":199,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:28:57.117+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":238,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:28:57.118+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T16:28:57.360+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T16:28:57.362+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T16:28:57.362+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T16:28:57.363+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T16:31:13.074+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T16:31:13.075+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T16:31:13.075+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T16:31:17.496+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":94,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:17.671+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:31:17.841+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":170,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:17.942+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:18.091+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":148,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:31:18.256+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:18.374+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:18.455+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:18.636+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":179,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:31:18.794+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":158,"影响行数":3}
{"level":"INFO","time":"2025-06-30T16:31:18.876+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":81,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:19.078+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":202,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:31:19.259+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":180,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:19.326+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:19.559+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":232,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:31:19.841+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":281,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:19.959+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:20.156+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":196,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:31:20.751+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":595,"影响行数":3}
{"level":"INFO","time":"2025-06-30T16:31:20.824+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":72,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:20.989+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":164,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:31:21.188+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":199,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:21.278+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:21.421+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":142,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:31:21.639+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":217,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:21.711+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":71,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:21.871+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":160,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:31:22.123+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":251,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:22.228+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":105,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:22.349+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":120,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:31:22.502+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":152,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:31:22.502+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T16:31:22.628+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T16:31:22.629+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T16:31:22.629+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T16:31:22.630+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T16:31:27.477+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":282,"影响行数":2}
{"level":"INFO","time":"2025-06-30T16:31:27.719+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '杭州/西溪园区/1号楼' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:31:27.997+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`meet_room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','','亚马逊河会议室',187,'中国浙江省杭州市','',10,'',1,NULL,'这是一个测试备注',0,'{\"type\":\"all\"}',0,'','','','',0,'',0,0,1,0,0,0,0,0,0)","耗时":277,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:38:00.217+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T16:38:00.217+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T16:38:00.218+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T16:38:03.600+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":110,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:03.841+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":224,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:38:04.088+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":246,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:04.177+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":88,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:04.289+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":112,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:38:04.448+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:04.588+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:04.649+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":60,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:04.731+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":81,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:38:04.856+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":124,"影响行数":3}
{"level":"INFO","time":"2025-06-30T16:38:04.891+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":34,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:05.016+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":124,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:38:05.128+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":112,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:05.178+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":49,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:05.344+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":165,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:38:05.508+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":163,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:05.591+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":82,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:05.745+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":153,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:38:05.889+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":144,"影响行数":3}
{"level":"INFO","time":"2025-06-30T16:38:05.936+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:06.055+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":119,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:38:06.200+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":143,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:06.266+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":65,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:06.377+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":111,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:38:06.521+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":143,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:06.568+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:06.735+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":166,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:38:06.856+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":121,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:06.898+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":41,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:07.028+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:38:07.131+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":101,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T16:38:07.131+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T16:38:07.244+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T16:38:07.246+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T16:38:07.246+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T16:38:07.248+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T16:38:11.289+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":122,"影响行数":2}
{"level":"INFO","time":"2025-06-30T16:38:11.490+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '杭州/西溪园区/1号楼' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:38:11.658+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`meet_room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','','亚马逊河会议室',187,'中国浙江省杭州市','',10,'',1,NULL,'这是一个测试备注',0,'{\"type\":\"all\"}',0,'','','','',0,'',0,0,1,0,0,0,0,0,0)","耗时":168,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:40:18.821+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":154,"影响行数":2}
{"level":"INFO","time":"2025-06-30T16:40:19.167+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '郑州/管城区/升龙广场' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":241,"影响行数":0}
{"level":"INFO","time":"2025-06-30T16:40:19.376+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_groups` (`corp_id`,`title`,`d_group_id`,`created_at`,`updated_at`,`sort`,`lock`,`type`,`status`,`room_id`,`meet_room_name`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','郑州/管城区/升龙广场',0,'2025-06-30 16:40:19.168','2025-06-30 16:40:19.168',0,0,0,0,'','')","耗时":208,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:40:19.568+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`meet_room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','','黄河会议室',188,'三号楼A座3202','',10,'',1,NULL,'这是一个测试备注',0,'{\"type\":\"all\"}',0,'','','','',0,'',0,0,1,0,0,0,0,0,0)","耗时":192,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:42:22.644+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_facility` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884'","耗时":118,"影响行数":2}
{"level":"INFO","time":"2025-06-30T16:42:22.841+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_groups` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND title = '郑州/管城区/升龙广场' ORDER BY `eyc_meeting_groups`.`id` LIMIT 1","耗时":137,"影响行数":1}
{"level":"INFO","time":"2025-06-30T16:42:22.983+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"INSERT INTO `eyc_meeting_room` (`corpid`,`title`,`meet_room_name`,`group_id`,`location`,`facility`,`capacity`,`device`,`is_open_booking`,`image`,`remark`,`sort`,`visible_scope`,`need_approval`,`approval_scope`,`approval_type`,`open_start_time`,`open_end_time`,`earliest_book_cycle`,`earliest_book_time`,`min_book_duration`,`max_book_duration`,`allow_recurring`,`allow_occupy`,`keep_signin`,`show_signin_advance_time`,`release_overtime_time`,`allow_delay`,`delay_time`) VALUES ('ding424e63f5c9ac81e1ffe93478753d9884','','黄河会议室',188,'中国河南省郑州市','',10,'',1,NULL,'这是一个测试备注',0,'{\"type\":\"all\"}',0,'','','','',0,'',0,0,1,0,0,0,0,0,0)","耗时":141,"影响行数":1}
{"level":"INFO","time":"2025-06-30T17:49:57.382+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T17:49:57.383+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T17:49:57.383+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T17:50:04.314+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":79,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:04.454+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":114,"影响行数":1}
{"level":"INFO","time":"2025-06-30T17:50:04.594+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:04.671+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:04.815+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":143,"影响行数":1}
{"level":"INFO","time":"2025-06-30T17:50:04.934+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:05.076+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:05.119+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":42,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:05.238+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":118,"影响行数":1}
{"level":"INFO","time":"2025-06-30T17:50:05.324+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":85,"影响行数":3}
{"level":"INFO","time":"2025-06-30T17:50:05.386+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:05.534+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":148,"影响行数":1}
{"level":"INFO","time":"2025-06-30T17:50:05.675+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:05.732+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:05.842+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":109,"影响行数":1}
{"level":"INFO","time":"2025-06-30T17:50:05.939+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":97,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:05.994+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":55,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:06.070+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":75,"影响行数":1}
{"level":"INFO","time":"2025-06-30T17:50:06.196+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":124,"影响行数":3}
{"level":"INFO","time":"2025-06-30T17:50:06.235+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":39,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:06.366+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":131,"影响行数":1}
{"level":"INFO","time":"2025-06-30T17:50:06.471+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":104,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:06.526+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":55,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:06.615+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":88,"影响行数":1}
{"level":"INFO","time":"2025-06-30T17:50:06.705+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":89,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:06.752+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":46,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:06.834+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":81,"影响行数":1}
{"level":"INFO","time":"2025-06-30T17:50:06.931+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:06.989+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:07.100+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-30T17:50:07.184+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T17:50:07.185+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T17:50:07.351+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T17:50:07.352+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T17:50:07.352+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T17:50:07.353+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T17:51:51.358+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":246,"影响行数":36}
{"level":"INFO","time":"2025-06-30T17:51:51.606+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND start_time < '2025-06-11 00:00:00' AND end_time > '2025-06-10 00:00:00' AND status != 'canceled'","耗时":247,"影响行数":0}
{"level":"INFO","time":"2025-06-30T17:52:01.906+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":231,"影响行数":36}
{"level":"INFO","time":"2025-06-30T17:52:02.046+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND start_time < '2025-06-16 00:00:00' AND end_time > '2025-06-15 00:00:00' AND status != 'canceled'","耗时":140,"影响行数":0}
{"level":"INFO","time":"2025-06-30T17:52:10.168+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":233,"影响行数":36}
{"level":"INFO","time":"2025-06-30T17:52:10.339+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND start_time < '2025-07-01 00:00:00' AND end_time > '2025-06-30 00:00:00' AND status != 'canceled'","耗时":170,"影响行数":0}
{"level":"INFO","time":"2025-06-30T17:53:51.920+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":152,"影响行数":36}
{"level":"INFO","time":"2025-06-30T17:53:52.035+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND start_time < '2025-07-01 00:00:00' AND end_time > '2025-06-30 00:00:00' AND status != 'canceled'","耗时":114,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:01:52.899+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T18:01:52.901+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T18:01:52.902+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T18:01:55.202+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":39,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:55.303+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":79,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:01:55.422+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":117,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:55.493+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:55.623+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:01:55.729+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":106,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:55.860+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":130,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:55.904+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":42,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:56.014+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":110,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:01:56.134+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":119,"影响行数":3}
{"level":"INFO","time":"2025-06-30T18:01:56.177+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":42,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:56.263+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":86,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:01:56.362+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:56.408+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":44,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:56.502+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":93,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:01:56.614+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":111,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:56.663+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":48,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:56.754+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":91,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:01:56.842+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":87,"影响行数":3}
{"level":"INFO","time":"2025-06-30T18:01:56.898+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":55,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:57.002+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":103,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:01:57.118+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":116,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:57.199+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:57.352+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":153,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:01:57.439+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":86,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:57.502+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:57.582+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":80,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:01:57.665+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":81,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:57.719+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:57.842+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":122,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:01:57.982+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":139,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:01:57.982+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T18:01:58.105+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T18:01:58.106+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T18:01:58.106+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T18:01:58.108+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T18:02:02.963+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":156,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:02:03.094+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND start_time < '2025-07-01 00:00:00' AND end_time > '2025-06-30 00:00:00' AND status != 'canceled'","耗时":131,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:02:31.603+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":208,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:02:40.638+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":573,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:02:40.973+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND start_time < '2025-06-04 00:00:00' AND end_time > '2025-06-03 00:00:00' AND status != 'canceled'","耗时":334,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:09:19.720+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T18:09:19.721+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T18:09:19.721+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T18:09:24.915+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":37,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:25.029+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":87,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:25.170+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":140,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:25.241+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:25.368+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":125,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:25.505+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":137,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:25.625+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:25.670+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":43,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:25.746+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":75,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:25.842+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":95,"影响行数":3}
{"level":"INFO","time":"2025-06-30T18:09:25.869+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":25,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:25.941+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":71,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:26.036+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":95,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:26.092+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:26.201+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":108,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:26.272+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:26.324+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":50,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:26.401+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":77,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:26.497+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":96,"影响行数":3}
{"level":"INFO","time":"2025-06-30T18:09:26.563+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":66,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:26.653+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":89,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:26.752+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:26.792+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":39,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:26.894+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":100,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:26.976+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":81,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:27.018+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":38,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:27.117+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":98,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:27.228+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":111,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:27.271+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":43,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:27.391+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":119,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:27.510+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:27.510+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T18:09:27.619+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T18:09:27.620+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T18:09:27.620+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T18:09:27.621+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T18:09:32.234+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":240,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:09:32.452+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND start_time < '2025-06-04 00:00:00' AND end_time > '2025-06-03 00:00:00'","耗时":218,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:09:40.944+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":178,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:09:41.072+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND start_time < '2025-06-04 00:00:00' AND end_time > '2025-06-03 00:00:00'","耗时":127,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:09:53.075+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":207,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:09:53.188+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND start_time < '2025-06-04 00:00:00' AND end_time > '2025-06-03 00:00:00'","耗时":112,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:09:54.622+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T18:09:54.622+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T18:09:54.622+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T18:09:56.232+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":42,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:56.413+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":155,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:56.608+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":194,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:56.693+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:56.853+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:57.031+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":177,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:57.192+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":160,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:57.266+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:57.424+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":157,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:57.593+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":168,"影响行数":3}
{"level":"INFO","time":"2025-06-30T18:09:57.686+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":93,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:57.792+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":105,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:57.982+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":189,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:58.073+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":90,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:58.251+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":177,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:58.429+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":177,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:58.532+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":102,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:58.693+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":159,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:58.849+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":155,"影响行数":3}
{"level":"INFO","time":"2025-06-30T18:09:58.904+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":54,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:59.074+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":169,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:59.289+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":214,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:59.372+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":82,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:59.513+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":140,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:09:59.722+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":208,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:09:59.823+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:10:00.008+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":185,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:10:00.133+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":124,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:10:00.232+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:10:00.383+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":151,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:10:00.568+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":184,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:10:00.568+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T18:10:00.705+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T18:10:00.707+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T18:10:00.707+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T18:10:00.708+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T18:10:05.453+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":193,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:10:05.592+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND start_time < '2025-06-04 00:00:00' AND end_time > '2025-06-03 00:00:00'","耗时":138,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:10:10.386+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":202,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:10:10.553+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND start_time < '2025-06-04 00:00:00' AND end_time > '2025-06-03 00:00:00'","耗时":165,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:13:18.537+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T18:13:18.538+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T18:13:18.538+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T18:13:22.357+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":88,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:22.517+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":147,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:22.676+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":158,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:22.751+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":73,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:23.010+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":259,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:23.158+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":147,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:23.338+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":179,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:23.408+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":69,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:23.557+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:23.750+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":191,"影响行数":3}
{"level":"INFO","time":"2025-06-30T18:13:23.838+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":87,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:23.995+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":156,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:24.122+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":126,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:24.185+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:24.321+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":136,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:24.400+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:24.476+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":75,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:24.596+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":119,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:24.715+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":119,"影响行数":3}
{"level":"INFO","time":"2025-06-30T18:13:24.777+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":61,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:24.917+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":139,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:25.060+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":142,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:25.120+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":59,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:25.257+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":137,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:25.376+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:25.428+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":51,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:25.532+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":103,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:25.675+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":141,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:25.756+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":80,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:25.873+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":116,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:25.992+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":118,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:25.992+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T18:13:26.115+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T18:13:26.116+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T18:13:26.116+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T18:13:26.118+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T18:13:40.625+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":153,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:13:40.718+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND DATE(start_time) <= '2025-06-03' AND DATE(end_time) >= '2025-06-03'","耗时":91,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:13:48.369+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T18:13:48.370+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T18:13:48.370+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T18:13:50.158+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:50.256+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":81,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:50.461+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":204,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:50.536+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:50.637+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:50.738+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":99,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:50.803+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:50.840+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":36,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:50.978+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":137,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:51.107+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":128,"影响行数":3}
{"level":"INFO","time":"2025-06-30T18:13:51.177+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":70,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:51.249+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":71,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:51.372+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":123,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:51.421+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":48,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:51.574+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":152,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:51.670+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:51.698+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":28,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:51.800+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":101,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:51.909+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":108,"影响行数":3}
{"level":"INFO","time":"2025-06-30T18:13:51.966+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":56,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:52.112+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":146,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:52.220+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":107,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:52.272+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":50,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:52.381+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":109,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:52.518+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":136,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:52.594+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":74,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:52.723+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:52.825+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":101,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:52.858+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":31,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:52.947+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":89,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:13:53.046+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":98,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:13:53.047+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T18:13:53.158+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T18:13:53.159+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T18:13:53.159+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T18:13:53.160+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T18:14:07.236+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":168,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:14:07.388+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND DATE(start_time) <= '2025-06-03' AND DATE(end_time) >= '2025-06-03'","耗时":152,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:17:41.031+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T18:17:41.032+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T18:17:41.032+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T18:17:44.866+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:45.031+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":137,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:17:45.228+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":195,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:45.305+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":76,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:45.518+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":213,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:17:45.732+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":212,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:46.033+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":300,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:46.170+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":135,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:46.343+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":173,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:17:46.515+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":171,"影响行数":3}
{"level":"INFO","time":"2025-06-30T18:17:46.592+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":77,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:46.703+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":109,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:17:46.863+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":159,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:46.926+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:47.088+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":161,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:17:47.274+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":184,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:47.362+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":86,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:47.537+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":174,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:17:47.690+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":152,"影响行数":3}
{"level":"INFO","time":"2025-06-30T18:17:47.763+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":72,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:47.948+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":184,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:17:48.136+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":187,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:48.206+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":68,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:48.413+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":206,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:17:48.632+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":218,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:48.688+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":55,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:48.856+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":168,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:17:48.995+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":138,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:49.092+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":96,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:49.220+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":127,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:17:49.478+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":258,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:17:49.478+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T18:17:49.727+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T18:17:49.728+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T18:17:49.728+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T18:17:49.730+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T18:17:54.998+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":288,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:17:55.222+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND DATE(start_time) <= '2025-06-03' AND DATE(end_time) >= '2025-06-03'","耗时":223,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:17:58.568+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":330,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:17:58.830+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND DATE(start_time) <= '2025-06-03' AND DATE(end_time) >= '2025-06-03'","耗时":261,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:21:01.552+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":127,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:21:01.637+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND DATE(start_time) <= '2025-06-30' AND DATE(end_time) >= '2025-06-30'","耗时":84,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:21:10.169+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":128,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:21:10.260+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND DATE(start_time) <= '2025-06-20' AND DATE(end_time) >= '2025-06-20'","耗时":91,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:30:25.411+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T18:30:25.411+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T18:30:25.411+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
{"level":"INFO","time":"2025-06-30T18:30:29.707+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":64,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:29.819+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":84,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:30:29.949+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.tables WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND table_type = 'BASE TABLE'","耗时":128,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:29.995+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":45,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:30.124+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":129,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:30:30.254+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `t_user` LIMIT 1","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:30.367+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' ORDER BY ORDINAL_POSITION","耗时":112,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:30.422+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":53,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:30.525+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":103,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:30:30.651+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":125,"影响行数":3}
{"level":"INFO","time":"2025-06-30T18:30:30.714+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":62,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:30.814+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":99,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:30:30.939+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_username'","耗时":123,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:31.007+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":67,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:31.106+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":98,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:30:31.220+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":113,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:31.279+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":58,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:31.409+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":130,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:30:31.537+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"\nSELECT\n\tTABLE_NAME,\n\tCOLUMN_NAME,\n\tINDEX_NAME,\n\tNON_UNIQUE \nFROM\n\tinformation_schema.STATISTICS \nWHERE\n\tTABLE_SCHEMA = 'eyc3_meeting' \n\tAND TABLE_NAME = 't_user' \nORDER BY\n\tINDEX_NAME,\n\tSEQ_IN_INDEX","耗时":127,"影响行数":3}
{"level":"INFO","time":"2025-06-30T18:30:31.617+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":78,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:31.766+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":149,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:30:31.850+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'eyc3_meeting' AND table_name = 't_user' AND constraint_name = 'uni_t_user_email'","耗时":84,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:31.897+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":47,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:31.981+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":83,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:30:32.067+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":85,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:32.099+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":31,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:32.243+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":143,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:30:32.372+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_username'","耗时":129,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:32.429+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT DATABASE()","耗时":57,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:32.535+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'eyc3_meeting%' ORDER BY SCHEMA_NAME='eyc3_meeting' DESC,SCHEMA_NAME limit 1","耗时":106,"影响行数":1}
{"level":"INFO","time":"2025-06-30T18:30:32.619+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'eyc3_meeting' AND table_name = 't_user' AND index_name = 'idx_t_user_email'","耗时":83,"影响行数":-1}
{"level":"INFO","time":"2025-06-30T18:30:32.619+0800","caller":"model/db.go:59","msg":"数据库初始化成功"}
{"level":"INFO","time":"2025-06-30T18:30:32.722+0800","caller":"redis/redis.go:63","msg":"Redis初始化成功"}
{"level":"INFO","time":"2025-06-30T18:30:32.724+0800","caller":"eyc3_meeting/main.go:61","msg":"服务已启动","port":9505,"mode":"debug"}
{"level":"INFO","time":"2025-06-30T18:30:32.724+0800","caller":"hlog/system.go:107","msg":"HERTZ: Using network library=standard"}
{"level":"INFO","time":"2025-06-30T18:30:32.725+0800","caller":"hlog/system.go:107","msg":"HERTZ: HTTP server listening on address=[::]:9505"}
{"level":"INFO","time":"2025-06-30T18:30:41.511+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":396,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:30:41.613+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND DATE(start_time) <= '2025-06-20' AND DATE(end_time) >= '2025-06-20'","耗时":101,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:30:48.415+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":219,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:30:48.560+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND DATE(start_time) <= '2025-6-20' AND DATE(end_time) >= '2025-6-20'","耗时":145,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:30:54.476+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":223,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:30:54.666+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND DATE(start_time) <= '2025-6-2' AND DATE(end_time) >= '2025-6-2'","耗时":189,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:31:00.026+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_room` WHERE corpid = 'ding424e63f5c9ac81e1ffe93478753d9884' ORDER BY sort ASC, id DESC","耗时":582,"影响行数":36}
{"level":"INFO","time":"2025-06-30T18:31:00.241+0800","caller":"model/logger.go:87","msg":"GORM查询","sql":"SELECT * FROM `eyc_meeting_bookings` WHERE corp_id = 'ding424e63f5c9ac81e1ffe93478753d9884' AND status != 3 AND DATE(start_time) <= '2025-6-02' AND DATE(end_time) >= '2025-6-02'","耗时":214,"影响行数":0}
{"level":"INFO","time":"2025-06-30T18:43:30.325+0800","caller":"eyc3_meeting/main.go:71","msg":"正在关闭服务..."}
{"level":"ERROR","time":"2025-06-30T18:43:30.325+0800","caller":"hlog/system.go:95","msg":"HERTZ: Error=accept tcp [::]:9505: use of closed network connection"}
{"level":"FATAL","time":"2025-06-30T18:43:30.326+0800","caller":"eyc3_meeting/main.go:57","msg":"启动服务器失败","error":"accept tcp [::]:9505: use of closed network connection"}
