package middleware

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"eyc3_meeting/config"
	"eyc3_meeting/internal/pkg/logger"
	"eyc3_meeting/internal/pkg/response"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/golang-jwt/jwt/v5"
)

// 声明JWT结构体
type Claims struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	CorpID   string `json:"corp_id"`
	Avatar   string `json:"avatar"`
	jwt.RegisteredClaims
}

// JWT中间件
func JWT() app.HandlerFunc {
	return func(c context.Context, ctx *app.RequestContext) {
		// 获取Header中的Token
		tokenString := getToken(ctx)
		if tokenString == "" {
			response.Unauthorized(ctx, "请先登录")
			ctx.Abort()
			return
		}

		// 解析Token
		claims, err := parseToken(tokenString)
		if err != nil {
			logger.Error("解析Token失败", logger.Error2(err))
			response.Unauthorized(ctx, "无效的Token")
			ctx.Abort()
			return
		}

		// 验证Token是否过期
		if claims.ExpiresAt == nil || claims.ExpiresAt.Time.Before(time.Now()) {
			response.Unauthorized(ctx, "Token已过期")
			ctx.Abort()
			return
		}

		// 验证用户是否存在
		//user, err := model.GetUserByID(claims.UserID)
		//if err != nil {
		//	logger.Error("获取用户信息失败", logger.Error2(err))
		//	response.ServerError(ctx, err)
		//	ctx.Abort()
		//	return
		//}
		//
		//if user == nil {
		//	response.Unauthorized(ctx, "用户不存在")
		//	ctx.Abort()
		//	return
		//}

		// 将用户信息写入上下文
		ctx.Set("user_id", claims.UserID)
		ctx.Set("username", claims.Username)
		ctx.Set("corp_id", claims.CorpID)
		ctx.Set("avatar", claims.Avatar)
		ctx.Next(c)
	}
}

// 从请求中获取Token
func getToken(ctx *app.RequestContext) string {
	// 先从Header中获取
	auth := string(ctx.GetHeader("Authorization"))
	if auth == "" {
		// 尝试从查询参数获取
		auth = ctx.Query("token")
	}

	// 提取Bearer Token
	if strings.HasPrefix(auth, "Bearer ") {
		return auth[7:] // 去掉前缀
	}

	return auth
}

// 解析Token
func parseToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证加密方式
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("无效的Token加密方式: %v", token.Header["alg"])
		}
		return []byte(config.AppConfig.JWT.Secret), nil
	})

	if claims, ok := token.Claims.(*Claims); ok {
		if token.Valid {
			return claims, nil
		}
		if errors.Is(err, jwt.ErrTokenExpired) {
			// 如果是Token过期错误，我们仍然返回claims，以便刷新Token
			return claims, err
		}
	}

	if err != nil {
		return nil, err
	}

	return nil, errors.New("无效的Token")
}

// 生成Token
func GenerateToken(userID uint, username, corpid, avatar string) (string, error) {
	// 设置JWT声明
	claims := Claims{
		UserID:   userID,
		Username: username,
		CorpID:   corpid,
		Avatar:   avatar,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(config.AppConfig.JWT.Expire)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Subject:   fmt.Sprintf("%d", userID),
		},
	}

	// 创建Token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名Token
	tokenString, err := token.SignedString([]byte(config.AppConfig.JWT.Secret))
	if err != nil {
		logger.Error("生成Token失败", logger.Error2(err))
		return "", err
	}

	return tokenString, nil
}

// RefreshToken 刷新Token
func RefreshToken(c context.Context, ctx *app.RequestContext) {
	// 获取旧Token
	oldTokenString := getToken(ctx)
	if oldTokenString == "" {
		response.Unauthorized(ctx, "请先登录")
		return
	}

	// 解析旧Token（即使过期也允许解析）
	claims, err := parseToken(oldTokenString)
	if err != nil {
		if !errors.Is(err, jwt.ErrTokenExpired) {
			response.Unauthorized(ctx, "无效的Token")
			return
		}
	}

	// 生成新Token
	tokenString, err := GenerateToken(claims.UserID, claims.Username, claims.CorpID, claims.Avatar)
	if err != nil {
		response.ServerError(ctx, err)
		return
	}

	// 返回新Token
	response.Success(ctx, map[string]interface{}{
		"token": tokenString,
	})
}
